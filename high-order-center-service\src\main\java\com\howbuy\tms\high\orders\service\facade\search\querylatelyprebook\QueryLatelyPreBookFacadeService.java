/**
 * Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.querylatelyprebook;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.crm.td.querylatelyprebook.CurrentPreInfoResult;
import com.howbuy.tms.common.outerservice.crm.td.querylatelyprebook.QueryCurrentPreInfoContext;
import com.howbuy.tms.common.outerservice.crm.td.querylatelyprebook.QueryLatelyPreBookOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookFacade;
import com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookRequest;
import com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:(查询最近一条可使用预约单)
 * @reason:
 * @date 2018年1月5日 下午6:38:58
 * @since JDK 1.6
 */
@DubboService
@Service("queryLatelyPreBookFacade")
public class QueryLatelyPreBookFacadeService extends AbstractBusiProcess implements QueryLatelyPreBookFacade {
    private static final Logger logger = LogManager.getLogger(QueryLatelyPreBookFacadeService.class);
    @Autowired
    private QueryLatelyPreBookOuterService queryLatelyPreBookOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryAccHboneInfoOuterService queryAccHboneInfoOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookFacade.execute(QueryLatelyPreBookRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryLatelyPreBookFacadeService
     * @apiName execute
     * @apiDescription 查询最近一条可使用预约单
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParam (请求参数) {Array} preType 成单方式 1-纸质成单； 2-电子成单； 3-无纸化交易
     * @apiParam (请求参数) {Array} tradeType 交易类型 交易类型 1-购买 2-追加 3-赎回
     * @apiParam (请求参数) {Array} prebookState 预约状态 -未确认；2-已确认；4-已撤销
     * @apiParam (请求参数) {String} useFlag 使用标识 1-未使用 2-已使用
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=gMuPt&preType=M5&pageSize=2386&disCode=F&txChannel=Uk6&appTm=7mus&useFlag=7ap&fundCode=arrRMsFC3g&prebookState=7tZH&subOutletCode=GHWNV&pageNo=3021&operIp=u&txAcctNo=C&appDt=DmGQKe&dataTrack=wA5z&txCode=n6&tradeType=kO8SipXd&outletCode=tDqLQ
     * @apiSuccess (响应结果) {String} preId 预约单标识，CRM唯一
     * @apiSuccess (响应结果) {String} hboneNo 一帐通号
     * @apiSuccess (响应结果) {String} custNo 客户号
     * @apiSuccess (响应结果) {String} custName 姓名
     * @apiSuccess (响应结果) {String} fundCode 产品代码
     * @apiSuccess (响应结果) {String} fundName 产品名称
     * @apiSuccess (响应结果) {String} tradeType 交易类型  1-购买 2-追加 3-赎回
     * @apiSuccess (响应结果) {String} idType 证件类型
     * @apiSuccess (响应结果) {String} idNo 证件号
     * @apiSuccess (响应结果) {String} prebookState 预约单状态:1-未确认；2-已确认；4-已撤销
     * @apiSuccess (响应结果) {String} nopaperState 无纸化预约单状态      1-未确认；      2-已确认；     3-驳回
     * @apiSuccess (响应结果) {Number} ackAmt 预约金额
     * @apiSuccess (响应结果) {Number} sellVol 预约份额
     * @apiSuccess (响应结果) {Number} fee 手续费
     * @apiSuccess (响应结果) {String} creDt 预约日期
     * @apiSuccess (响应结果) {Number} disCount 预约折扣
     * @apiSuccess (响应结果) {Number} feeRate 费率
     * @apiSuccess (响应结果) {String} doubleNeedFlag
     * @apiSuccess (响应结果) {String} doubleHandleFlag
     * @apiSuccess (响应结果) {Number} doubleHandleDt
     * @apiSuccess (响应结果) {String} firstPreId
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"preId":"IXfDbQ5","fee":9189.254961287535,"description":"pqLbu1","sellVol":860.0277581651117,"doubleNeedFlag":"8uTPoMfO6","totalCount":3061,"idNo":"HGBDonZatL","feeRate":1525.0873386059693,"nopaperState":"8J","returnCode":"f32RS","fundCode":"vk","ackAmt":4011.0912056154034,"pageNo":3531,"firstPreId":"Jj8qw8oG","tradeType":"nH","hboneNo":"bog0i1","custNo":"WK6xppJgK","idType":"bGw91VT","disCount":1555.163996661507,"totalPage":1849,"custName":"Z","doubleHandleDt":340736409079,"creDt":"Q25xlteP","prebookState":"zp","doubleHandleFlag":"QMOH","fundName":"Rr"}
     */
    @Override
    public QueryLatelyPreBookResponse execute(QueryLatelyPreBookRequest request) {

        //根据一帐通号查询客户信息
        QueryAccHboneInfoResult queryAccHboneInfoResult = queryAccHboneInfoOuterService.queryAccHboneInfo(request.getHbOneNo());
        if (queryAccHboneInfoResult == null) {
            throw new BusinessException(ExceptionCodes.HIGH_ORDER_HBONE_ERROR, MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_HBONE_ERROR));
        }

        //0-机构；1-个人
        String invstType = queryAccHboneInfoResult.getUserType();
        QueryLatelyPreBookResponse resp = new QueryLatelyPreBookResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(request.getFundCode());
        if (highProductBaseBean == null) {
            return resp;
        }

        //查询最近一条可使用预约单
        QueryCurrentPreInfoContext queryContext = new QueryCurrentPreInfoContext();
        queryContext.setHboneNo(request.getHbOneNo());
        queryContext.setFundCode(request.getFundCode());
        queryContext.setTradeType(request.getTradeType());
        queryContext.setPreType(request.getPreType());
        queryContext.setPrebookState(request.getPrebookState());
        queryContext.setUseFlag(request.getUseFlag());
        logger.info("QueryLatelyPreBookFacadeService|queryContext:{}", JSON.toJSONString(queryContext));
        CurrentPreInfoResult currentPreInfoResult = queryLatelyPreBookOuterService.getCurrentPreInfo(queryContext);
        logger.info("QueryLatelyPreBookFacadeService|currentPreInfoResult:{}", JSON.toJSONString(currentPreInfoResult));
        if (currentPreInfoResult == null) {
            return resp;
        }

        if ("0004".equals(currentPreInfoResult.getReturnCode())) {
            resp.setPreId(currentPreInfoResult.getPreId());
            resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.HIGH_PREINFO_IS_NULL));
            resp.setReturnCode(ExceptionCodes.HIGH_PREINFO_IS_NULL);
            return resp;
        }

        //复制预约信息
        BeanUtils.copyProperties(currentPreInfoResult, resp, "returnCode", "description");
        //请求时间
        String dateStr = request.getAppDt() + request.getAppTm();
        Date appDtm = DateUtils.formatToDate(dateStr, DateUtils.YYYYMMDDHHMMSS);
        String busiType = convertBusiType(currentPreInfoResult.getTradeType());
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());

        ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoWithDeferPurchaseConfig(request.getHbOneNo(), highProductBaseBean.getFundCode(), highProductBaseBean.getShareClass(), request.getDisCode(), appDtm, busiType);
        String busiCode = getBusiCode(highProductBaseBean, productAppointmentInfoBean, taTradeDt);

        HighProductFeeRateBean highProductFeeRateBean = queryHighProductOuterService.getFundFeeRateByAmt(highProductBaseBean.getFundCode(), busiCode, invstType, highProductBaseBean.getShareClass(), currentPreInfoResult.getAckAmt());

        buildPreBookInfo(resp, currentPreInfoResult, highProductFeeRateBean);
        return resp;

    }

    /**
     * buildPreBookInfo:(构建详细信息)
     *
     * @param resp
     * @param currentPreInfoResult
     * @param highProductFeeRateBean
     * <AUTHOR>
     * @date 2017年10月30日 下午5:47:57
     */
    private void buildPreBookInfo(QueryLatelyPreBookResponse resp, CurrentPreInfoResult currentPreInfoResult, HighProductFeeRateBean highProductFeeRateBean) {
        resp.setAckAmt(currentPreInfoResult.getAckAmt());
        resp.setCreDt(currentPreInfoResult.getCreDt());
        resp.setCustName(currentPreInfoResult.getCustName());
        resp.setCustNo(currentPreInfoResult.getCustNo());
        resp.setDisCount(currentPreInfoResult.getDiscountRate());
        resp.setFundCode(currentPreInfoResult.getFundCode());
        resp.setFundName(currentPreInfoResult.getFundName());
        resp.setHboneNo(currentPreInfoResult.getHboneNo());
        resp.setIdNo(currentPreInfoResult.getIdNo());
        resp.setIdType(currentPreInfoResult.getIdType());
        resp.setNopaperState(currentPreInfoResult.getNopaperState());
        resp.setPrebookState(currentPreInfoResult.getPrebookState());
        resp.setPreId(currentPreInfoResult.getPreId());
        resp.setSellVol(currentPreInfoResult.getSellVol());
        resp.setTradeType(currentPreInfoResult.getTradeType());
        // 双录完成时间
        resp.setDoubleHandleDt(currentPreInfoResult.getDoubleHandleDt());
        // 双录处理标识0-无需双录 1-未双录 2-已双录
        resp.setDoubleHandleFlag(currentPreInfoResult.getDoubleHandleFlag());
        // 是否需要双录标识 0-否 1-是
        resp.setDoubleNeedFlag(currentPreInfoResult.getDoubleNeedFlag());
        // 是否首次实缴标识 0-否 1-是
        resp.setFirstPreId(currentPreInfoResult.getFirstPreId());
        // 折扣类型：1-使用折扣率，2-使用折扣金额
        resp.setDiscountUseType(currentPreInfoResult.getDiscountUseType());
        // 折扣金额
        resp.setDiscountAmt(currentPreInfoResult.getDiscountAmt());
        //手续费
        if (highProductFeeRateBean != null) {
            BigDecimal fee = calFee(currentPreInfoResult.getAckAmt(), currentPreInfoResult.getDiscountRate(), highProductFeeRateBean, currentPreInfoResult.getDiscountUseType(), currentPreInfoResult.getDiscountAmt());
            resp.setFee(fee);
            resp.setFeeRate(highProductFeeRateBean.getFeeRate());
        }

    }

}

