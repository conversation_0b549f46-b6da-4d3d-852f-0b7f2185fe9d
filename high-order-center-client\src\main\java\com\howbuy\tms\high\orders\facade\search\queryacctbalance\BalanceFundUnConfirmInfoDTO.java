package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:基金维度未确认订单信息
 * @Author: yun.lu
 * Date: 2025/9/3 17:47
 */
@Getter
@Setter
public class BalanceFundUnConfirmInfoDTO extends BaseDto {
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 是否全赎,1:是,0:不是
     */
    private String allIsRedeem;
    /**
     * 是否全部上报,1:是,0:不是
     */
    private String allSubmitRedeem;
    /**
     * 最大赎回上报日
     */
    private String maxRedeemSubmitTaDt;

    /**
     * 总待确认金额:申请净金额-储蓄罐预约冻结金额(人民币)
     */
    private BigDecimal totalUnConfirmAmt;

    /**
     * 总待确认金额当前币种
     */
    private BigDecimal totalCurrencyUnConfirmAmt;

    /**
     * 总赎回份额
     */
    private BigDecimal totalSellVol;

    /**
     * 买入待确认数量
     */
    private int buyUnConfirmNum;

    /**
     * 卖出待确认数量
     */
    private int sellUnConfirmNum;

    /**
     * 买入到确认交易
     */
    private List<BuyUnConfirmOrderDTO> buyUnConfirmList;
    /**
     * 卖出待确认交易
     */
    private List<SellUnConfirmOrderDTO> sellUnConfirmList;


}
