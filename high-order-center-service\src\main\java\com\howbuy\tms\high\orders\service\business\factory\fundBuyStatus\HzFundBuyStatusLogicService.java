package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.dto.HzFundAmtLockConfDto;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.FirstBuyFlagEnum;
import com.howbuy.tms.common.enums.database.NotifySubmitFlagEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.CustomerInfoCommand;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:好臻产品可购买状态逻辑处理类
 * @Author: yun.lu
 * Date: 2023/10/26 15:15
 */
@Service
public class HzFundBuyStatusLogicService extends AbstractFundBuyStatusLogicService {
    Logger log = LoggerFactory.getLogger(HzFundBuyStatusLogicService.class);


    @Override
    public String getDisCode() {
        return DisCodeEnum.HZ.getCode();
    }

    @Override
    public boolean isWithe(String hbOneNo, QueryFundBuyStatusParam queryFundBuyStatusParam, HighProductInfoModel highProductInfoBean) {
        HighProductAppointmentInfoModel productAppointmentInfoBean = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductAppointmentInfoModel();
        if (productAppointmentInfoBean == null) {
            log.info("好臻,认申购,没有预约日历,不是白名单");
            return false;
        }
        List<HzFundAmtLockConfDto> hzFundAmtLockCfgDtoList = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHzFundAmtLockConfDtoList();
        if (CollectionUtils.isEmpty(hzFundAmtLockCfgDtoList)) {
            log.info("好臻,认申购,没有好臻金额锁定配置,不是白名单");
            return false;
        }
        List<HzFundAmtLockConfDto> lockCfgDtoList = hzFundAmtLockCfgDtoList.stream().filter(x -> x.getSubscribeAmtLock().equals(YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(lockCfgDtoList)) {
            log.info("好臻,认申购,有锁定的好臻金额锁定配置,是白名单");
            return true;
        }
        log.info("好臻,认申购,没有锁定的好臻金额锁定配置,不是白名单");
        return false;
    }

    @Override
    public FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        // 1.通用购买逻辑
        FundBuyStatusDto fundBuyStatus = super.getFundBuyStatus(queryFundBuyStatusParam);
        // 如果不可以购买,就直接返回
        if (YesOrNoEnum.NO.getCode().equals(fundBuyStatus.getFundBuyStatusEnum().getCanBuy())) {
            return fundBuyStatus;
        }
        // 2.好臻产品可购买逻辑
        return getHzFundBuyStatus(queryFundBuyStatusParam);
    }


    @Override
    public boolean validAgeLimit(HighProductInfoBean highProductInfoBean, String txChannel, CustomerInfoCommand customerInfoCommand) {
        if (customerInfoCommand == null) {
            log.info("没有用户信息,不需要校验年龄");
            return true;
        }
        if (YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getPeDivideCallFlag()) && FirstBuyFlagEnum.NO.getCode().equals(customerInfoCommand.getFirstBuyFlag())) {
            log.info("好臻分次call非首次不需要校验年龄");
            return true;
        }
        // 线上下单买好臻股权,年龄必须<70岁
        if (!TxChannelEnum.COUNTER.getCode().equals(txChannel)) {
            return ProductInfoValidator.validateCustAge(customerInfoCommand.getIdNo(), 0, 70);
        }
        return super.validAgeLimit(highProductInfoBean, txChannel, customerInfoCommand);
    }

    /**
     * 好臻产品可购买逻辑
     *
     * @param queryFundBuyStatusParam 查询入参
     * @return 可买状态
     */
    public FundBuyStatusDto getHzFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        // 1.没有产品信息
        FundBuyStatusDto fundBuyStatus = new FundBuyStatusDto();
        fundBuyStatus.setProductCode(queryFundBuyStatusParam.getFundCode());
        fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_BUY);
        fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
        fundBuyStatus.setMsg(FundBuyStatusEnum.CAN_BUY.getDesc());
        HighProductInfoModel highProductInfoModel = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductInfoModel();
        HighProductInfoBean highProductBaseModel = new HighProductInfoBean();
        BeanUtils.copyProperties(highProductInfoModel, highProductBaseModel);

        // 查询在途
        QueryHighDealOrderParam param = new QueryHighDealOrderParam();
        param.setTxAcctNo(queryFundBuyStatusParam.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(queryFundBuyStatusParam.getFundCode()));
        param.setDisCodeList(Collections.singletonList(queryFundBuyStatusParam.getDisCode()));
        List<HighDealOrderDtlPo> onWayAgentDealDtlList = queryFundBuyStatusParam.getHzOnWayAgentDealDtlList();

        // 2.年龄校验
        CustomerInfoCommand customerInfoCommand = null;
        QueryCustInfoResult customerInfo = queryFundBuyStatusParam.getCustInfo();
        if (customerInfo != null) {
            customerInfoCommand = new CustomerInfoCommand();
            BeanUtils.copyProperties(customerInfo, customerInfoCommand);
        } else {
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,没有用户信息,不需要校验后续逻辑,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return fundBuyStatus;
        }
        String firstBuyFlag = CollectionUtils.isEmpty(queryFundBuyStatusParam.getHzConfirmBalanceBaseInfoList()) ? FirstBuyFlagEnum.YES.getCode() : FirstBuyFlagEnum.NO.getCode();
        customerInfoCommand.setFirstBuyFlag(firstBuyFlag);
        if (!validAgeLimit(highProductBaseModel, queryFundBuyStatusParam.getTxChannel(), customerInfoCommand)) {
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.LIMIT_AGE.getCode());
            fundBuyStatus.setMsg("年龄校验不通过");
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-年龄校验不通过,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return fundBuyStatus;
        }

        // 查询好臻金额锁定配置
        List<HzFundAmtLockConfDto> hzFundAmtLockCfgDtoList = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHzFundAmtLockConfDtoList();
        // 3.分次call产品必须有预约日历
        if (YesOrNoEnum.YES.getCode().equals(highProductBaseModel.getPeDivideCallFlag())) {
            peDivideCallOrderCheck(queryFundBuyStatusParam, fundBuyStatus, onWayAgentDealDtlList, hzFundAmtLockCfgDtoList);
        } else {
            unPeDivideCallOrderCheck(queryFundBuyStatusParam, fundBuyStatus, onWayAgentDealDtlList, hzFundAmtLockCfgDtoList);
        }
        log.info("HzFundBuyStatusLogicService-getFundBuyStatus,好臻购买状态结果,queryFundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
        return fundBuyStatus;
    }

    /**
     * 非分次call购买状态校验
     *
     * @param queryFundBuyStatusParam
     * @param fundBuyStatus
     * @param onWayAgentDealDtlList
     * @param hzFundAmtLockCfgDtoList
     */
    private void unPeDivideCallOrderCheck(QueryFundBuyStatusParam queryFundBuyStatusParam, FundBuyStatusDto fundBuyStatus, List<HighDealOrderDtlPo> onWayAgentDealDtlList, List<HzFundAmtLockConfDto> hzFundAmtLockCfgDtoList) {
        // 1.有确认持仓,非分次call不能买
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = queryFundBuyStatusParam.getHzConfirmBalanceBaseInfoList();
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList)) {
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.OTHERS.getCode());
            fundBuyStatus.setMsg("好臻非分次call,有确认持仓不可以购买");
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,好臻非分次call,有确认持仓不可以购买,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        // 2.没有在途,可以买
        if (CollectionUtils.isEmpty(onWayAgentDealDtlList)) {
            log.info("HzFundBuyStatusLogicService-unPeDivideCallOrderCheck,获取产品购买状态-非分次call没有在途,可以买,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        // 接下来就是有在途,修改的场景
        // 3.没有好臻金额锁定配置,可以修改
        if (CollectionUtils.isEmpty(hzFundAmtLockCfgDtoList)) {
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_MODIFY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
            fundBuyStatus.setMsg("好臻非分次call,有在途,没有好臻金额锁定配置,可以修改");
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,好臻非分次call,有在途,没有好臻金额锁定配置,可以修改,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        // 4.接下来就是有好臻金额锁定配置,有在途的场景
        List<HzFundAmtLockConfDto> unUsedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList());
        List<HzFundAmtLockConfDto> lockedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getSubscribeAmtLock())).collect(Collectors.toList());
        // 4.1.有可用的好臻金额锁定配置
        if (CollectionUtils.isNotEmpty(unUsedCfgList)) {
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_MODIFY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
            fundBuyStatus.setMsg("好臻非分次call,有在途,有可用的好臻金额锁定配置,可以修改");
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,好臻非分次call,有在途,有可用的好臻金额锁定配置,可以修改,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        // 4.2.没有可用好臻金额锁定配置
        // 4.2.1.好臻金额锁定配置都是锁定
        if (CollectionUtils.isNotEmpty(lockedCfgList)) {
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,非分次call产品,有在途,无可用好臻金额配置,且认缴金额锁定,不可以修改,queryFundBuyStatusParam={}", JSON.toJSONString(queryFundBuyStatusParam));
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_MODIFY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.OTHERS.getCode());
            fundBuyStatus.setMsg("非分次call产品,有在途,无可用好臻金额配置,且认缴金额锁定,不可以修改");
        } else {
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,非分次call产品,有在途,无可用好臻金额配置,且认缴金额不锁定,可以修改,queryFundBuyStatusParam={}", JSON.toJSONString(queryFundBuyStatusParam));
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_MODIFY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
            fundBuyStatus.setMsg("非分次call产品,有在途,无可用好臻金额配置,且认缴金额不锁定,可以修改");
        }
    }

    /**
     * 分次call订单校验
     *
     * @param queryFundBuyStatusParam
     * @param fundBuyStatus
     * @param onWayAgentDealDtlList
     * @param hzFundAmtLockCfgDtoList
     */
    private void peDivideCallOrderCheck(QueryFundBuyStatusParam queryFundBuyStatusParam,
                                        FundBuyStatusDto fundBuyStatus, List<HighDealOrderDtlPo> onWayAgentDealDtlList, List<HzFundAmtLockConfDto> hzFundAmtLockCfgDtoList) {
        if (CollectionUtils.isEmpty(onWayAgentDealDtlList)) {
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-分次call没有在途,可以买,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        HighProductAppointmentInfoModel productAppointmentInfoBean = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductAppointmentInfoModel();
        if (productAppointmentInfoBean == null) {
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.LIMIT_AGE.getCode());
            fundBuyStatus.setMsg("分次call产品没有预约日历");
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-年龄校验不通过,fundBuyStatusParam={},fundBuyStatus={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatus));
            return;
        }
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = onWayAgentDealDtlList.stream().filter(x -> x.getNotifySubmitFlag().equals(NotifySubmitFlagEnum.HAS_BEEN_NOTIFY.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(highDealOrderDtlPoList)) {
            log.info("HzCreateOrderCheckLogicProcess-validateOnWayOrder,该客户该产品该预约期内如果有申请成功的认申购订单，订单必须是未上报,否则不允许下单,highDealOrderDtlPoList={}", JSON.toJSONString(highDealOrderDtlPoList));
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.OTHERS.getCode());
            fundBuyStatus.setMsg("有非未上报的,同预约日历的在途订单");
            return;
        }
        // 非首次,没有可用配置 不可以改
        List<HzFundAmtLockConfDto> unUsedConfList = CollectionUtils.isNotEmpty(hzFundAmtLockCfgDtoList) ? hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList()) : new ArrayList<>();
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = queryFundBuyStatusParam.getHzConfirmBalanceBaseInfoList();
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList) && CollectionUtils.isEmpty(unUsedConfList)) {
            log.info("HzFundBuyStatusLogicService-getFundBuyStatus,分次call,非首次,没有可用好臻金额锁定配置,不可以修改,queryFundBuyStatusParam={}", JSON.toJSONString(queryFundBuyStatusParam));
            fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_MODIFY);
            fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.OTHERS.getCode());
            fundBuyStatus.setMsg("分次call,非首次,没有可用好臻金额锁定配置,不可以修改");
            return;
        }
        // 6.其他情况都是可以修改
        fundBuyStatus.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_MODIFY);
        fundBuyStatus.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
        fundBuyStatus.setMsg("可以修改");
    }
}
