package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.interlayer.common.utils.DateUtils;
import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.FractionatedCallOuterService;
import com.howbuy.tms.common.outerservice.crm.td.fractionatedcall.bean.FractionatedCallBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.simu.comprehensive.QueryComprehensiveOuterService;
import com.howbuy.tms.common.outerservice.simu.comprehensive.bean.RmbhlzjjBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.common.utils.ReflectUtils;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkRequest;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.business.calvaluedate.ProductValueDateCalService;
import com.howbuy.tms.high.orders.service.cacheservice.alarm.HighBalanceAlarmService;
import com.howbuy.tms.high.orders.service.common.utils.OpsBusinessMonitor;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.facade.search.querycommon.BigUtil;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import com.howbuy.tms.high.orders.service.task.CheckBalanceAlarmTask;
import com.howbuy.tms.high.orders.service.task.HowBuyRunTaskUil;
import com.howbuy.tms.high.orders.service.task.QueryStructProductNavTask;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Description:查询非海外持仓
 * @Author: yun.lu
 * Date: 2025/8/29 11:25
 */
@DubboService
@Service("queryAcctBalanceWithoutHkFacade")
@RefreshScope
public class QueryAcctBalanceWithoutHkFacadeService implements QueryAcctBalanceWithoutHkFacade {
    private static final Logger log = LogManager.getLogger(QueryAcctBalanceWithoutHkFacadeService.class);
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryAssetService queryAssetService;
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;
    @Autowired
    private FractionatedCallOuterService fractionatedCallOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private ProductValueDateCalService productValueDateCalService;
    @Autowired
    private QueryComprehensiveOuterService queryComprehensiveOuterService;
    @Autowired
    private HighBalanceAlarmService highBalanceAlarmService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Value("${noAlarmFundCodes}")
    private String noAlarmFundCodes;
    /**
     * 千禧年产品列表
     */
    @Value("${qianXiProductsCfg}")
    private String qxProductsCfg;

    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceWithoutHkRequest request) {
        log.info("查询非海外持仓-start,request={}", JSON.toJSONString(request));
        QueryAcctBalanceResponse response = new QueryAcctBalanceResponse();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setDisCode(request.getDisCode());
        response.setDisCodeList(request.getDisCodeList());
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 1.查询确认持仓
        QueryBalanceParam queryBalanceParam = new QueryBalanceParam();
        queryBalanceParam.setHboneNo(request.getHbOneNo());
        queryBalanceParam.setFundCode(request.getProductCode());
        queryBalanceParam.setTxAcctNo(request.getTxAcctNo());
        queryBalanceParam.setDisCodeList(request.getDisCodeList());
        List<CustConfirmBalanceDto> confirmBalanceList = acctBalanceBaseInfoService.queryCustConfirmBalance(queryBalanceParam);
        // 2.查询买入待确认交易
        List<BuyUnConfirmUnHkOrderBean> buyUnConfirmUnHkOrderBeanList = acctBalanceBaseInfoService.queryBuyUnConfirmUnHkOrderList(queryBalanceParam);
        // 3.查询卖出待确认交易
        List<SellUnConfirmUnHkOrderBean> sellUnConfirmUnHkOrderBeanList = acctBalanceBaseInfoService.querySellUnConfirmUnHkOrderList(queryBalanceParam);
        // 4.查询待资金到账的
        List<RefundDealOrderInfo> refundDealOrderInfoList = acctBalanceBaseInfoService.queryRefundAmtDealOrderInfo(queryBalanceParam);
        // 5.遍历持仓列表,按照fundCode维度,将买入待确认,卖出待确认,贷资金到账,匹配加入持仓列表里
        List<BalanceBean> balanceList = acctBalanceBaseInfoService.buildAndSetBalanceWithRefundAndUnConfirmInfo(confirmBalanceList, buyUnConfirmUnHkOrderBeanList, sellUnConfirmUnHkOrderBeanList, refundDealOrderInfoList);
        // 6.授权过滤
        filterBalanceInfoByAuth(response, balanceList, request);
        // 7.查询清盘中产品
        List<String> crisisFundList = acctBalanceBaseInfoService.getCrisisFundList();
        // 8.产品类型,市值,收益信息添加
        setAssertAndMarketInfo(balanceList, queryBalanceParam, crisisFundList);
        response.setBalanceList(balanceList);
        // 7.通过产品代码批量查询特殊产品指标控制配置项
        Map<String, List<HighProductFieldControlBean>> fieldMap = getProductFieldControl(balanceList);
        // 8.数据合并
        totalProcess(response, balanceList, fieldMap, request);
        // 9.特殊产品指标控制处理（字段小数位,根据配置表，将对应属性置空）
        dealProductFieldControl(balanceList, fieldMap);
        // 10.检查告警
        checkAlarm(queryBalanceParam, balanceList, crisisFundList);
        log.info("查询非海外持仓-end,response={}", JSON.toJSONString(response));
        return response;
    }

    /**
     * 检查告警
     */
    private void checkAlarm(QueryBalanceParam queryBalanceParam, List<BalanceBean> balanceList, List<String> crisisFundList) {
        try {
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(balanceList)) {
                List<CheckBalanceAlarmTask> taskList = new ArrayList<>();
                for (BalanceBean balanceBean : balanceList) {
                    taskList.add(new CheckBalanceAlarmTask(balanceBean, highBalanceAlarmService, highDealOrderDtlRepository, queryBalanceParam.getTxAcctNo(), queryBalanceParam.getHboneNo(), crisisFundList, cmCusttradeDirectRepository, noAlarmFundCodes));
                }
                howBuyRunTaskUil.runTask(taskList);
            }
        } catch (Exception e) {
            log.warn("检查告警异常:", e);
        }
    }

    /**
     * 特殊产品指标控制处理（字段小数位,根据配置表，将对应属性置空）
     */
    private void dealProductFieldControl(List<BalanceBean> balanceList, Map<String, List<HighProductFieldControlBean>> fieldMap) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }
        for (BalanceBean balanceBean : balanceList) {
            // 份额格式化
            balanceBean.setBalanceVol(MoneyUtil.formatMoney(balanceBean.getBalanceVol(), 2));
            List<HighProductFieldControlBean> fieldList = fieldMap.get(balanceBean.getProductCode());
            if (CollectionUtil.isNotEmpty(fieldList)) {
                for (HighProductFieldControlBean field : fieldList) {
                    if (StringUtil.isEmpty(field.getField())) {
                        log.warn("特殊产品指标控制配置信息异常，field为空，配置信息:{}", JSON.toJSONString(field));
                        continue;
                    }
                    try {
                        ReflectUtils.setValue(balanceBean, field.getField(), null);
                        // 是否控制表人为置空
                        ReflectUtils.setValue(balanceBean, field.getField() + "Ctl", "1");
                    } catch (Exception e) {
                        log.warn("特殊产品指标置空异常，产品:{}，指标:{}，异常信息:{}", field.getFundCode(), field.getField(), e);
                        OpsBusinessMonitor.warn(JSON.toJSONString(getMsgMap(field.getFundCode(), field.getField())), OpsBusinessMonitor.WARN);
                    }
                }
            }
        }
    }

    /**
     * 异常指标控制
     */
    private Map<String, String> getMsgMap(String fundCode, String field) {
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("time", DateUtils.formatToString(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS_SSS));
        String msg = HighBalanceAlarmEnum.PRODUCT_5.getName() + "，指标：" + field;
        msgMap.put("msg", msg);
        msgMap.put("fundCode", fundCode);
        msgMap.put("txAcctNo", null);
        msgMap.put("type", HighBalanceAlarmEnum.PRODUCT_5.getType());
        return msgMap;
    }


    /**
     * 处理小数位
     */
    private void processFiledControl(QueryAcctBalanceResponse response) {
        List<BalanceBean> balanceList = response.getBalanceList();
        if (!CollectionUtils.isEmpty(balanceList)) {
            for (BalanceBean balanceBean : balanceList) {
                // 份额格式化
                balanceBean.setBalanceVol(MoneyUtil.formatMoney(balanceBean.getBalanceVol(), 2));
            }
        }

    }

    /**
     * 资产汇总处理
     *
     * @param response    持仓响应
     * @param balanceList 持仓列表
     * @param fieldMap    特殊产品指标控制配置项
     * @param request     请求参数
     */
    private void totalProcess(QueryAcctBalanceResponse response, List<BalanceBean> balanceList,
                              Map<String, List<HighProductFieldControlBean>> fieldMap, QueryAcctBalanceWithoutHkRequest request) {
        //总收益
        BigDecimal totalCurrentAsset = BigDecimal.ZERO;
        // 总市值
        BigDecimal totalMarketValue = BigDecimal.ZERO;
        // 总回款金额（人民币）
        BigDecimal totalCashCollection = BigDecimal.ZERO;
        // 总收益的计算状态
        String totalIncomCalStat = IncomeCalStatEnum.FINISHED.getCode();
        // 累计收益(人民币)
        BigDecimal totalAccumIncome = BigDecimal.ZERO;
        // 累计收益(当前币种)
        BigDecimal totalCurrencyAccumIncome = BigDecimal.ZERO;
        Set<String> processedAssetProductCodeSet = new HashSet<>();
        Set<String> processedIncomeProductCodeSet = new HashSet<>();
        for (BalanceBean balanceBean : balanceList) {
            // 在控制表,就不需要汇总
            boolean needExclude = !CollectionUtils.isEmpty(fieldMap.get(balanceBean.getProductCode()));
            // 总市值
            if (balanceBean.getMarketValue() != null && !needExclude && balanceBean.isBalance()) {
                totalMarketValue = totalMarketValue.add(balanceBean.getMarketValue());
            }
            // 总收益
            if (balanceBean.getCurrentAsset() != null && acctBalanceBaseInfoService.canNotCountAssert(balanceBean, fieldMap)) {
                // 收益汇总，固收每个产品只汇总一次
                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType()) && processedAssetProductCodeSet.contains(balanceBean.getProductCode())) {
                    continue;
                }
                totalCurrentAsset = totalCurrentAsset.add(balanceBean.getCurrentAsset());
                processedAssetProductCodeSet.add(balanceBean.getProductCode());
            }
            // 累计收益
            if (balanceBean.getAccumIncome() != null && acctBalanceBaseInfoService.canNotCountAssert(balanceBean, fieldMap)) {
                // 累计收益，固收每个产品只汇总一次
                if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType()) && processedIncomeProductCodeSet.contains(balanceBean.getProductCode())) {
                    continue;
                }
                totalAccumIncome = totalAccumIncome.add(balanceBean.getAccumIncomeRmb());
                totalCurrencyAccumIncome = totalCurrencyAccumIncome.add(balanceBean.getAccumIncome());
                processedIncomeProductCodeSet.add(balanceBean.getProductCode());
            }
            // 总回款
            if (balanceBean.getCashCollection() != null) {
                totalCashCollection = totalCashCollection.add(balanceBean.getCashCollection());
            }

            // 收益状态,存在一笔持仓产品的收益是计算中，则总收益的收益状态是计算中
            if (IncomeCalStatEnum.PROCESSING.getCode().equals(balanceBean.getIncomeCalStat()) && IncomeCalStatEnum.FINISHED.getCode().equals(totalIncomCalStat)) {
                totalIncomCalStat = IncomeCalStatEnum.PROCESSING.getCode();
            }
        }
        response.setTotalIncomCalStat(totalIncomCalStat);
        // 四舍五入保留2位小数
        response.setTotalMarketValue(BigUtil.formatMoney(totalMarketValue, 2));
        // 四舍五入保留2位小数
        response.setTotalCurrentAsset(BigUtil.formatMoney(totalCurrentAsset, 2));
        // 四舍五入保留2位小数
        response.setTotalAccumIncome(BigUtil.formatMoney(totalAccumIncome, 2));
        // 四舍五入保留2位小数
        response.setTotalCurrencyAccumIncome(BigUtil.formatMoney(totalCurrencyAccumIncome, 2));
        // 总回款
        response.setTotalCashCollection(MoneyUtil.formatMoney(totalCashCollection, 2));


    }



    /**
     * 设置持仓资产收益市值信息
     *
     * @param balanceList       持仓列表
     * @param queryBalanceParam 查询参数
     */
    private void setAssertAndMarketInfo(List<BalanceBean> balanceList, QueryBalanceParam queryBalanceParam, List<String> crisisFundList) {
        // 1.没有持仓信息,就不处理
        if (CollectionUtils.isEmpty(balanceList)) {
            log.info("setAssertAndMarketInfo-没有持仓数据");
            return;
        }
        // 2.查询产品db信息
        List<String> fundCodeList = balanceList.stream().map(BalanceBean::getProductCode).distinct().collect(Collectors.toList());
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        // 3.查询拆单基金列表
        List<String> splitFundList = queryHighProductOuterService.queryRedeemSplitFund(null, null);
        // 4.千禧年产品
        List<String> qxProductIdList = Arrays.asList(qxProductsCfg.split(","));
        // 5.遍历确认持仓
        // 需要计算收益的产品
        Set<String> incomeFundCodeSet = new HashSet<>();
        // 股权产品
        Set<String> guQuanFundCodeSet = new HashSet<>();
        // 代销分次call产品
        Set<String> agentFractionateCallFundCodeSet = new HashSet<>();
        // 直销分次call产品
        Set<String> directFractionateCallFundCodeSet = new HashSet<>();
        // 类固定收益
        Set<String> fixedIncomeFundCodeSet = new HashSet<>();
        // 千禧年产品
        Set<String> qxFundCodeSet = new HashSet<>();
        // 净值产品
        Set<String> navTypeFundCodeSet = new HashSet<>();
        for (BalanceBean balanceBean : balanceList) {
            HighProductDBInfoBean highProductDBInfoBean = highProductDbInfoBeanMap.get(balanceBean.getProductCode());
            if (highProductDBInfoBean == null) {
                log.info("根据产品编码查询不到产品db信息,fundCode={}", balanceBean.getProductCode());
                continue;
            }
            setBalanceByProductInfo(splitFundList, highProductDBInfoBean, crisisFundList, balanceBean);
            addFundSet(balanceBean, highProductDBInfoBean, qxFundCodeSet,
                    incomeFundCodeSet, guQuanFundCodeSet, agentFractionateCallFundCodeSet,
                    directFractionateCallFundCodeSet, fixedIncomeFundCodeSet, navTypeFundCodeSet, qxProductIdList);
        }
        // 6.按照是否分期成立,查询产品净值信息,分期成立的,用子基金代码,结果key是子基金代码,否则是母基金代码
        Map<String, HighProductNavBean> navBeanMap = buildAndSetNavInfo(balanceList, incomeFundCodeSet);
        // 7.批量查询产品收益,分期成立的,用子基金代码查询,结果key是子基金代码,否则是母基金代码
        List<String> subProductCodeList = balanceList.stream().map(BalanceBean::getSubProductCode).filter(subProductCode -> !StringUtils.isBlank(subProductCode)).distinct().collect(Collectors.toList());
        List<String> mainProductCodeList = balanceList.stream().filter(x -> StringUtils.isBlank(x.getSubProductCode())).map(BalanceBean::getProductCode).distinct().collect(Collectors.toList());
        List<String> allProductCodeList = new ArrayList<>();
        allProductCodeList.addAll(subProductCodeList);
        allProductCodeList.addAll(mainProductCodeList);
        Map<String, HighFundAssetIncomeDomain> currentAssetMap = queryAssetService.getCurrentAssetMap(allProductCodeList, queryBalanceParam.getHboneNo(), queryBalanceParam.getDisCodeList());
        // 8.批量获取基金分红信息map,入参是母基金代码,结果key是母基金代码
        List<String> allMainFundCodeList = balanceList.stream().map(BalanceBean::getSubProductCode).filter(subProductCode -> !StringUtils.isBlank(subProductCode)).distinct().collect(Collectors.toList());
        Map<String, HighProductNavDivBean> fundNavDivMap = getNavDivMap(new ArrayList<>(allMainFundCodeList));
        // 9.获取当前账户股权分次call产品实缴金额,入参区分直销基金代码,直销基金代码,返回结果key是母基金代码
        Map<String, BigDecimal> paidInAmtMap = getPaidInAmtMap(queryBalanceParam, agentFractionateCallFundCodeSet, directFractionateCallFundCodeSet);
        // 10.查询净值型产品的确认的认申购订单,用母基金代码
        Map<String, List<AckDealOrderInfo>> ackDealMap = getAckDealDtlMap(queryBalanceParam.getTxAcctNo(), queryBalanceParam.getHboneNo(), navTypeFundCodeSet);
        // 11.查询固收类产品起息日相关信息,用母基金代码查询
        Map<String, List<HighProductValueDateBean>> valueDateMap = productValueDateCalService.getValueDateMap(fixedIncomeFundCodeSet);
        // 12查询产品净购买金额
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setHbOneNo(queryBalanceParam.getHboneNo());
        queryAcctBalanceBaseParam.setTxAcctNo(queryBalanceParam.getTxAcctNo());
        queryAcctBalanceBaseParam.setFundCodeList(new ArrayList<>(guQuanFundCodeSet));
        Map<String, OwnershipOrderDto> ownershipDtoMap = acctBalanceBaseInfoService.getOwnershipOrderInfoMap(queryAcctBalanceBaseParam);
        //查询千禧年信息
        Map<String, BigDecimal> netBuyAmtQxMap = getQxFundNetBuyAmtMap(queryAcctBalanceBaseParam.getHbOneNo(), new ArrayList<>(qxFundCodeSet));
        // 13.按照基金维度设置市值等字段
        for (BalanceBean balanceBean : balanceList) {
            // 产品汇率人民币中间价
            BigDecimal rmbZjj = getRMBZjj(balanceBean.getCurrency());
            // 查询fundCode,分期成立的,部分数据需要用子基金匹配
            String queryProductCode = !StringUtils.isBlank(balanceBean.getSubProductCode()) ? balanceBean.getSubProductCode() : balanceBean.getProductCode();
            HighFundAssetIncomeDomain currentAssetDto = currentAssetMap.get(queryProductCode);
            // 股权产品需要设置成本价
            setNetBuyAmtInfo(balanceBean, ownershipDtoMap, rmbZjj);
            // 清盘中的不计算市值与收益
            if (!YesOrNoEnum.YES.getCode().equals(balanceBean.getCrisisFlag())) {
                // 设置未清盘持仓信息
                setBalanceMarketAndAssetInfo(queryBalanceParam, navBeanMap, fundNavDivMap, ackDealMap, balanceBean, rmbZjj, queryProductCode, currentAssetDto);
            }
            // 收益计算状态
            balanceBean.setIncomeCalStat(acctBalanceBaseInfoService.getIncomeCalStatus(balanceBean, crisisFundList));
            // 分次call实缴金额设置
            balanceBean.setPaidInAmt(MoneyUtil.formatMoney(paidInAmtMap.get(balanceBean.getProductCode()), 2));
            // 部分产品类型不管是否清盘,都需设置收益回款信息
            acctBalanceBaseInfoService.setAssertWithOutCrisis(balanceBean, currentAssetDto);
            // 千禧年产品适配
            acctBalanceBaseInfoService.setQxFundInfo(netBuyAmtQxMap, paidInAmtMap, new ArrayList<>(qxFundCodeSet), balanceBean, rmbZjj);
            // 计算固收类产品的起息日
            acctBalanceBaseInfoService.calFixedIncomeValueDate(balanceBean, valueDateMap);
        }
    }

    /**
     * 根据产品信息设置持仓信息
     */
    private void setBalanceByProductInfo(List<String> splitFundList, HighProductDBInfoBean productBean, List<String> crisisFundList, BalanceBean bean) {
        bean.setProductName(productBean.getFundAttr());
        bean.setProductType(productBean.getFundType());
        bean.setProductSubType(productBean.getFundSubType());
        bean.setCurrency(productBean.getCurrency());
        bean.setHkSaleFlag(productBean.getHkSaleFlag());
        // 股权产品存续期限描述
        bean.setFundCXQXStr(productBean.getFundCXQXStr());
        bean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        bean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        // 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
        bean.setStandardFixedIncomeFlag(productBean.getStandardFixedIncomeFlag());
        // 基准类型（0-业绩比较基准 1-业绩报酬计提基准
        bean.setBenchmarkType(productBean.getBenchmarkType());
        bean.setHwSaleFlag(productBean.getHwSaleFlag());
        bean.setOneStepType(productBean.getOneStepType());
        bean.setTwoStepType(productBean.getTwoStepType());
        bean.setSecondStepType(productBean.getSecondStepType());
        // 产品销售类型
        bean.setProductSaleType(productBean.getProductSaleType());
        bean.setSfhwcxg(productBean.getSfhwcxg());

        // NA产品收费类型
        bean.setNaProductFeeType(productBean.getNaProductFeeType());
        // 是否是净值型产品
        bean.setIsNavProduct(acctBalanceBaseInfoService.isNavTypeFund(productBean.getStandardFixedIncomeFlag(), productBean.getFundSubType(), productBean.getFundCode()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 是否拆单产品
        if (splitFundList.contains(bean.getProductCode())) {
            bean.setStageFlag(YesOrNoEnum.YES.getCode());
        }
        // 净值披露方式(1-净值,2-份额收益)
        bean.setNavDisclosureType(productBean.getNavDisclosureType());
        // 清盘中赋值
        if (crisisFundList.contains(bean.getProductCode())) {
            bean.setCrisisFlag(YesOrNoEnum.YES.getCode());
        }
        bean.setCpqxsm(productBean.getCpqxsm());
        bean.setSxz(productBean.getNaProductFeeType());
    }

    /**
     * 通过产品代码批量查询特殊产品指标控制配置项
     *
     * @param balanceList 持仓信息
     * @return 特殊产品指标控制配置项
     */
    private Map<String, List<HighProductFieldControlBean>> getProductFieldControl(List<BalanceBean> balanceList) {
        // 通过产品代码批量查询特殊产品指标控制配置项（用于总市值/总收益产品剔除、接口返回值特殊产品指标置空） 特殊产品指标控制需求20221124
        Map<String, List<HighProductFieldControlBean>> fieldMap = new HashMap<>();
        // 获取产品代码list
        List<String> productCodeList = balanceList.stream().map(BalanceBean::getProductCode).collect(Collectors.toList());
        fieldMap = queryHighProductOuterService.queryProductFieldControlByFundCodeForMap(productCodeList);
        return fieldMap;
    }

    /**
     * 如果未授权,就需要过滤掉,好臻/好买香港的产品
     *
     * @param response    返回实体
     * @param balanceList 所有的持仓产品信息
     */
    private void filterBalanceInfoByAuth(QueryAcctBalanceResponse response, List<BalanceBean> balanceList, QueryAcctBalanceWithoutHkRequest request) {
        // 如果是未授权
        Iterator<BalanceBean> iterator = balanceList.iterator();
        while (iterator.hasNext()) {
            BalanceBean balanceBean = iterator.next();
            // 过滤香港产品,并在返回实体标注该用户有香港产品
            if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
                response.setHasHKProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
                    iterator.remove();
                    continue;
                }
            }
            // 过滤好臻产品,并在返回实体中标注该用户有好臻产品
            if (DisCodeEnum.HZ.getCode().equals(balanceBean.getDisCode())) {
                response.setHasHZProduct(YesOrNoEnum.YES.getCode());
                if (YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
                    iterator.remove();
                }
            }
        }
    }


    /**
     * 设置市值以及收益信息
     */
    private void setBalanceMarketAndAssetInfo(QueryBalanceParam queryConfirmBalanceParam,
                                              Map<String, HighProductNavBean> navBeanMap, Map<String, HighProductNavDivBean> fundNavDivMap,
                                              Map<String, List<AckDealOrderInfo>> ackDealMap, BalanceBean balanceBean,
                                              BigDecimal rmbZjj, String queryProductCode, HighFundAssetIncomeDomain currentAssetDto) {
        // 净值
        HighProductNavBean navBean = navBeanMap.get(queryProductCode);
        // 最新确认订单
        List<AckDealOrderInfo> ackList = ackDealMap.get(balanceBean.getProductCode());
        // 设置市值与净值信息
        setMarketValueAndNavInfo(queryConfirmBalanceParam.getHboneNo(), balanceBean, navBean, ackList, rmbZjj);
        // 展示总资产
        acctBalanceBaseInfoService.setFundTotalAssert(balanceBean);
        // 处理净值分红状态
        acctBalanceBaseInfoService.processNavDivFlag(balanceBean, navBean, queryConfirmBalanceParam.getTxAcctNo(), queryConfirmBalanceParam.getHboneNo(), fundNavDivMap);
        // 收益信息
        setBalanceAssetInfo(balanceBean, rmbZjj, currentAssetDto);
        // 现金管理类产品查询七日年化和收益日期
        acctBalanceBaseInfoService.setYieldIncomeInfo(balanceBean);
    }

    /**
     * getQxFundNetBuyAmtMap:(千禧年产品查询累计购买净金额直销，仅查询120、130、122)
     *
     * @param hboneNo      一账通号
     * @param fundCodeList 基金代码
     */
    private Map<String, BigDecimal> getQxFundNetBuyAmtMap(String hboneNo, List<String> fundCodeList) {
        Map<String, BigDecimal> netBuyMap = new HashMap<String, BigDecimal>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(fundCodeList)) {
            return netBuyMap;
        }

        // 查询产品累计净购买金额
        List<BalanceVo> netBuyList = custBooksRepository.selectNetBuyAmountQX(hboneNo, fundCodeList);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(netBuyList)) {
            for (BalanceVo netBuyVo : netBuyList) {
                netBuyMap.put(netBuyVo.getProductCode(), netBuyVo.getNetBuyAmount());
            }
        }
        return netBuyMap;
    }

    /**
     * 设置成本价信息
     *
     * @param balanceBean     持仓信息
     * @param ownershipDtoMap 股权订单信息
     */
    private void setNetBuyAmtInfo(BalanceBean balanceBean, Map<String, OwnershipOrderDto> ownershipDtoMap, BigDecimal rmbZjj) {
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
            OwnershipOrderDto ownershipOrderDto = ownershipDtoMap.get(balanceBean.getProductCode());
            if (ownershipOrderDto != null) {
                // 股权当前投资成本/实缴金额: 净购买金额
                processNetBuyAmount(balanceBean, ownershipOrderDto.getNetBuyAmt(), rmbZjj);
                // 股权产品持仓成本
                balanceBean.setBalanceCostCurrency(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                balanceBean.setBalanceCost(MoneyUtil.formatMoney(ownershipOrderDto.getNetBuyAmt(), 2));
                // 股权产品转让标识
                balanceBean.setOwnershipTransferIdentity(ownershipOrderDto.getTransferIdentity());
            }
        }
    }


    /**
     * 净购买金额处理(币种)
     */
    private void processNetBuyAmount(BalanceBean balanceBean, BigDecimal netBuyAmt, BigDecimal rmbZjj) {
        if (netBuyAmt == null) {
            return;
        }

        // 外币市值处理
        balanceBean.setCurrencyNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        if (CurrencyEnum.RMB.getCode().equals(balanceBean.getCurrency())) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt, 2));
        } else if (rmbZjj != null) {
            balanceBean.setNetBuyAmount(MoneyUtil.formatMoney(netBuyAmt.multiply(rmbZjj), 2));
        }
    }

    /**
     * 设置收益信息
     *
     * @param balanceBean     持仓信息
     * @param rmbZjj          人民币中间价
     * @param currentAssetDto 收益信息
     */
    private void setBalanceAssetInfo(BalanceBean balanceBean, BigDecimal rmbZjj, HighFundAssetIncomeDomain currentAssetDto) {
        if (!ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            acctBalanceBaseInfoService.setAgentBalanceAssetInfo(balanceBean, currentAssetDto);
        } else {
            acctBalanceBaseInfoService.setDirectBalanceAssetInfo(balanceBean, rmbZjj, new JjxswConfigModel(), currentAssetDto);
        }
    }


    /**
     * 获取汇率人民币中间价
     *
     * @param currency 当前币种
     * @return 汇率
     */
    public BigDecimal getRMBZjj(String currency) {
        if (CurrencyEnum.RMB.getCode().equals(currency)) {
            return null;
        }

        RmbhlzjjBean rmbhlzjjBean = queryComprehensiveOuterService.getRmbhlzjj(null, currency);
        if (rmbhlzjjBean == null || rmbhlzjjBean.getZjj() == null) {
            log.error("QueryDirectBalanceCacheService|getBalanceList|rmbhlzjjBean is null, currency:{}, rmbhlzjjBean:{}",
                    currency, JSON.toJSONString(rmbhlzjjBean));
            return null;
        }

        return BigDecimal.valueOf(rmbhlzjjBean.getZjj());
    }

    /**
     * 设置市值与净值信息
     */
    private void setMarketValueAndNavInfo(String hboneNo, BalanceBean balanceBean, HighProductNavBean navBean, List<AckDealOrderInfo> ackList, BigDecimal rmbZJJ) {
        if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            acctBalanceBaseInfoService.setDirectMarketValueAndNavInfo(hboneNo, balanceBean, rmbZJJ, ackList, new JjxswConfigModel(), navBean);
        } else {
            acctBalanceBaseInfoService.setAgentMarketValueAndNavInfo(balanceBean, navBean, ackList);
        }
    }

    /**
     * 设置将产品按照类型筛选出不同的set
     */
    private void addFundSet(BalanceBean balanceBean, HighProductDBInfoBean productBean,
                            Set<String> qxFundCodeSet, Set<String> incomeFundCodeSet,
                            Set<String> guQuanFundCodeSet, Set<String> agentFractionateCallFundCodeSet,
                            Set<String> directFractionateCallFundCodeSet, Set<String> fixedIncomeFundCodeSet,
                            Set<String> navTypeFundCodeSet, List<String> qianXiProducts) {
        String productCode = balanceBean.getProductCode();
        // 分次call产品
        if (FractionateCallFlagEnum.YES.getCode().equals(productBean.getFractionateCallFlag())) {
            if (ScaleTypeEnum.DIRECT.getCode().equals(productCode)) {
                directFractionateCallFundCodeSet.add(productCode);
            } else {
                agentFractionateCallFundCodeSet.add(productCode);
            }
        }
        // 千禧年产品当做股权与分次call产品处理
        if (qianXiProducts.contains(productCode)) {
            qxFundCodeSet.add(productCode);
            if (ScaleTypeEnum.DIRECT.getCode().equals(productCode)) {
                directFractionateCallFundCodeSet.add(productCode);
            } else {
                agentFractionateCallFundCodeSet.add(productCode);
            }
        }
        // 股权产品
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productBean.getFundSubType())) {
            guQuanFundCodeSet.add(productCode);
        } else {
            if (StageEstablishFlagEnum.STAGE.getCode().equals(productBean.getStageEstablishFlag()) && !StringUtils.isEmpty(balanceBean.getSubProductCode())) {
                // 分期成立产品,直销的有子基金代码, 用子产品代码查信息
                incomeFundCodeSet.add(balanceBean.getSubProductCode());
            } else {
                incomeFundCodeSet.add(productCode);
            }
        }
        // 固定收益
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productBean.getFundSubType())) {
            fixedIncomeFundCodeSet.add(productCode);
        }
        // 是否净值化产品
        if (acctBalanceBaseInfoService.isNavTypeFund(productBean.getStandardFixedIncomeFlag(), productBean.getFundSubType(), productBean.getFundCode())) {
            navTypeFundCodeSet.add(productCode);
        }
    }

    /**
     * 查询确认交易订单
     *
     * @param txAcctNo  交易账号
     * @param hboneNo   一账通
     * @param fundCodes 基金代码
     * @return 确认交易
     */
    private Map<String, List<AckDealOrderInfo>> getAckDealDtlMap(String txAcctNo, String hboneNo, Set<String> fundCodes) {
        // 查询代销确认订单
        Map<String, List<AckDealOrderInfo>> agentAckDealDtlMap = acctBalanceBaseInfoService.getAgentAckDealDtlMap(txAcctNo, fundCodes);
        // 查询直销确认订单
        Map<String, List<AckDealOrderInfo>> directAckDealDtlMap = acctBalanceBaseInfoService.getDirectAckDealDtlMap(hboneNo, fundCodes);
        // 合并直销与代销交易
        for (Map.Entry<String, List<AckDealOrderInfo>> directEntry : directAckDealDtlMap.entrySet()) {
            if (agentAckDealDtlMap.containsKey(directEntry.getKey())) {
                List<AckDealOrderInfo> ackDealOrderInfos = agentAckDealDtlMap.get(directEntry.getKey());
                ackDealOrderInfos.addAll(directEntry.getValue());
            } else {
                agentAckDealDtlMap.put(directEntry.getKey(), directEntry.getValue());
            }
        }
        return agentAckDealDtlMap;
    }

    /**
     * 设置净值信息
     *
     * @param balanceList
     * @param incomeFundCodeSet
     */
    private Map<String, HighProductNavBean> buildAndSetNavInfo(List<BalanceBean> balanceList, Set<String> incomeFundCodeSet) {
        List<BalanceBean> stageBalanceList = balanceList.stream().filter(balanceBean -> YesOrNoEnum.YES.getCode().equals(balanceBean.getStageFlag())).collect(Collectors.toList());
        // 分期成立的产品净值信息,注意这个里面会设置持仓产品的子基金代码,map的key是子基金代码
        Map<String, HighProductNavBean> navMap = getStructureNavMap(stageBalanceList);
        // 5.2.查询非分期成立产品净值信息
        Map<String, HighProductNavBean> noStageNavMap = getNavMap(new ArrayList<>(incomeFundCodeSet));
        // 5.3.合并净值map
        navMap.putAll(noStageNavMap);
        return navMap;
    }

    /**
     * 查询股权产品(分次call)认缴金额
     *
     * @param queryConfirmBalanceParam         查询入参
     * @param agentFractionateCallFundCodeSet  代销基金代码
     * @param directFractionateCallFundCodeSet 直销基金代码
     * @return 金额认缴金额, key是母基金代码
     */
    private Map<String, BigDecimal> getPaidInAmtMap(QueryBalanceParam queryConfirmBalanceParam,
                                                    Set<String> agentFractionateCallFundCodeSet,
                                                    Set<String> directFractionateCallFundCodeSet) {
        Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
        // 查询代销认缴金额
        if (!CollectionUtils.isEmpty(agentFractionateCallFundCodeSet)) {
            // 查询客户产品的认缴金额
            List<SubscribeAmtDetailPo> subscribeAmtDetailList = subscribeAmtDetailRepository.getSubscribeAmtDetail(queryConfirmBalanceParam.getTxAcctNo(), new ArrayList<>(agentFractionateCallFundCodeSet));
            if (!CollectionUtils.isEmpty(subscribeAmtDetailList)) {
                for (SubscribeAmtDetailPo subscribeAmtDetailPo : subscribeAmtDetailList) {
                    map.put(subscribeAmtDetailPo.getFundCode(), subscribeAmtDetailPo.getSubscribeAmt());
                }
            } else {
                log.info("代销分次call基金查不到认缴实缴信息,agentFractionateCallFundCodeSet={}", JSON.toJSON(agentFractionateCallFundCodeSet));
            }
        }
        // 查询直销认缴金额
        if (!CollectionUtils.isEmpty(directFractionateCallFundCodeSet)) {
            // 查询客户产品的认缴金额
            FractionatedCallOuterResult result = fractionatedCallOuterService.queryPaidInAmt(queryConfirmBalanceParam.getHboneNo(), new ArrayList<>(directFractionateCallFundCodeSet));
            if (result == null || CollectionUtils.isEmpty(result.getFractionatedCallBeanList())) {
                log.info("直销分次call基金查不到认缴实缴信息,directFractionateCallFundCodeSet={}", JSON.toJSON(directFractionateCallFundCodeSet));
                return map;
            }
            // 同一个fundCode，可能会对应多条数据，因此需要累加金额 add 千禧年产品适配需求 20230214
            for (FractionatedCallBean fractionatedCallBean : result.getFractionatedCallBeanList()) {
                BigDecimal paidInAmt = map.get(fractionatedCallBean.getFundCode()) == null ? fractionatedCallBean.getPaidInAmt()
                        : map.get(fractionatedCallBean.getFundCode()).add(fractionatedCallBean.getPaidInAmt());
                map.put(fractionatedCallBean.getFundCode(), paidInAmt);
            }

        }
        return map;
    }

    /**
     * 获取分期成立产品净值信息
     */
    private Map<String, HighProductNavBean> getStructureNavMap(List<BalanceBean> balanceBeanList) {
        final Map<String, HighProductNavBean> fundNavMap = new ConcurrentHashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(balanceBeanList)) {
            return fundNavMap;
        }

        // 多线程异步查询产品净值信息
        final CountDownLatch latch = new CountDownLatch(balanceBeanList.size());
        for (BalanceBean balanceBean : balanceBeanList) {
            CommonThreadPool.submit(new QueryStructProductNavTask(queryHighProductOuterService, fundNavMap, balanceBean, latch));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("getStructureNavMap-查询分期成立产品净值出现异常,e:", e);
            Thread.currentThread().interrupt();
        }
        log.info("分期成立产品净值map,fundNavMap={}", JSON.toJSONString(fundNavMap));
        return fundNavMap;
    }

    /**
     * getNavMap:(批量获取基金净值map)
     *
     * @param midProductIds
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:00:24
     */
    private Map<String, HighProductNavBean> getNavMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavBean> highProductNavMap = new HashMap<String, HighProductNavBean>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavMap;
        }

        List<HighProductNavBean> highProductNavBeanList = null;
        try {
            highProductNavBeanList = queryHighProductOuterService.getHighProductNavInfo(new ArrayList<>(midProductIds));
        } catch (Exception e) {
            log.error("getNavMap-查询产品净值异常e:{},fundCode={}", e, JSON.toJSONString(midProductIds));
        }

        if (!org.apache.commons.collections.CollectionUtils.isEmpty(highProductNavBeanList)) {
            for (HighProductNavBean highProductNavBean : highProductNavBeanList) {
                highProductNavMap.put(highProductNavBean.getFundCode(), highProductNavBean);
            }
        }
        return highProductNavMap;
    }

    /**
     * @param midProductIds
     * @return java.util.Map<java.lang.String, com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavDivBean>
     * @description:(批量获取基金分红信息map)
     * @author: haiguang.chen
     * @date: 2022/7/14 14:16
     * @since JDK 1.8
     */
    private Map<String, HighProductNavDivBean> getNavDivMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavDivBean> highProductNavDivBeanMap = new HashMap<>();
        if (CollectionUtils.isEmpty(midProductIds)) {
            return highProductNavDivBeanMap;
        }

        List<HighProductNavDivBean> highProductNavDivBeanList = null;
        try {
            highProductNavDivBeanList = queryHighProductOuterService.getHighProductNavDivInfo(new ArrayList<>(midProductIds));
        } catch (Exception e) {
            log.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo,productCode={}, error={}", JSON.toJSONString(midProductIds), e);
        }

        if (!CollectionUtils.isEmpty(highProductNavDivBeanList)) {
            for (HighProductNavDivBean highProductNavDivBean : highProductNavDivBeanList) {
                highProductNavDivBeanMap.put(highProductNavDivBean.getFundCode(), highProductNavDivBean);
            }
        }
        return highProductNavDivBeanMap;
    }


}
