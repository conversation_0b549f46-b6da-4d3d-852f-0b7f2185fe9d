package com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description:查询es交易记录结果
 * @Author: yun.lu
 * Date: 2025/6/23 19:59
 */
public class QueryEsOrderRecordListResponse extends OrderSearchBaseResponse {
    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct;
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct;
    /**
     * 高端订单列表
     */
    private List<DealOrderBean> dealOrderList;

    public String getHasHZProduct() {
        return hasHZProduct;
    }

    public void setHasHZProduct(String hasHZProduct) {
        this.hasHZProduct = hasHZProduct;
    }

    public String getHasHKProduct() {
        return hasHKProduct;
    }

    public void setHasHKProduct(String hasHKProduct) {
        this.hasHKProduct = hasHKProduct;
    }

    public List<DealOrderBean> getDealOrderList() {
        return dealOrderList;
    }

    public void setDealOrderList(List<DealOrderBean> dealOrderList) {
        this.dealOrderList = dealOrderList;
    }

    public static class DealOrderBean implements Serializable {
        private static final long serialVersionUID = 7757051939821034685L;
        /**
         * 客户订单号
         */
        private String dealNo;
        /**
         * 分销代码
         */
        private String disCode;
        /**
         * 交易账号
         */
        private String txAcctNo;
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 银行账号
         */
        private String bankAcct;
        /**
         * 银行代码
         */
        private String bankCode;

        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品简称
         */
        private String productAttr;
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 申请金额
         */
        private BigDecimal appAmt;
        /**
         * 申请份额
         */
        private BigDecimal appVol;
        /**
         * 申请日期时间
         */
        private Date appDtm;
        /**
         * 付款状态
         */
        private String payStatus;
        /**
         * 订单状态
         */
        private String orderStatus;
        /**
         * TA交易日期
         */
        private String taTradeDt;
        /**
         * 手续费
         */
        private BigDecimal fee;
        /**
         * 中台业务码
         */
        private String mBusiCode;

        /**
         * 销售类型: 1-直销; 2-代销
         */
        private String scaleType;

        /**
         * 交易状态
         */
        private String tradeStatus;
        /**
         * 分红方式
         */
        private String divMode;

        /**
         * 确认份额
         */
        private BigDecimal ackVol;

        /**
         * 确认金额
         */
        private BigDecimal ackAmt;
        /**
         * 确认日期
         */
        private String ackDt;

        /**
         * 净值
         */
        private BigDecimal nav;


        /**
         * 产品类型
         */
        private String productType;

        /**
         * 上报TA日期
         */
        private String submitTaDt;

        /**
         * 产品子类型
         */
        private String productSubType;

        /**
         * 币种
         */
        private String currency;

        /**
         * 好买香港代销标识: 0-否; 1-是
         */
        private String hkSaleFlag;

        /**
         * 确认标识
         */
        private String txAckFlag;

        public String getTradeStatus() {
            return tradeStatus;
        }

        public void setTradeStatus(String tradeStatus) {
            this.tradeStatus = tradeStatus;
        }

        public String getDealNo() {
            return dealNo;
        }

        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getBankAcct() {
            return bankAcct;
        }

        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductAttr() {
            return productAttr;
        }

        public void setProductAttr(String productAttr) {
            this.productAttr = productAttr;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public BigDecimal getAppAmt() {
            return appAmt;
        }

        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public Date getAppDtm() {
            return appDtm;
        }

        public void setAppDtm(Date appDtm) {
            this.appDtm = appDtm;
        }

        public String getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(String payStatus) {
            this.payStatus = payStatus;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getTaTradeDt() {
            return taTradeDt;
        }

        public void setTaTradeDt(String taTradeDt) {
            this.taTradeDt = taTradeDt;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getmBusiCode() {
            return mBusiCode;
        }

        public void setmBusiCode(String mBusiCode) {
            this.mBusiCode = mBusiCode;
        }

        public String getScaleType() {
            return scaleType;
        }

        public void setScaleType(String scaleType) {
            this.scaleType = scaleType;
        }

        public String getDivMode() {
            return divMode;
        }

        public void setDivMode(String divMode) {
            this.divMode = divMode;
        }

        public BigDecimal getAckVol() {
            return ackVol;
        }

        public void setAckVol(BigDecimal ackVol) {
            this.ackVol = ackVol;
        }

        public BigDecimal getAckAmt() {
            return ackAmt;
        }

        public void setAckAmt(BigDecimal ackAmt) {
            this.ackAmt = ackAmt;
        }

        public String getAckDt() {
            return ackDt;
        }

        public void setAckDt(String ackDt) {
            this.ackDt = ackDt;
        }

        public BigDecimal getNav() {
            return nav;
        }

        public void setNav(BigDecimal nav) {
            this.nav = nav;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public String getSubmitTaDt() {
            return submitTaDt;
        }

        public void setSubmitTaDt(String submitTaDt) {
            this.submitTaDt = submitTaDt;
        }

        public String getProductSubType() {
            return productSubType;
        }

        public void setProductSubType(String productSubType) {
            this.productSubType = productSubType;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getHkSaleFlag() {
            return hkSaleFlag;
        }

        public void setHkSaleFlag(String hkSaleFlag) {
            this.hkSaleFlag = hkSaleFlag;
        }

        public String getTxAckFlag() {
            return txAckFlag;
        }

        public void setTxAckFlag(String txAckFlag) {
            this.txAckFlag = txAckFlag;
        }
    }
}
