# ✅ AI-Dubbo交易接口代码生成提示词（基于详细设计）

你是一名资深Java开发和架构师,请根据我提供的**Dubbo交易接口详细设计文档**内容，严格遵循当前项目的代码规范和结构，生成完整的**后端代码实现**。

## 📌 核心输出要求

- **输出格式**：Java 源代码，按项目模块（`client`, `service`, `dao`）和文件类型（`Facade`, `Request`, `Response`, `Impl`, `Service`, `Repository`, `PO`, `Mapper`）分块输出，确保代码可直接应用到项目中。
- **遵循项目规范**：严格遵守 `.cursor/rules/` 下定义的项目结构、命名约定、注释标准和编码风格。
- **复用优先**：优先使用项目中已有的公共组件、工具类（如日期、字符串、集合处理）、和基础框架，禁止重复造轮子。
- **交易特性**：专门针对交易类接口，涉及数据修改，必须支持幂等性和完整事务管理。

## 📘 你的任务：基于设计文档生成交易接口代码

请根据我提供的**详细设计文档**，一次性生成所有相关的代码。

### 设计文档结构（输入）

- **功能名称**: 交易接口的核心业务功能，例如 "签署待补签协议"、"修改回款信息"。
- **接口定义**:
    - **接口名**: `SubmitSupSignAgreementFacade`
    - **方法名**: `execute`
- **请求参数（Request）**: 字段名、Java类型、中文描述、是否必填、校验规则（如长度、格式、取值范围）。
- **响应参数（Response）**: 字段名、Java类型、中文描述。
- **关键业务处理流程**:
    1.  **参数校验**: 检查请求参数的完整性和有效性。
    2.  **业务校验**: 检查业务规则是否满足，如用户权限、数据访问权限等。
    3.  **幂等性检查**: 通过toIdempotentString()方法检查是否为重复交易。
    4.  **核心交易逻辑**: 描述数据修改、业务处理、状态更新等交易步骤。
    5.  **数据持久化**: 说明需要插入或更新的数据库表及字段。
    6.  **外部调用**: 列出需要调用的其他Dubbo服务或HTTP接口（如需要）。
    7.  **事务提交**: 确保数据一致性的事务边界。
- **异常处理策略**: 定义不同场景下的错误码和异常信息。
- **幂等性设计**: 描述幂等性判断逻辑和重复处理策略。

### 代码生成清单（输出）

请按以下结构生成代码，确保所有文件都符合项目规范。

#### 1. `high-order-center-client` 模块

- **接口定义 (`Facade`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/trade/{具体功能}/...`
    - **命名**: `功能名Facade.java`
    - **规范**:
        - 继承 `BaseFacade<Request, Response>`。
        - 必须包含完整的APIDOC风格注释 (`@api`, `@apiName`, `@apiGroup` 等)。
        - 注释中明确说明这是交易接口。

- **请求对象 (`Request`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/trade/{具体功能}/...`
    - **命名**: `接口名Request.java`
    - **规范**:
        - **继承 `OrderTradeBaseRequest`**（不是BaseRequest或OrderSearchBaseRequest）。
        - **必须实现 `IdempotentSupport` 接口**。
        - **使用@Getter/@Setter注解**。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 使用 `@MyValidation` 注解进行字段校验。
        - 自动继承交易账号(txAcctNo)、一账通账号(hbOneNo)、外部订单号(externalDealNo)等通用交易参数。
        - **必须包含无参构造函数，并在构造函数中设置txCode**：
            ```java
            public 接口名Request() {
                this.setTxCode(TxCodes.对应的交易码);
            }
            ```
        - **必须重写 `toIdempotentString()` 方法**：
            ```java
            @Override
            public String toIdempotentString() {
                StringBuilder idempotent = new StringBuilder(100);
                idempotent.append(getTxAcctNo());
                idempotent.append(getTxCode());
                // 根据业务需要添加唯一标识字段
                if (null != getExternalDealNo()) {
                    idempotent.append(getExternalDealNo());
                } else {
                    idempotent.append(getAppDt());
                    idempotent.append(getShortAppTm());
                }
                return idempotent.toString();
            }
            ```
        - **必须包含完整的APIDOC注释，格式如下**：
            ```java
            /**
             * @api {DUBBO} com.howbuy.tms.high.orders.facade.trade.{功能包名}.{接口名}.execute() 接口描述
             * @apiVersion 1.0.0
             * @apiGroup 接口名FacadeService
             * @apiName execute
             * @apiDescription 接口详细描述
             *
             * @apiParam (请求参数) {类型} [字段名] 字段描述
             * @apiParamExample 请求参数示例
             * dubbo com.howbuy.tms.high.orders.facade.trade.{功能包名}.{接口名Request}
             *
             * @apiSuccess (响应结果) {类型} 字段名 字段描述
             * @apiSuccessExample 响应结果示例
             * dubbo com.howbuy.tms.high.orders.facade.trade.{功能包名}.{接口名Response}
             * {响应示例JSON}
             */
            ```

- **响应对象 (`Response`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/trade/{具体功能}/...`
    - **命名**: `接口名Response.java`
    - **规范**:
        - **继承 `OrderTradeBaseResponse`**（不是实现Serializable或继承OrderSearchBaseResponse）。
        - **不使用@Getter/@Setter注解，手动编写getter/setter方法**。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 通常包含dealNo（订单号）等交易相关字段。

#### 2. `high-order-center-service` 模块

- **接口实现 (`FacadeService`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/facade/trade/{具体功能}/...`
    - **命名**: `功能名FacadeService.java`
    - **规范**:
        - 实现对应的 `Facade` 接口。
        - **使用 `@DubboService` 和 `@Service("接口名首字母小写")` 注解**。
        - **在execute方法上使用 `@Idempotent` 注解**。
        - 注入 `Service` 层并调用其方法，只做参数转换和基本校验，不实现核心交易逻辑。
        - 添加标准的类和方法注释。
        - **必须支持幂等性**。
        - **添加详细的日志记录**，包括请求参数和响应结果。

- **业务逻辑 (`Service`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/service/trade/{具体功能}/...`
    - **命名**: `功能名Service.java`
    - **规范**:
        - 使用 `@Service` 和 `@Slf4j` 注解。
        - 实现所有核心交易逻辑、数据修改和业务处理。
        - 调用 `Repository` 层进行数据持久化。
        - 方法需有完整的Javadoc注释（`@description`, `@param`, `@return`, `@author`, `@date`）。
        - **专注于数据修改操作，需要考虑事务边界**。
        - **添加完善的异常处理和业务校验**。

- **数据仓库 (`Repository`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/repository/...`
    - **命名**: `表名Repository.java`
    - **规范**:
        - 使用 `@Repository` 注解。
        - 注入 `Mapper` 接口，封装数据库操作。
        - **事务方法使用 `@Transactional`**。
        - 类级别使用 `@Transactional(propagation = Propagation.REQUIRED)`。
        - 方法需有完整的Javadoc注释。
        - 支持复杂的数据更新和插入操作。

#### 3. `high-order-center-dao` 模块

- **数据实体 (`PO`)**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/po/...`
    - **命名**: `表名Po.java`
    - **规范**:
        - 与数据库表字段一一对应。
        - 使用 `@Getter` / `@Setter` 注解。
        - 字段有中文注释。

- **根据mybatis插件自动生成的Mapper 接口**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/mysql/...`
    - **命名**: `表名AutoMapper.java`

- **根据mybatis插件自动生成的Mapper XML**
    - **位置**: `high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/mysql/...`
    - **命名**: `表名AutoMapper.xml`

- **根据业务需求自定义的Mapper 接口**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/mysql/customize/...`
    - **命名**: `表名Mapper.java`，并且该类需要继承自动生成的mapper:`表名AutoMapper`
    - **规范**:
        - 包含复杂的更新、插入方法，如条件更新、批量插入、关联更新等。
        - 方法命名清晰，体现操作意图。

- **自定义Mapper XML**
    - **位置**: `high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/mysql/customize/...`
    - **命名**: `表名Mapper.xml`
    - **规范**:
        - 包含复杂的更新和插入SQL，支持动态条件。
        - 注意SQL性能优化，合理使用索引。
        - 支持事务操作。

## 🔍 交易接口专有特性

| 特性                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ✅ **数据修改操作**      | 专注于数据修改，涉及插入、更新、删除操作，需要事务控制。     |
| ✅ **强制幂等性要求**   | 交易操作必须实现IdempotentSupport接口和toIdempotentString()方法。           |
| ✅ **继承OrderTradeBaseRequest** | 请求对象继承OrderTradeBaseRequest，不是BaseRequest或OrderSearchBaseRequest。        |
| ✅ **继承OrderTradeBaseResponse** | 响应对象继承OrderTradeBaseResponse，不是实现Serializable或继承OrderSearchBaseResponse。   |
| ✅ **@DubboService和@Service注解** | FacadeImpl使用@DubboService和@Service("beanName")注解，不是@Component。           |
| ✅ **包路径规范**    | Request和Response都在facade.trade.{具体功能}包下，不在client.domain包下。 |
| ✅ **手动getter/setter** | 不使用@Getter/@Setter注解，手动编写getter/setter方法。    |
| ✅ **构造函数设置txCode** | 必须在无参构造函数中设置对应的交易码。                |
| ✅ **完整APIDOC注释** | 包含@api、@apiVersion、@apiGroup、@apiName、@apiDescription、@apiParam、@apiSuccess等完整注释。 |
| ✅ **@Idempotent注解**   | execute方法必须添加@Idempotent注解确保幂等性处理。      |
| ✅ **字段校验注解**   | 使用@MyValidation注解进行字段校验，而不是其他校验注解。      |
| ✅ **事务管理**      | Repository层方法需要@Transactional注解，确保数据一致性。                   |
| ✅ **详细日志记录**      | 记录详细的交易日志，包括请求参数、响应结果、异常信息。                   |

## 🚫 禁止事项

| 类型                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ❌ **违反项目规范**  | 所有代码的包结构、命名、注释必须符合 `.cursor/rules/` 中的定义。 |
| ❌ **硬编码**        | 禁止在代码中硬编码任何常量、URL、配置项，应使用常量类或配置中心。 |
| ❌ **逻辑泄露**      | 交易逻辑必须封装在 `Service` 层，`FacadeImpl` 只做调度和校验。 |
| ❌ **忽略异常处理**   | 必须对外部调用和数据库操作进行 `try-catch`，并进行合理的异常转换和日志记录。 |
| ❌ **绕过Repository** | `Service` 层禁止直接注入和调用 `Mapper`，必须通过 `Repository` 层访问数据库。 |
| ❌ **禁止使用魔法值** | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举。 |
| ❌ **缺少枚举或常量** | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量。 |
| ❌ **错误包路径**    | Request和Response必须在facade.trade.{具体功能}包下，不能放在client.domain包下。 |
| ❌ **错误继承关系**  | 交易接口请求必须继承OrderTradeBaseRequest，不能继承BaseRequest或OrderSearchBaseRequest；响应必须继承OrderTradeBaseResponse，不能实现Serializable或继承OrderSearchBaseResponse。 |
| ❌ **错误注解使用**  | FacadeImpl不能使用@Component，必须使用@DubboService和@Service注解；不能使用@Getter/@Setter注解，必须手动编写getter/setter方法。    |
| ❌ **缺少构造函数**  | 请求类必须包含无参构造函数，并在构造函数中设置对应的txCode。  |
| ❌ **不完整APIDOC**  | 必须包含完整的APIDOC注释格式，包括@api、@apiVersion、@apiGroup、@apiName、@apiDescription、@apiParam、@apiSuccess、@apiSuccessExample等。 |
| ❌ **错误校验注解**  | 必须使用@MyValidation注解进行字段校验，不能使用其他校验注解。 |
| ❌ **缺少幂等性实现**  | 必须实现IdempotentSupport接口和toIdempotentString()方法，execute方法必须添加@Idempotent注解。  |
| ❌ **忽略事务管理**  | Repository层的数据修改方法必须添加@Transactional注解。 |

## 📊 交易接口关键特性

### 1. 幂等性设计
- 所有交易接口必须实现幂等性，防止重复提交
- 通过toIdempotentString()方法生成唯一标识
- 使用@Idempotent注解在execute方法上

### 2. 事务管理
- Repository层方法使用@Transactional注解
- 事务边界控制在Repository层
- 确保数据一致性和原子性

### 3. 异常处理
- 完善的异常捕获和转换机制
- 统一的错误码和错误信息
- 详细的异常日志记录

### 4. 安全控制
- 严格的参数校验
- 业务权限检查
- 敏感操作审计日志

### 5. 性能优化
- 合理的索引使用
- 批量操作优化
- 数据库连接池管理

## 🔄 交易类接口与查询类接口的主要区别

| 特性 | 交易类接口 | 查询类接口 |
| ---- | --------- | --------- |
| 包路径 | facade.trade.{功能} | facade.search.{功能} |
| 请求继承 | OrderTradeBaseRequest | OrderSearchBaseRequest |
| 响应继承 | OrderTradeBaseResponse | OrderSearchBaseResponse |
| 幂等性 | 必须实现IdempotentSupport | 无需幂等性 |
| 注解使用 | @DubboService + @Service("beanName") | @Component |
| execute方法注解 | @Idempotent | 无 |
| 事务特性 | 涉及数据修改，需事务控制 | 只读操作，无事务要求 |
| 构造函数 | 必须设置交易码 | 可选设置 |
| 日志记录 | 详细的交易日志 | 简单的查询日志 |
| 异常处理 | 完善的异常处理机制 | 基本的异常处理 |

## 📋 代码生成示例结构

```
交易功能名称: 签署待补签协议 (SubmitSupSignAgreement)

生成的文件结构:
├── client模块
│   └── facade/trade/submitsupsignagreement/
│       ├── SubmitSupSignAgreementFacade.java          # 接口定义
│       ├── SubmitSupSignAgreementRequest.java         # 请求对象
│       └── SubmitSupSignAgreementResponse.java        # 响应对象
├── service模块  
│   ├── facade/trade/submitsupsignagreement/
│   │   └── SubmitSupSignAgreementFacadeService.java   # 接口实现
│   ├── service/trade/submitsupsignagreement/
│   │   └── SubmitSupSignAgreementService.java         # 业务逻辑
│   └── repository/
│       └── 相关Repository.java                        # 数据访问
└── dao模块
    ├── po/
    │   └── 相关Po.java                                 # 数据实体
    └── mapper/mysql/customize/
        ├── 相关Mapper.java                             # 数据映射接口
        └── 相关Mapper.xml                              # SQL映射文件
```

## 🎯 生成指南

1. **明确交易业务需求**，确定接口名称和功能
2. **在正确的包路径下**创建接口和对应的请求/响应类
3. **请求类继承OrderTradeBaseRequest**，实现IdempotentSupport接口
4. **构造函数中设置交易码**
5. **实现toIdempotentString()方法**，确保交易幂等性
6. **响应类继承OrderTradeBaseResponse**
7. **在service模块中创建对应的实现类**
8. **实现类添加@DubboService和@Service注解**
9. **按规范编写APIDOC注释**，确保包含所有必要标签
10. **所有字段添加明确的中文注释**
11. **非简单方法添加详细的方法注释**
12. **添加@Idempotent注解在execute方法上**确保幂等性
13. **Repository层方法添加@Transactional注解**
14. **日志记录请求和响应信息**
15. **完善的异常处理和业务校验**

**请在我提供详细设计文档后，立即按上述要求生成完整、高质量、符合幂等性要求的交易接口代码。**

---
*文档生成时间: 2025-09-02 09:32:47*  
*作者: hongdong.xie*  
*版本: 1.0.0*