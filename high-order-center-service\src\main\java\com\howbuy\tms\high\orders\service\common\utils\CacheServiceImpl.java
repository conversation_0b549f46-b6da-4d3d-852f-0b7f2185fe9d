package com.howbuy.tms.high.orders.service.common.utils;


import com.howbuy.cachemanagement.client.*;
import com.howbuy.cachemanagement.constant.CacheConstant;
import com.howbuy.cachemanagement.dto.*;
import com.howbuy.cachemanagement.exception.MgetDataException;
import com.howbuy.cachemanagement.logger.RedisLogGenerator;
import com.howbuy.cachemanagement.processor.ConfigProcessor;
import com.howbuy.cachemanagement.processor.KeyProcessor;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.sync.CacheSynchronizer;
import com.howbuy.cachemanagement.sync.CacheSynchronizerFactory;
import com.howbuy.cachemanagement.sync.SyncCacheListener;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static com.howbuy.cachemanagement.constant.CacheConstant.CLUSTER_TYPE_SHARDS;
import static com.howbuy.cachemanagement.constant.CacheConstant.MGET_MAX_SIZE;
import static com.howbuy.cachemanagement.processor.KeyProcessor.getKeyPrefix;

/**
 * 缓存服务
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 2014-7-25 上午9:38:25
 */
@SuppressWarnings("unchecked")
public class CacheServiceImpl implements com.howbuy.cachemanagement.service.CacheService {

    private static final Logger redisLogger = LoggerFactory.getLogger("redis_log");
    private static final Logger redisMQLogger = LoggerFactory.getLogger("redis_mq_log");

    private static final String SUCC = "success";
    private static final String FAIL = "fail";
    private static final String NONSUPPORT = "-1"; // 不支持
    private static final String HIT = "1"; // 命中
    private static final String NOHIT = "0";// 未命中

    /**
     * 缓存服务单例
     */
    private static com.howbuy.cachemanagement.service.CacheService instance;

    /**
     * 获取单例，尝试次数取默认值1
     *
     * @return 缓存服务单例
     */
    public static com.howbuy.cachemanagement.service.CacheService getInstance() {
        // 默认只尝试1次
        return getInstance(1);
    }

    /**
     * 获取单例，同时设置尝试次数
     *
     * @param tryTimes 尝试次数
     * @return 缓存服务单例
     */
    public static com.howbuy.cachemanagement.service.CacheService getInstance(Integer tryTimes) {
        if (instance == null) {
            newInstance();
        }
        if (instance != null) {
            instance.setTryTimes(tryTimes);
        }
        return instance;
    }


    /**
     * 新建单例，线程安全
     */
    public static synchronized void newInstance() {

        if (instance == null) {
            CacheService tmp = new CacheServiceImpl();
            instance = tmp;
        }
    }

    /**
     * 尝试次数
     */
    private Integer tryTimes = 1;

    /**
     * logger
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 同步监听器池<Key, SyncCacheListener>
     */
    private ConcurrentMap<String, SyncCacheListener> syncCacheListeners = new ConcurrentHashMap<String, SyncCacheListener>();

    private CacheServiceImpl() {
        initCacheSynchronizers();
    }

    /**
     * 为Key添加同步监听器，包含前置、后置监听
     *
     * @param key               键
     * @param syncCacheListener 监听器
     */
    public void addSyncCacheListener(String key, SyncCacheListener syncCacheListener) {
        key = trimKey(key);
        key = getKeyPrefix(key);
        syncCacheListeners.put(key, syncCacheListener);
    }

    public void afterSyncronizingCache(String key) {
        SyncCacheListener syncCacheListener = getSyncCacheListener(key);
        if (syncCacheListener == null) {
            return;
        }
        syncCacheListener.postSyncCache(key);
    }

    public <T> Long append(String key, T value) {
        // 按CCMS配置重置失效时间
        return append(key, value, true);
    }

    public <T> Long append(String key, T value, boolean reExpire) {
        Long result = -1L;
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            result = remoteCacheClient.append(key, value, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("append", "rpush,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    /**
     * 本地同步key之前的回调
     *
     * @param key
     */
    public void beforeSyncronizingCache(String key) {
        SyncCacheListener syncCacheListener = getSyncCacheListener(key);
        if (syncCacheListener == null) {
            return;
        }
        syncCacheListener.preSyncCache(key);
    }

    /**
     * 本地缓存版本是否老于所传的版本
     *
     * @param localCacheGroup
     * @param key
     * @param fromVersion
     * @return
     */
    private boolean earlierThanVersion(String localCacheGroup, String key, Long fromVersion) {
        if (fromVersion == null) {
            return false;
        }

        // 本地缓存中key最新的版本号
        Object version = EhCacheClientFactory.getLocalCacheClient().get(localCacheGroup, CacheConstant.SYNC_PREFIX + key);
        if (version == null) {
            return true;
        }

        Long localVersion = Long.valueOf(version.toString());

        return localVersion < fromVersion;
    }

    public boolean exists(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        boolean ret = false;

        try {
            ret = remoteCacheClient.exists(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("exists", "exists", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public boolean existsInMap(String key, String field) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        boolean ret = false;

        try {
            ret = remoteCacheClient.existsInMap(key, field);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("existsInMap", "hexists", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public boolean existsInSet(String key, String member) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        boolean ret = false;

        try {
            ret = remoteCacheClient.existsInSet(key, member);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("existsInMap", "hexists", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public void expires(String key, Integer seconds) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            remoteCacheClient.expires(key, seconds);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("expires", "expire", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }


    }

    /**
     * added by william.hu for iteration version 1.1.0
     */
    public <T> List<T> mget(String... keys) {
        List<T> results = new ArrayList<T>();
        for (String key : keys) {
            results.add((T) get(key, null));
        }

        return results;
    }

    public <T> T get(String key) {
        return get(key, null);
    }

    private <T> T get(String key, Long fromVersion) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);
        // CacheConfig cacheConfig = keyProperty.getCacheConfig();

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();

        T value = null;

        try {
            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();


            if (needLocalCache) {
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();
                // 读取本地缓存
                String localCacheGroup = keyProperty.getLocalCacheGroup();

                if (cacheConfig.getAddrList() == null || cacheConfig.getAddrList().isEmpty()) {
                    // 直接取本地缓存
                    value = (T) ehCacheClient.get(localCacheGroup, key);
                } else {
                    if (!earlierThanVersion(localCacheGroup, key, fromVersion)) {
                        // 本地缓存的版本新于指定版本，取本地缓存
                        value = (T) ehCacheClient.get(localCacheGroup, key);
                    }

                    if (value == null) {
                        synchronized (key) {
                            if (!earlierThanVersion(localCacheGroup, key, fromVersion)) {
                                // 本地缓存的版本新于指定版本，取本地缓存
                                value = (T) ehCacheClient.get(localCacheGroup, key);
                            }

                            // 需要双重检查
                            if (value == null) {
                                // 带备份的远程读取
                                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
//                                remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                                value = remoteCacheClient.get(key);
                                ehCacheClient.put(localCacheGroup, key, value);
                                // 缓存中的版本信息
                                // 取得Key的版本
                                Long remoteVersion = remoteCacheClient.getRemoteVersion(key);
                                ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                            }
                        }
                    }

                    // 用到这个Key，才加载其（所在本地组的）消息监听器
                    CacheSynchronizer cacheSynchronizer = keyProperty.getCacheSynchronizerVO();
                    cacheSynchronizer.addReceiveListener(localCacheGroup);
                }
            } else {
                // 读取远程缓存，无备份
                value = new ScalableRemoteCacheClient(remoteCacheClient).get(key);
            }

        } catch (RuntimeException e) {
            throw e;
        }

        return value;
    }

    /**
     * 根据键，及重置失效标志，获取失效时间
     *
     * @param key      键
     * @param reExpire 重置失效标志
     * @return 失效时间，秒。如果为0，不重置
     */
    private Integer getExpire(String key, boolean reExpire) {
        Integer expire = 0;
        if (reExpire) {
            // 重置过期时间标志==true，则从CCMS配置中取得该Key的过期时间
            KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

            expire = keyProperty.getExpire();
        }

        return expire;
    }

    public Map<String, String> getFromMap(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;
        String isHit = NOHIT;

        Map<String, String> map = null;

        try {
            map = remoteCacheClient.getFromMap(key);
            if (map != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFromMap", "hgetAll", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, isHit));
            }
        }

        return map;
    }

    public List<String> getFromMap(String key, List<String> fields) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);
        List<String> list = new ArrayList<String>();

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;
        String isHit = NOHIT;

        try {
            list = remoteCacheClient.getFromMap(key, fields);
            if (list != null && list.size() > 0) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFromMap", "hgetAll", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, isHit));
            }
        }

        return list;
    }

    public Object getObjFromMap(String key, String field) {
        return getObjFromMap(key, field, false);
    }

    public Object getObjFromMap(String key, String field, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String isHit = NOHIT;

        Object ret = null;

        try {
            ret = remoteCacheClient.getObjFromMap(key, field, getExpire(key, reExpire));
            if (ret != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getObjFromMap", "hget,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, isHit));
            }
        }

        return ret;
    }

    public Map<String, Object> getFromObjMap(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Map<String, Object> ret = null;

        String isHit = NOHIT;

        try {
            ret = remoteCacheClient.getFromObjMap(key);
            if (ret != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFromObjMap", "hgetAll", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, isHit));
            }
        }

        return ret;
    }

    public Set<String> getFieldsFromMap(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String isHit = NOHIT;

        Set<String> ret = null;

        try {
            ret = remoteCacheClient.getFieldsFromMap(key);
            if (ret != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFieldsFromMap", "hkeys", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, isHit));
            }
        }

        return ret;
    }

    public <T extends Serializable> List<T> getFromSortedSet(String key, Long start, Long end) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String isHit = NOHIT;

        List<T> list = null;

        try {
            list = remoteCacheClient.getFromSortedSet(key, start, end);
            if (list != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFromSortedSet", "zrange", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, isHit));
            }
        }

        return list;
    }

    public <T extends Serializable> List<T> getFromSortedSetByScore(String key, Double min, Double max) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String isHit = NOHIT;

        List<T> list = null;

        try {
            list = remoteCacheClient.getFromSortedSetByScore(key, min, max);
            if (list != null) {
                isHit = HIT;
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getFromSortedSetByScore", "zrangeByScore", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, isHit));
            }
        }

        return list;
    }

    /**
     * 根据Key配置，获取远程Cache客户端
     *
     * @param cacheConfig 配置
     * @return 远程Cache客户端
     */
    private RemoteCacheClient getRemoteCacheClient(CacheConfig cacheConfig) {
        // 处理Remote缓存
        String remoteCacheType = cacheConfig.getRemoteCacheType();

        //非缓存魔方方式不允许地址为空
        if (StringUtils.isBlank(cacheConfig.getAddrList())) {
            // 表示是LOCAL类型
            return null;
        }


        RemoteCacheClientFactory remoteCacheClientFactory = RemoteCacheClientFactory.getInstance(remoteCacheType);
        RemoteCacheClient remoteCacheClient = remoteCacheClientFactory.getCacheClient(cacheConfig);

        if (tryTimes > 1) {
            // 如果尝试次数大于1，构造可重试缓存客户端
            remoteCacheClient = new RetryableRemoteCacheClient(remoteCacheClient, tryTimes);
        }
        return remoteCacheClient;
    }

    private Long getRemoteVersion(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.getRemoteVersion(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getRemoteVersion", "get", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    /**
     * 获取某Key的同步监听器
     *
     * @param key 键
     * @return 同步监听器
     */
    public SyncCacheListener getSyncCacheListener(String key) {
        key = trimKey(key);
        key = getKeyPrefix(key);
        return syncCacheListeners.get(key);
    }

    /**
     * 获取同步Key
     *
     * @param keyProperty
     * @param remoteVersion
     * @return SynchronizableKey
     */
    private SynchronizableKey getSyncKey(String key, KeyProperty keyProperty, Long remoteVersion, String saveType) {
        SynchronizableKey synchronizableKey = new SynchronizableKey();
        synchronizableKey.setCacheKey(key);
        synchronizableKey.setLocalCacheGroup(keyProperty.getLocalCacheGroup());
        synchronizableKey.setObjType(keyProperty.getObjType());
        synchronizableKey.setRemoteVersion(remoteVersion);
        synchronizableKey.setSaveType(saveType);

        return synchronizableKey;
    }

    public Integer getTryTimes() {
        return tryTimes;
    }

    private <T> ValueWithVersion<T> getWithVersion(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        ValueWithVersion<T> obj = null;

        try {
            obj = remoteCacheClient.getWithVersion(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getWithVersion", "get,get", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return obj;
    }

    public Long incrBy(String key, Long step) {
        // 按CCMS配置重置失效时间
        return incrBy(key, step, true);
    }

    public Long incrBy(String key, Long step, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);
        Long ret = -1L;

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            ret = remoteCacheClient.incrBy(key, step, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("incrBy", "incrBy", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long incrByInMap(String key, String field, Long step) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);
        Long ret = -1L;

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            ret = remoteCacheClient.incrByInMap(key, field, step);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("incrByInMap", "hincrBy", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    /**
     * 初始化缓存同步器
     */
    private void initCacheSynchronizers() {
        try {
            CacheSynchronizerFactory.getCacheSynchronizer(CacheConstant.CACHE_SYNCHRONIZER_TYPE_HOWBUYMESSAGE);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    public <T> List<T> list(String key, Long amount) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        List<T> list = new ArrayList<T>();

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            list = remoteCacheClient.list(key, amount);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("list", "lrange", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return list;
    }

    @Override
    public <T> boolean put(Integer expire, String key, T value) {
        return put(expire, key, value, null);
    }

    @Override
    public <T extends Serializable> List<T> mget4SamePrefixKeys(String... keys) throws MgetDataException {
        final String keyPreFix = preCheckAndGetKeyPrefix(keys);
        CacheConfig cacheConfig = getKeyOfCacheConfig(keyPreFix);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        long begin = System.currentTimeMillis();
        List<T> values = null;
        String executeResult = SUCC;
        try {
            values = remoteCacheClient.mget(keyPreFix, keys);
        } catch (Exception e) {
            executeResult = FAIL;
            throw new MgetDataException(e);
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("mget4SamePrefixKeys", "mget4SamePrefixKeys", ArrayUtils.toString(keys), spendTime,
                        cacheConfig.getAddrList(), executeResult, NONSUPPORT));
            }
        }
        return values;
    }

    /**
     * 断言非shards
     *
     * @param cacheConfig
     */
    private void assertIsNotShard(CacheConfig cacheConfig) {
        if (CLUSTER_TYPE_SHARDS.equals(cacheConfig.getClusterType())) {
            final String errMsg = String.format("clusterType is not allow %s", cacheConfig.getClusterType());
            throw new MgetDataException(errMsg);
        }
    }

    /**
     * 合法校验并返回key前缀
     *
     * @param keys
     * @return
     */
    private String preCheckAndGetKeyPrefix(String[] keys) {
        if (ArrayUtils.isEmpty(keys)) {
            throw new MgetDataException("keys not allow empty");
        }
        if (keys.length > MGET_MAX_SIZE) {
            throw new MgetDataException("keys size has to be under 500");
        }
        return checkIsSameKeyPrefixAndReturn(keys);
    }

    /**
     * 校验key数组中的key的前缀是否一致，并返回前缀
     *
     * @param keys
     * @return
     */
    private String checkIsSameKeyPrefixAndReturn(String[] keys) {
        final String baseKey = keys[0];
        if (StringUtils.isEmpty(baseKey)) {
            throw new MgetDataException("key not allow empty");
        }
        if (!baseKey.contains(CacheConstant.KEY_PREFIX_DELIMER)) {
            throw new MgetDataException("There must be a separator(|) between the prefix and the key");
        }
        final String baseKeyPrefix = keys[0].substring(0, keys[0].indexOf(CacheConstant.KEY_PREFIX_DELIMER) + 1);
        for (int keyIndex = 1; keyIndex < keys.length - 1; keyIndex++) {
            if (StringUtils.isEmpty(keys[keyIndex]) || !keys[keyIndex].startsWith(baseKeyPrefix)) {
                String errMsg = String.format("The prefix of the key is inconsistent cause baseKey:【%s】 checkKey:【%s】", baseKey, keys[keyIndex]);
                throw new MgetDataException(errMsg);
            }
        }
        return baseKeyPrefix;
    }

    public <T> boolean put(String key, T value) {
        return put(0, key, value, null);
    }


    private <T> boolean put(Integer targetExpire, String key, T value, Long version) {

        long begin = System.currentTimeMillis();
        boolean success = false;

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            Integer expire = targetExpire != null && targetExpire > 0 ? targetExpire : keyProperty.getExpire();

            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();
                String localCacheGroup = keyProperty.getLocalCacheGroup();

                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要存远端
                    // //// 写入远程缓存
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                    // 带备份的客户端
//                    remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                    // 装饰完毕，写入
                    if (version != null) {
                        // 传入了version，才带version写入
                        success = remoteCacheClient.putWithVersion(key, expire, value, version);
                    } else {
                        // 未传入version，直接写入，并重试
                        success = remoteCacheClient.put(key, expire, value);
                    }
                    if (success) {
                        logger.debug("Successfully put value {} into REMOTE cache, identified by key {}.", value, key);
                    } else {
                        logger.debug("Failed to put value {} into REMOTE cache, identified by key {}.", value, key);

                        // 写远端失败，退出，不再写本地
                        return false;
                    }

                    // //// 写入本地缓存
                    // 取得Key的版本
                    Long remoteVersion = remoteCacheClient.getRemoteVersion(key);
                    // 同步Key
                    String syncKey = getSyncKey(key, keyProperty, remoteVersion, CacheConstant.SAVE_TYPE_BYTES).toString();
                    try {
                        // 缓存中的版本信息
                        ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                        logger.debug("Successfully put value {} into LOCAL cache, identified by key {}.", value, key);
                    } catch (Exception ex) {
                        logger.error(
                                "Failed to put value " + value + " into LOCAL cache, identified by key " + key + "\n" + ex.getMessage(), ex);
                    }
                    // 更新本地缓存；
                    ehCacheClient.put(localCacheGroup, key, value);
                    // 同步到其他节点的本地缓存；此处通知其它节点需要放在本地更新后再通知
                    localCacheSync(key, localCacheGroup, syncKey);
                } else {
                    // 支持单一本地缓存
                    ehCacheClient.put(localCacheGroup, key, value);
                }

            } else {
                // //// 写入远程缓存，无备份
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                // 装饰完毕，写入
                if (version != null) {
                    // 传入了version，才带version写入
                    success = remoteCacheClient.putWithVersion(key, expire, value, version);
                } else {
                    // 未传入version，直接写入，并重试
                    success = remoteCacheClient.put(key, expire, value);
                }

            }
        } catch (RuntimeException e) {
            throw e;
        }

        return success;
    }

    private <T> boolean put(String key, T value, Long version) {
        return put(0, key, value, version);
    }

    public <T> boolean putIfAbsent(String key, T value) {
        boolean success = false;

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);


        try {
            Integer expire = keyProperty.getExpire();

            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                String localCacheGroup = keyProperty.getLocalCacheGroup();

                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要存远端
                    // //// 写入远程缓存
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                    // 带备份的客户端
//                    remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                    success = remoteCacheClient.putIfAbsent(key, value);
                    if (success) {
                        // 成功则设置过期
                        remoteCacheClient.expires(key, expire);
                    }

                    if (success) {
                        logger.debug("Successfully put value {} into REMOTE cache, identified by key {}.", value, key);
                    } else {
                        logger.debug("Failed to put value {} into REMOTE cache, identified by key {}.", value, key);

                        // 写远端失败，退出，不再写本地
                        return false;
                    }

                    // //// 写入本地缓存
                    // 取得Key的版本
                    Long remoteVersion = remoteCacheClient.getRemoteVersion(key);

                    // 同步Key
                    String syncKey = getSyncKey(key, keyProperty, remoteVersion, CacheConstant.SAVE_TYPE_BYTES).toString();
                    EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();
                    try {
                        // 缓存中的版本信息
                        ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                        logger.debug("Successfully put value {} into LOCAL cache, identified by key {}.", value, key);
                    } catch (Exception ex) {
                        logger.error("Failed to put value " + value + " into LOCAL cache, identified by key " + key + "\n" + ex.getMessage(), ex);
                    }
                    //修改本地缓存
                    ehCacheClient.put(localCacheGroup, key, value);
                    //同步到其他节点的本地缓存；此处通知其它节点需要放在本地更新后再通知
                    localCacheSync(key, localCacheGroup, syncKey);

                } else {
                    //不支持单一本地缓存
                    return false;
                }

            } else {
                // //// 写入远程缓存，无备份
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                // 装饰完毕，写入
                success = remoteCacheClient.putIfAbsent(key, value);
                if (success) {
                    // 成功则设置过期
                    remoteCacheClient.expires(key, expire);
                }
            }
        } catch (RuntimeException e) {
            throw e;
        }

        return success;
    }

    /**
     * 写入缓存
     *
     * @param key   键
     * @param value 值
     * @return 远端缓存，失败返回-1
     */
    private <T> Long putAndReplyVersion(String key, T value) {
        boolean result = put(key, value);
        if (result) {
            return getRemoteVersion(key);
        } else {
            return CacheConstant.KEY_VERSION_DEFAULT;
        }
    }

    public <T> void putToList(String key, Map<Long, T> map) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            remoteCacheClient.putToList(key, map);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putToList", "lset", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }
    }

    public String putToMap(String key, Map<String, String> map) {
        // 默认按CCMS配置重置失效时间
        return putToMap(key, map, true);
    }

    public String putToMap(String key, Map<String, String> map, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String ret = null;

        try {
            ret = remoteCacheClient.putToMap(key, map, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putToMap", "hmset,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long putObjToMap(String key, String field, Object obj) {
        // 默认按CCMS配置重置失效时间
        return putObjToMap(key, field, obj, true);
    }

    public Long putObjToMap(String key, String field, Object obj, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.putObjToMap(key, field, obj, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putObjToMap", "hset,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long putToMap(String key, String field, String obj) {
        // 默认按CCMS配置重置失效时间
        return putToMap(key, field, obj, true);
    }

    public Long putToMap(String key, String field, String obj, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.putToMap(key, field, obj, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putToMap", "hset,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public <T extends Serializable> Long putToSortedSet(String key, Long score, T obj) {
        // 默认按CCMS重置过期时间
        return putToSortedSet(key, score, obj, true);
    }

    public <T extends Serializable> Long putToSortedSet(String key, Long score, T obj, boolean reExpire) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.putToSortedSet(key, score, obj, getExpire(key, reExpire));
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putToSortedSet", "zadd,expires", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public <T extends Serializable> Long getRankFromSortedSet(String key, T obj) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.getRankFromSortedSet(key, obj);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getRankFromSortedSet", "zrank", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public void remove(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();
                // 删除本地，同步到其他结点
                String localCacheGroup = keyProperty.getLocalCacheGroup();
                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要删远端
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                    remoteCacheClient.remove(key);
                    // 删除本地
                    ehCacheClient.remove(localCacheGroup, key);
                    //同步到其他节点的本地缓存；此处通知其它节点需要放在本地更新后再通知
                    // 同步Key
                    SynchronizableKey synchronizableKey = getSyncKey(key, keyProperty, null, CacheConstant.SAVE_TYPE_STR);
                    synchronizableKey.setOperate(CacheConstant.REMOVE);
                    String syncKey = synchronizableKey.toString();
                    // //// 同步到其他节点的本地缓存
                    localCacheSync(key, localCacheGroup, syncKey);
                } else {
                    ehCacheClient.remove(localCacheGroup, key);
                }

            } else {
                // 删除远端
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                remoteCacheClient.remove(key);
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("remove", "del", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

    }

    public Long removeFromMap(String key, List<String> fields) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.removeFromMap(key, fields);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("removeFromMap", "hdel", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long removeFromMap(String key, String field) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.removeFromMap(key, field);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("removeFromMap", "hdel", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long removeFromSortedSetByRank(String key, Long start, Long end) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.removeFromSortedSetByRank(key, start, end);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("removeFromSortedSetByRank", "zremrangeByRank", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public Long removeFromSortedSetByScore(String key, Double start, Double end) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long ret = null;

        try {
            ret = remoteCacheClient.removeFromSortedSetByScore(key, start, end);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("removeFromSortedSetByScore", "zremrangeByScore", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return ret;
    }

    public void removeSyncCacheListener(String key) {
        key = trimKey(key);
        key = getKeyPrefix(key);
        syncCacheListeners.remove(key);
    }

    public void setTryTimes(Integer tryTimes) {
        this.tryTimes = tryTimes;
    }

    public <T> List<T> take(String key, Long amount) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        List<T> list = new ArrayList<T>();

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            list = remoteCacheClient.take(key, amount);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("take", "lpop", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return list;
    }

    public <T> List<T> takeWithThrowExcp(String key, Long amount) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        List<T> list = new ArrayList<T>();

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            list = remoteCacheClient.takeWithThrowExcp(key, amount);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("take", "lpop", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return list;
    }

    /**
     * 如果是前缀型的，去除空格
     *
     * @param key 键
     * @return 去空后的键
     */
    private String trimKey(String key) {
        if (key.indexOf(CacheConstant.KEY_PREFIX_DELIMER) > 0) {
            // 是带前缀的，去除空格
            key = key.replace("\\s+", "_");
        }

        return key;
    }

    public boolean initializeKey(String key) {
        try {
            KeyProcessor.getInstance().getKeyProperty(key);
            return true;
        } catch (Exception ex) {
            logger.error("Error initializing key " + key, ex);
            return false;
        }
    }

    @Override
    public boolean putStr(Integer expire, String key, String value) {
        return putStr(expire, key, value, null);
    }

    public boolean putStr(String key, String value) {
        return putStr(0, key, value, null);
    }

    private boolean putStr(Integer targetExpire, String key, String value, Long version) {

        boolean success = false;

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            Integer expire = targetExpire != null && targetExpire > 0 ? targetExpire : keyProperty.getExpire();

            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                String localCacheGroup = keyProperty.getLocalCacheGroup();
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();

                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要存远端
                    // //// 写入远程缓存
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                    // 带备份的客户端
//                    remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                    // 装饰完毕，写入
                    if (version != null) {
                        // 传入了version，才带version写入
                        success = remoteCacheClient.putStrWithVersion(key, expire, value, version);
                    } else {
                        // 未传入version，直接写入，并重试
                        success = remoteCacheClient.putStr(key, expire, value);
                    }
                    if (success) {
                        logger.debug("Successfully put value {} into REMOTE cache, identified by key {}.", value, key);
                    } else {
                        logger.debug("Failed to put value {} into REMOTE cache, identified by key {}.", value, key);

                        // 写远端失败，退出，不再写本地
                        return false;
                    }

                    // //// 写入本地缓存
                    // 取得Key的版本
                    Long remoteVersion = remoteCacheClient.getRemoteVersion(key);

                    // 同步Key
                    String syncKey = getSyncKey(key, keyProperty, remoteVersion, CacheConstant.SAVE_TYPE_STR).toString();
                    try {
                        // 缓存中的版本信息
                        ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                        logger.debug("Successfully put value {} into LOCAL cache, identified by key {}.", value, key);
                    } catch (Exception ex) {
                        logger.error(
                                "Failed to put value " + value + " into LOCAL cache, identified by key " + key + "\n" + ex.getMessage(), ex);
                    }
                    //本地缓存更新
                    ehCacheClient.put(localCacheGroup, key, value);
                    //同步到其他节点的本地缓存；此处通知其它节点需要放在本地更新后再通知
                    localCacheSync(key, localCacheGroup, syncKey);
                } else {
                    //仅支持本地缓存
                    ehCacheClient.put(localCacheGroup, key, value);
                }

            } else {
                // //// 写入远程缓存，无备份
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                // 装饰完毕，写入
                if (version != null) {
                    // 传入了version，才带version写入
                    success = remoteCacheClient.putStrWithVersion(key, expire, value, version);
                } else {
                    // 未传入version，直接写入，并重试
                    success = remoteCacheClient.putStr(key, expire, value);
                }
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putStr", "set,incr", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return success;
    }

    private boolean putStr(String key, String value, Long version) {
        return putStr(0, key, value, version);
    }

    public boolean putStrIfAbsent(String key, String value) {
        boolean success = false;

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        try {
            Integer expire = keyProperty.getExpire();

            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                String localCacheGroup = keyProperty.getLocalCacheGroup();
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();

                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要存远端
                    // //// 写入远程缓存
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
                    // 带备份的客户端
//                    remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                    success = remoteCacheClient.putStrIfAbsent(key, value);

                    if (success) {
                        // 成功则设置过期
                        remoteCacheClient.expires(key, expire);
                        logger.debug("Successfully put value {} into REMOTE cache, identified by key {}.", value, key);
                    } else {
                        logger.debug("Failed to put value {} into REMOTE cache, identified by key {}.", value, key);

                        // 写远端失败，退出，不再写本地
                        return false;
                    }

                    // //// 写入本地缓存
                    // 取得Key的版本
                    Long remoteVersion = remoteCacheClient.getRemoteVersion(key);

                    // 同步Key
                    String syncKey = getSyncKey(key, keyProperty, remoteVersion, CacheConstant.SAVE_TYPE_BYTES).toString();
                    try {
                        // 缓存中的版本信息
                        ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                        logger.debug("Successfully put value {} into LOCAL cache, identified by key {}.", value, key);
                    } catch (Exception ex) {
                        logger.error(
                                "Failed to put value " + value + " into LOCAL cache, identified by key " + key + "\n" + ex.getMessage(), ex);
                    }
                    //更新本地缓存
                    ehCacheClient.put(localCacheGroup, key, value);
                    // 同步到其他节点的本地缓存
                    localCacheSync(key, localCacheGroup, syncKey);
                } else {
                    //不支持单一本地缓存
                    return false;
                }
            } else {
                // //// 写入远程缓存，无备份
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                // 装饰完毕，写入
                success = remoteCacheClient.putStrIfAbsent(key, value);
                if (success) {
                    // 成功则设置过期
                    remoteCacheClient.expires(key, expire);
                }

            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putStrIfAbsent", "setnx,incr", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return success;
    }

    /**
     * added by william.hu for iteration version 1.1.0
     */
    public List<String> mgetStr(String... keys) {
        List<String> results = new ArrayList<String>();
        for (String key : keys) {
            results.add(getStr(key, null));
        }

        return results;
    }

    public String getStr(String key) {
        return getStr(key, null);
    }

    private String getStr(String key, Long fromVersion) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        String value = null;

        try {
            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();
                // 读取本地缓存
                String localCacheGroup = keyProperty.getLocalCacheGroup();

                if (cacheConfig.getAddrList() == null || cacheConfig.getAddrList().isEmpty()) {
                    // 直接取本地缓存
                    value = (String) ehCacheClient.get(localCacheGroup, key);
                } else {
                    if (!earlierThanVersion(localCacheGroup, key, fromVersion)) {
                        // 本地缓存的版本新于指定版本，取本地缓存
                        value = (String) ehCacheClient.get(localCacheGroup, key);
                    }

                    if (value == null) {
                        synchronized (key) {
                            if (!earlierThanVersion(localCacheGroup, key, fromVersion)) {
                                // 本地缓存的版本新于指定版本，取本地缓存
                                value = (String) ehCacheClient.get(localCacheGroup, key);
                            }

                            // 需要双重检查
                            if (value == null) {
                                // 带备份的远程读取
                                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);
//                                remoteCacheClient = new MasterSlaveRemoteCacheClient(remoteCacheClient, slaveClient, cacheConfig);

                                value = remoteCacheClient.getStr(key);
                                ehCacheClient.put(localCacheGroup, key, value);
                                // 缓存中的版本信息
                                // 取得Key的版本
                                Long remoteVersion = remoteCacheClient.getRemoteVersion(key);
                                ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                            }
                        }
                    }

                    // 用到这个Key，才加载其（所在本地组的）消息监听器
                    CacheSynchronizer cacheSynchronizer = keyProperty.getCacheSynchronizerVO();
                    cacheSynchronizer.addReceiveListener(localCacheGroup);
                }
            } else {
                // 读取远程缓存，无备份
                value = new ScalableRemoteCacheClient(remoteCacheClient).getStr(key);
            }
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("getStr", "get", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return value;
    }

    public Long countInMap(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.countInMap(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("countInMap", "hlen", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Long countInList(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.countInList(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("countInList", "llen", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Long countInSet(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.countInSet(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("countInSet", "scard", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Long countInSortedSet(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.countInSortedSet(key);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("countInSortedSet", "zcard", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Long putToSet(String key, String value) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.putToSet(key, value);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("putToSet", "sadd", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Set<String> keys(String keyPattern) {
        // 去掉*，得到key
        String key = keyPattern.replaceAll("\\*", "");
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Set<String> result = null;

        try {
            result = remoteCacheClient.keys(keyPattern);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("keys", "keys", key, spendTime, cacheConfig.getAddrList(),
                        excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    public Long incrAndExpire(String key, Long step, Integer expireSeconds) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        long begin = System.currentTimeMillis();
        String excuteResult = SUCC;

        Long result = null;

        try {
            result = remoteCacheClient.incrAndExpire(key, step, expireSeconds);
        } catch (RuntimeException e) {
            excuteResult = FAIL;
            throw e;
        } finally {
            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisLogger.info(RedisLogGenerator.generateConsumerLog("incrAndExpire", "add,evalsha", key, spendTime,
                        cacheConfig.getAddrList(), excuteResult, NONSUPPORT));
            }
        }

        return result;
    }

    private CacheConfig getKeyOfCacheConfig(String key) {
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);
        if (null == keyProperty) {
            logger.error("key {} is not define", key);
            throw new IllegalArgumentException(key + " ,key is not define");
        }
        return keyProperty.getCacheConfig();
    }

    /**
     * 本地缓存消息通知
     */
    private void localCacheSync(String key, String localCacheGroup, String syncKey) {
        // //// 同步到其他节点的本地缓存
        try {
            CacheConfig cacheConfig = getKeyOfCacheConfig(key);
            KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

            long begin = System.currentTimeMillis();

            CacheSynchronizer cacheSynchronizer = keyProperty.getCacheSynchronizerVO();

            cacheSynchronizer.addReceiveListener(localCacheGroup);
            cacheSynchronizer.syncCacheKey(syncKey);
            logger.info("Successfully sent message to synchronize value identified by key {}, to other appservers.", key);

            long spendTime = System.currentTimeMillis() - begin;
            if (cacheConfig.hasTimeout() && spendTime >= cacheConfig.getTimeout()) {
                redisMQLogger.info(RedisLogGenerator.generateConsumerLog(null, null, key, spendTime, cacheConfig.getAddrList(), null, null));
            }
        } catch (Exception ex) {
            logger.error("Failed to send message to synchronize value identified by key {}, to other appservers.", key);
        }
    }

    @Override
    public Long ttl(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.ttl(key);
        } catch (RuntimeException e) {
            throw e;
        }

    }

    @Override
    public Long bitCount(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.bitCount(key);
        } catch (RuntimeException e) {
            throw e;
        }

    }

    @Override
    public Long bitCount(String key, long start, long end) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.bitCount(key, start, end);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public Boolean setBit(String key, long offset, boolean value) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.setBit(key, offset, value);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public Boolean setBit(String key, long offset, String value) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.setBit(key, offset, value);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public Boolean getBit(String key, long offset) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.getBit(key, offset);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    public <T extends Serializable> boolean lock(String key, T value) {

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            Integer expire = keyProperty.getExpire();

            // //// 写入远程缓存，无备份
            // 区分大对象与普通对象的客户端
            remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

            // 装饰完毕，写入
            return remoteCacheClient.lock(key, value, expire);

        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public Set<String> smembers(String key) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.smembers(key);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public Long srem(String key, String member) {
        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            return remoteCacheClient.srem(key, member);
        } catch (RuntimeException e) {
            throw e;
        }
    }

    @Override
    public <T extends Serializable> boolean put(String key, T value, String nxxx, int secondTime) {

        long begin = System.currentTimeMillis();
        boolean success = false;

        CacheConfig cacheConfig = getKeyOfCacheConfig(key);
        RemoteCacheClient remoteCacheClient = getRemoteCacheClient(cacheConfig);
        KeyProperty keyProperty = KeyProcessor.getInstance().getKeyProperty(key);

        // 如果是前缀型的，去除空格
        key = trimKey(key);

        try {
            // int expire = secondTime > 0 ? secondTime : keyProperty.getExpire();
            // 三目转换为if else 防止keyProperty.getExpire()出现空指针异常
            int expire = 0;
            if (secondTime > 0) {
                expire = secondTime;
            } else {
                Integer expire1 = keyProperty.getExpire();
                if (expire1 != null) {
                    expire = expire1;
                }
            }
            //时间单位，EX为秒，PX为毫秒 ，默认设置为秒
            String expx = "EX";

            // 处理本地缓存
            boolean needLocalCache = cacheConfig.getNeedLocalCache();

            if (needLocalCache) {
                String localCacheGroup = keyProperty.getLocalCacheGroup();
                EhCacheClient ehCacheClient = EhCacheClientFactory.getLocalCacheClient();

                if (cacheConfig.getAddrList() != null && !cacheConfig.getAddrList().isEmpty()) {
                    // 需要存远端
                    // //// 写入远程缓存
                    // 区分大对象与普通对象的客户端
                    remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                    // 装饰完毕，写入
                    success = remoteCacheClient.put(key, value, nxxx, expx, expire);
                    if (success) {
                        logger.debug("Successfully put value {} into REMOTE cache, identified by key {}.", value, key);
                    } else {
                        logger.debug("Failed to put value {} into REMOTE cache, identified by key {}.", value, key);

                        // 写远端失败，退出，不再写本地
                        return false;
                    }

                    // //// 写入本地缓存
                    // 取得Key的版本
                    Long remoteVersion = remoteCacheClient.getRemoteVersion(key);

                    // 同步Key
                    String syncKey = getSyncKey(key, keyProperty, remoteVersion, CacheConstant.SAVE_TYPE_BYTES).toString();
                    try {
                        // 缓存中的版本信息
                        ehCacheClient.put(localCacheGroup, CacheConstant.SYNC_PREFIX + key, remoteVersion);
                        logger.debug("Successfully put value {} into LOCAL cache, identified by key {}.", value, key);
                    } catch (Exception ex) {
                        logger.error(
                                "Failed to put value " + value + " into LOCAL cache, identified by key " + key + "\n" + ex.getMessage(), ex);
                    }
                    //更新缓存
                    ehCacheClient.put(localCacheGroup, key, value);
                    // 同步到其他节点的本地缓存
                    localCacheSync(key, localCacheGroup, syncKey);
                } else {
                    //不支持单一本地缓存
                    return false;
                }


            } else {
                // //// 写入远程缓存，无备份
                // 区分大对象与普通对象的客户端
                remoteCacheClient = new ScalableRemoteCacheClient(remoteCacheClient);

                success = remoteCacheClient.put(key, value, nxxx, expx, expire);

            }
        } catch (RuntimeException e) {
            throw e;
        }

        return success;
    }


    @Override
    public HealthCheckResult healthCheck() {
        List<HealthFailDetail> detailList = new ArrayList<>();
        try {
            Map<String, CacheConfig> allCacheConfig = ConfigProcessor.getInstance().getAllCacheConfig();
            if (Objects.nonNull(allCacheConfig)) {
                Set<String> set = allCacheConfig.keySet();
                set.stream().forEach(key -> {

                    CacheConfig cacheConfig = allCacheConfig.get(key);
                    try {
                        RemoteCacheClient remoteCacheClient = this.getRemoteCacheClient(cacheConfig);
                        if (remoteCacheClient != null) {
                            Boolean executeResult = remoteCacheClient.executePing(cacheConfig);
                            if (!executeResult) {
                                detailList.add(new HealthFailDetail(key, cacheConfig.getAddrList()));
                            }
                        }
                    } catch (Exception e) {
                        detailList.add(new HealthFailDetail(key, cacheConfig.getAddrList()));
                        logger.error("redis地址：{},healthCheck error===", cacheConfig.getAddrList(), e);
                    }
                });
            }
        } catch (Exception e) {
            logger.error("healthCheck fail====", e);
            throw new RuntimeException(e);
        }
        return new HealthCheckResult(detailList);
    }
}
