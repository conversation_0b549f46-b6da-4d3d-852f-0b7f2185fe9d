/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.querycancelorderlist;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.date.DateUtil;
import com.howbuy.interlayer.product.model.HighProductCanBuyInfoModel;
import com.howbuy.tms.cache.service.highquota.HighQuotaBeanNew;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.CanCancelFlagEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListResponse.CancelOrderBean;
import com.howbuy.tms.high.orders.service.business.fundbuystatus.FundBuyStatusService;
import com.howbuy.tms.high.orders.service.business.querymeragesubmit.QueryMergeSubmitOrderService;
import com.howbuy.tms.high.orders.service.task.QueryCanCancelTask;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusListParam;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(查询高端可撤单订单列表)
 * @date 2017年4月25日 下午1:42:34
 * @since JDK 1.6
 */
@DubboService
@Service("queryCancelOrderListFacade")
public class QueryCancelOrderListFacadeService implements QueryCancelOrderListFacade {
    private static final Logger logger = LogManager.getLogger(QueryCancelOrderListFacadeService.class);

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryMergeSubmitOrderService queryMergeSubmitOrderService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private FundBuyStatusService fundBuyStatusService;

    private static final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade.execute(QueryCancelOrderListRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryCancelOrderListFacadeService
     * @apiName execute
     * @apiDescription 查询高端可撤单订单列表
     * @apiParam (请求参数) {String} taTradeDt
     * @apiParam (请求参数) {String} isAuth
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=K&taTradeDt=jrcT8mqKy&pageSize=2625&disCode=us&txChannel=R1NryqK&appTm=1&isAuth=uqJmjZ&subOutletCode=AufuD2PN&pageNo=5524&operIp=bSRljRupyy&txAcctNo=uF&appDt=aYsz&dataTrack=s3b&txCode=3dCcGDwu&outletCode=yiifuHK
     * @apiSuccess (响应结果) {String} hasHzProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHkProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} cancelOrderList 撤单列表
     * @apiSuccess (响应结果) {String} cancelOrderList.dealNo 客户订单号
     * @apiSuccess (响应结果) {String} cancelOrderList.disCode 分销机构
     * @apiSuccess (响应结果) {String} cancelOrderList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} cancelOrderList.fundBuyStatus 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
     * @apiSuccess (响应结果) {String} cancelOrderList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} cancelOrderList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} cancelOrderList.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} cancelOrderList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} cancelOrderList.bankName 银行名称
     * @apiSuccess (响应结果) {String} cancelOrderList.productName 产品名称
     * @apiSuccess (响应结果) {String} cancelOrderList.productCode 产品代码
     * @apiSuccess (响应结果) {String} cancelOrderList.paymentType 支付方式
     * @apiSuccess (响应结果) {Number} cancelOrderList.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} cancelOrderList.appVol 申请份额
     * @apiSuccess (响应结果) {Number} cancelOrderList.appRatio 申请比例
     * @apiSuccess (响应结果) {Number} cancelOrderList.appDtm 申请日期时间
     * @apiSuccess (响应结果) {String} cancelOrderList.payStatus 付款状态
     * @apiSuccess (响应结果) {String} cancelOrderList.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} cancelOrderList.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {Number} cancelOrderList.fee 手续费
     * @apiSuccess (响应结果) {String} cancelOrderList.mBusiCode 中台业务码
     * @apiSuccess (响应结果) {String} cancelOrderList.firstBuyFlag 首次购买标识
     * @apiSuccess (响应结果) {String} cancelOrderList.redeemDirection 赎回去向
     * @apiSuccess (响应结果) {String} cancelOrderList.mergeSubmitFlag 合并上报标识 1-合并上报
     * @apiSuccess (响应结果) {String} cancelOrderList.txChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；5-App
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"Rhj","cancelOrderList":[{"appAmt":5390.***********,"fee":1764.*************,"mergeSubmitFlag":"8ej","bankAcct":"MSzKc","orderStatus":"0","taTradeDt":"y6yAN","bankName":"Cz","disCode":"LIVr","txChannel":"V","mBusiCode":"JEc","productName":"yQ4N","paymentType":"S","redeemDirection":"U7dC9","appVol":542.*************,"txAcctNo":"YE86xSkTbV","appDtm":*************,"cpAcctNo":"QiYR","hkSaleFlag":"M","firstBuyFlag":"XznQ","bankCode":"L7Dbf2M5O","dealNo":"V1MMF","productCode":"yebs","appRatio":4969.************,"payStatus":"i4ORb6frO","fundBuyStatus":"H8C"}],"hasHzProduct":"qq4O","hasHkProduct":"S524A3CL","totalPage":560,"pageNo":8668,"description":"nz0CtqMc","totalCount":9767}
     */
    @Override
    public QueryCancelOrderListResponse execute(QueryCancelOrderListRequest request) {
        QueryCancelOrderListResponse resp = new QueryCancelOrderListResponse();
        List<CancelOrderBean> cancelOrderList = new ArrayList<CancelOrderBean>();
        resp.setCancelOrderList(cancelOrderList);
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        // 是否授权
        String isAuth = StringUtils.isEmpty(request.getIsAuth()) ? YesOrNoEnum.YES.getCode() : request.getIsAuth();
        String txAcctNo = request.getTxAcctNo();
        Date currDate = new Date();
        logger.info("currDate :{}", DateUtils.formatToString(currDate, DateUtils.YYYYMMDDHHMMSS));

        List<DealOrderVo> canCancelVoList = dealOrderRepository.selectCancelOrderList(currDate, txAcctNo);
        // 处理合并上报单汇总信息
        queryMergeSubmitOrderService.processMergeSubmitOrder(canCancelVoList);

        if (CollectionUtils.isEmpty(canCancelVoList)) {
            logger.info("currDate, canCancelVoList is empty");
            return resp;
        }
        logger.info("canCancelVoList size:{}", canCancelVoList.size());

        CountDownLatch canCancelLatch = new CountDownLatch(canCancelVoList.size());
        for (DealOrderVo dealOrderVo : canCancelVoList) {
            CommonThreadPool.submit(new QueryCanCancelTask(queryHighProductOuterService, dealOrderVo, currDate, canCancelLatch));
        }

        try {
            canCancelLatch.await();
        } catch (InterruptedException e) {
            logger.error("QueryCanCancelTask|latch.await exception:", e);
            Thread.currentThread().interrupt();
        }

        // 查询产品是否香港产品
        List<String> fundCodeList = canCancelVoList.stream().map(DealOrderVo::getProductCode).distinct().collect(Collectors.toList());
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        // 构建可撤单列表
        List<CancelOrderBean> cancelOrderBeanList = buildCancelList(resp, isAuth, canCancelVoList, highProductDbInfoBeanMap);
        // 购买状态添加
        setFundBuyStatus(request, cancelOrderBeanList);
        resp.setCancelOrderList(cancelOrderBeanList);

        return resp;

    }

    /**
     * 查询购买状态
     *
     * @param request             请求入参
     * @param cancelOrderBeanList 可取消列表
     */
    private void setFundBuyStatus(QueryCancelOrderListRequest request, List<CancelOrderBean> cancelOrderBeanList) {
        if (CollectionUtils.isEmpty(cancelOrderBeanList)) {
            logger.info("QueryCancelOrderListFacadeService-setFundBuyStatus-没有可撤销订单");
            return;
        }
        String taTradeDt = queryTradeDayOuterService.getWorkDay(DateUtil.getAppDt(), DateUtil.getAppDt());
        // 按照分销渠道区分处理
        Map<String, List<CancelOrderBean>> fundPartList = cancelOrderBeanList.stream().collect(Collectors.groupingBy(CancelOrderBean::getDisCode));
        // 查询客户信息
        QueryCustInfoResult custInfo = null;
        if (StringUtils.isNotEmpty(request.getTxAcctNo())) {
            custInfo = fundBuyStatusService.queryCustInfo(request.getTxAcctNo(), request.getDisCode());
        }

        // 获取直销黑名单
        List<String> blackFundCodes = new ArrayList<>();
        String hboneNo = null;
        if (custInfo != null && StringUtils.isNotEmpty(custInfo.getHboneNo())) {
            hboneNo = custInfo.getHboneNo();
            blackFundCodes = CACHE_SERVICE.get(CacheKeyPrefix.HIGH_CM_BLACK + custInfo.getHboneNo());
            if (blackFundCodes == null) {
                blackFundCodes = new ArrayList<>();
            }
        }

        List<String> fundCodes = cancelOrderBeanList.stream().map(CancelOrderBean::getProductCode).distinct().collect(Collectors.toList());

        // 查询所有产品信息
        String appDtm = request.getAppDt() + request.getAppTm();
        Map<String, HighProductCanBuyInfoModel> productInfoModelMap = queryHighProductOuterService.getHighProductAllInfoByCanBuy(fundCodes, hboneNo, appDtm, "0");
        List<HighQuotaBeanNew> highQuotaBeans = CACHE_SERVICE.mget4SamePrefixKeys(getBatchProductTotalKeys(fundCodes));
        Map<String, HighQuotaBeanNew> highQuotaBeanNewMap = highQuotaBeans.stream().filter(Objects::nonNull).collect(Collectors.toMap(HighQuotaBeanNew::getFundCode, highQuotaBeanNew -> highQuotaBeanNew));

        // 获取在途或者持仓产品列表 - 未走缓存
        List<String> ccOrZtList = fundBuyStatusService.getCcOrZtList(request.getTxAcctNo(), fundCodes);
        // 批处理
        List<FundBuyStatusDto> fundBuyStatusDtos = new ArrayList<>();
        for (List<CancelOrderBean> subFundCodeList : fundPartList.values()) {
            List<String> fundCodeList = subFundCodeList.stream().map(CancelOrderBean::getProductCode).distinct().collect(Collectors.toList());
            CancelOrderBean cancelOrderBean = subFundCodeList.get(0);
            QueryFundBuyStatusListParam queryFundBuyStatusListParam = new QueryFundBuyStatusListParam();
            queryFundBuyStatusListParam.setFundCodeList(fundCodeList);
            queryFundBuyStatusListParam.setDisCode(cancelOrderBean.getDisCode());
            queryFundBuyStatusListParam.setTxAcctNo(cancelOrderBean.getTxAcctNo());
            queryFundBuyStatusListParam.setAppDt(DateUtil.getAppDt());
            queryFundBuyStatusListParam.setAppTm(DateUtil.getAppTm());
            queryFundBuyStatusListParam.setAppointmentFlag(YesOrNoEnum.NO.getCode());
            queryFundBuyStatusListParam.setHighQuotaBeanNewMap(highQuotaBeanNewMap);
            queryFundBuyStatusListParam.setTaTradeDt(taTradeDt);
            queryFundBuyStatusListParam.setTxChannel(request.getTxChannel());
            queryFundBuyStatusListParam.setProductInfoModelMap(productInfoModelMap);
            queryFundBuyStatusListParam.setCustInfo(custInfo);
            queryFundBuyStatusListParam.setCcOrZtList(ccOrZtList);
            queryFundBuyStatusListParam.setBlackFundCodes(blackFundCodes);
            fundBuyStatusDtos = fundBuyStatusService.queryProcess(queryFundBuyStatusListParam);
        }
        Map<String, FundBuyStatusDto> fundBuyStatusDtoMap = fundBuyStatusDtos.stream().collect(Collectors.toMap(FundBuyStatusDto::getProductCode, x -> x));
        // 设置购买状态
        for (CancelOrderBean cancelOrderBean : cancelOrderBeanList) {
            FundBuyStatusDto fundBuyStatusDto = fundBuyStatusDtoMap.get(cancelOrderBean.getProductCode());
            if (fundBuyStatusDto != null) {
                cancelOrderBean.setFundBuyStatus(fundBuyStatusDto.getFundBuyStatusEnum().getStatus());
            } else {
                logger.error("QueryCancelOrderListFacadeService-setFundBuyStatus-查询产品购买状态为空,productCode={}", cancelOrderBean.getProductCode());
            }
        }
    }

    /**
     * 构建可撤单列表
     *
     * @param resp                     响应实体
     * @param isAuth                   是否已授权
     * @param canCancelVoList          订单集合
     * @param highProductDbInfoBeanMap 产品信息
     */
    private List<CancelOrderBean> buildCancelList(QueryCancelOrderListResponse resp, String isAuth, List<DealOrderVo> canCancelVoList, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap) {
        List<CancelOrderBean> cancelList = new ArrayList<CancelOrderBean>();
        CancelOrderBean cancelOrderBean;
        for (DealOrderVo dealOrderVo : canCancelVoList) {
            if (CanCancelFlagEnum.CAN.getCode().equals(dealOrderVo.getCanCancelFlag())) {
                cancelOrderBean = new CancelOrderBean();
                BeanUtils.copyProperties(dealOrderVo, cancelOrderBean);
                // 产品信息
                HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(dealOrderVo.getProductCode());
                cancelOrderBean.setHkSaleFlag(highProductDbInfoBean.getHkSaleFlag());
                // 非授权,就将香港+好臻的过滤掉
                if (YesOrNoEnum.NO.getCode().equals(isAuth)) {
                    boolean needFilter = false;
                    if (YesOrNoEnum.YES.getCode().equals(cancelOrderBean.getHkSaleFlag())) {
                        resp.setHasHkProduct(YesOrNoEnum.YES.getCode());
                        needFilter = true;
                    }
                    if (DisCodeEnum.HZ.getCode().equals(cancelOrderBean.getDisCode())) {
                        resp.setHasHzProduct(YesOrNoEnum.YES.getCode());
                        needFilter = true;
                    }
                    if (needFilter) {
                        continue;
                    }
                }
                cancelList.add(cancelOrderBean);
            }
        }
        return cancelList;
    }
    /**
     * 获取缓存的keys
     */
    private String[] getBatchProductTotalKeys(List<String> fundCodes) {
        return fundCodes.stream().map(this::geProductTotalKey).toArray(String[]::new);
    }

    private String geProductTotalKey(String fundCode) {
        return CacheKeyPrefix.MIDDLE_PRODUCT_SALE + "PRODUCTTOTALNEW_" + fundCode;
    }

}

