package com.howbuy.tms.high.orders.service.repository;

import com.howbuy.tms.high.orders.dao.mapper.customize.SubCustBooksPoMapper;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.dao.vo.SubCustBooksVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SubCustBooksRepository {
    @Autowired
    private SubCustBooksPoMapper subCustBooksPoMapper;

    public List<SubCustBooksPo> selectSubCustBooksByOpenRedeDt(List<String> disCodeList, String txAcctNo, String fundCode, String cpAcctNo, String openRedeDt) {
        return subCustBooksPoMapper.selectSubCustBooksByOpenRedeDt(disCodeList, txAcctNo, fundCode, cpAcctNo, openRedeDt);
    }

    public List<SubCustBooksPo> selectSubCustBooks(List<String> disCodeList, String txAcctNo, String fundCode, String openRedeDt, String balanceStatus) {
        return subCustBooksPoMapper.selectSubCustBooks(disCodeList, txAcctNo, fundCode, openRedeDt, balanceStatus);
    }

    public List<SubCustBooksPo> selectSubCustBooksByTxAcctNo(List<String> disCodeList, String txAcctNo, String fundCode, String cpAcctNo) {
        return subCustBooksPoMapper.selectSubCustBooksByTxAcctNo(disCodeList, txAcctNo, fundCode, cpAcctNo);
    }

    public List<SubCustBooksPo> selectSubCustBookSumByAckDt(List<String> disCodeList, String txAcctNo, String fundCode, String balanceStatus) {
        return subCustBooksPoMapper.selectSubCustBookSumByAckDt(disCodeList, txAcctNo, fundCode, balanceStatus);
    }

    public List<SubCustBooksPo> selectZhBalanceDtlWithOutSubBook(List<String> disCodeList, String txAcctNo, String productCode, String cpAcctNo) {
        return subCustBooksPoMapper.selectZhBalanceDtlWithOutSubBook(disCodeList, txAcctNo, productCode, cpAcctNo);
    }

    public List<SubCustBooksVo> selectAcctSubCustBooks(String txAcctNo, String fundCode) {
        return subCustBooksPoMapper.selectAcctSubCustBooks(txAcctNo, fundCode);
    }
}
