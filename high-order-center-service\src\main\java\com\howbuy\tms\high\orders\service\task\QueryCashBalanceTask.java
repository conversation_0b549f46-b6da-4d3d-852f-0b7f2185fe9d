package com.howbuy.tms.high.orders.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.request.cash.QueryCashBalanceByFundTxAcctRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.cash.QueryCashBalanceByFundTxAcctResponse;
import com.howbuy.dtms.order.client.facade.query.cashbalance.QueryCashBalanceByFundTxAcctFacade;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew.QueryBalanceContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * @Description:查询现金余额任务
 * @Author: yun.lu
 * Date: 2025/8/1 18:52
 */
public class QueryCashBalanceTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryCashBalanceTask.class);
    private static final String RMB = "156";
    private static final String HK_SUCCESS_CODE = "0000";
    private QueryAcctBalanceRequest request;
    private QueryBalanceContext queryBalanceContext;
    private Boolean hkProduct;
    private QueryCashBalanceByFundTxAcctFacade queryCashBalanceByFundTxAcctFacade;

    @Override
    protected void callTask() {
        if (StringUtils.isNotBlank(request.getNotFilterHkFund()) && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
            log.info("queryHkBalance-没有香港权限,不查询现金余额");
            return;
        }
        if (hkProduct != null && !hkProduct) {
            log.info("queryCashBalance-不是香港产品,不查询现金余额");
            return;
        }
        if (StringUtils.isBlank(request.getHbOneNo())) {
            log.info("queryCashBalance-没有一账通号,不查询现金余额");
            return;
        }
        QueryCashBalanceByFundTxAcctRequest queryCashBalanceByFundTxAcctRequest = new QueryCashBalanceByFundTxAcctRequest();
        queryCashBalanceByFundTxAcctRequest.setDisCurrency(RMB);
        queryCashBalanceByFundTxAcctRequest.setHboneNo(request.getHbOneNo());
        Response<QueryCashBalanceByFundTxAcctResponse> result = queryCashBalanceByFundTxAcctFacade.execute(queryCashBalanceByFundTxAcctRequest);
        log.info("queryCashBalance-查询现金余额-结果:response={}", JSON.toJSONString(result));
        if (!HK_SUCCESS_CODE.equals(result.getCode()) || result.getData() == null) {
            log.info("queryCashBalance-查询现金余额,返回非成功状态,当做没有海外持仓处理");
            return;
        }
        queryBalanceContext.setCashBalance(result.getData());
    }

    public QueryCashBalanceTask(QueryAcctBalanceRequest request,QueryBalanceContext queryBalanceContext, Boolean hkProduct, QueryCashBalanceByFundTxAcctFacade queryCashBalanceByFundTxAcctFacade) {
        this.request = request;
        this.queryBalanceContext = queryBalanceContext;
        this.hkProduct = hkProduct;
        this.queryCashBalanceByFundTxAcctFacade = queryCashBalanceByFundTxAcctFacade;
    }

    public QueryAcctBalanceRequest getRequest() {
        return request;
    }

    public void setRequest(QueryAcctBalanceRequest request) {
        this.request = request;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public Boolean getHkProduct() {
        return hkProduct;
    }

    public void setHkProduct(Boolean hkProduct) {
        this.hkProduct = hkProduct;
    }

    public QueryCashBalanceByFundTxAcctFacade getQueryCashBalanceByFundTxAcctFacade() {
        return queryCashBalanceByFundTxAcctFacade;
    }

    public void setQueryCashBalanceByFundTxAcctFacade(QueryCashBalanceByFundTxAcctFacade queryCashBalanceByFundTxAcctFacade) {
        this.queryCashBalanceByFundTxAcctFacade = queryCashBalanceByFundTxAcctFacade;
    }
}
