package com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:用户赎回日历信息
 * @Author: yun.lu
 * Date: 2024/10/9 16:36
 */
@Data
public class CustomerRedeemAppointInfo implements Serializable {
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * 产品简称
     */
    private String fundName;
    /**
     * 总份额
     */
    private BigDecimal totalVol;
    /**
     * 可用份额
     */
    private BigDecimal availVol;

    /**
     * 可赎回份额
     */
    private BigDecimal canRedeemVol;

    /**
     * 子账本明细id
     */
    private Long subCustBooksId;

    /**
     * 锁定份额
     */
    private BigDecimal lockVol = BigDecimal.ZERO;
    /**
     * 冻结份额
     */
    private BigDecimal frznVol;
    /**
     * 司法冻结份额
     */
    private BigDecimal justFrznVol;

    /**
     * 份额锁定期,yyyyMMdd
     */
    private String openRedeemDt;

    /**
     * 资金账号
     */
    private String cpAcctNo;
    /**
     * 锁定状态（1、锁定中； 2、锁定可赎回； 3、锁定已过,4,不可赎回）
     */
    private String lockStatus;

    /**
     * 可赎回开始日期,yyyyMMdd
     */
    private String redeemStartDt;
    /**
     * 可赎回结束日期,yyyyMMdd
     */
    private String redeemEndDt;
    /**
     * 开放开始日期,yyyyMMdd
     */
    private String openStartDt;
    /**
     * 开放结束日期,yyyyMMdd
     */
    private String openEndDt;

    /**
     * 不可赎回原因
     */
    private String errorMsg;
    /**
     * 是否异常
     */
    private boolean isError;
}
