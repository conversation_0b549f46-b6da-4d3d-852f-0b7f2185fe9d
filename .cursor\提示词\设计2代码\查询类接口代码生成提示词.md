# ✅ AI-Dubbo查询接口代码生成提示词（基于详细设计）

你是一名资深Java开发和架构师,请根据我提供的**Dubbo查询接口详细设计文档**内容，严格遵循当前项目的代码规范和结构，生成完整的**后端代码实现**。

## 📌 核心输出要求

- **输出格式**：Java 源代码，按项目模块（`client`, `service`, `dao`）和文件类型（`Facade`, `Request`, `Response`, `Impl`, `Service`, `Repository`, `PO`, `Mapper`）分块输出，确保代码可直接应用到项目中。
- **遵循项目规范**：严格遵守 `.cursor/rules/` 下定义的项目结构、命名约定、注释标准和编码风格。
- **复用优先**：优先使用项目中已有的公共组件、工具类（如日期、字符串、集合处理）、和基础框架，禁止重复造轮子。
- **查询特性**：专门针对查询类接口，不涉及数据修改，无需幂等性支持和复杂事务管理。

## 📘 你的任务：基于设计文档生成查询接口代码

请根据我提供的**详细设计文档**，一次性生成所有相关的代码。

### 设计文档结构（输入）

- **功能名称**: 查询接口的核心业务功能，例如 "查询交易订单详情"。
- **接口定义**:
    - **接口名**: `QueryTradeDetailFacade`
    - **方法名**: `execute`
- **请求参数（Request）**: 字段名、Java类型、中文描述、是否必填、校验规则（如长度、格式、取值范围）。
- **响应参数（Response）**: 字段名、Java类型、中文描述。
- **关键业务处理流程**:
    1.  **参数校验**: 检查请求参数的完整性和有效性。
    2.  **业务校验**: 检查业务规则是否满足，如用户权限、数据访问权限等。
    3.  **核心查询逻辑**: 描述数据查询、条件过滤、排序、分页等查询步骤。
    4.  **数据组装**: 说明需要查询的数据库表及字段，以及返回数据的组装逻辑。
    5.  **外部调用**: 列出需要调用的其他Dubbo服务或HTTP接口（如需要）。
- **异常处理策略**: 定义不同场景下的错误码和异常信息。
- **性能要求**: 查询优化建议，如索引使用、分页策略等。

### 代码生成清单（输出）

请按以下结构生成代码，确保所有文件都符合项目规范。

#### 1. `high-order-center-client` 模块

- **接口定义 (`Facade`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/search/{具体功能}/...`
    - **命名**: `功能名Facade.java`
    - **规范**:
        - 继承 `BaseFacade<Request, Response>`。
        - 必须包含完整的APIDOC风格注释 (`@api`, `@apiName`, `@apiGroup` 等)。
        - 注释中明确说明这是查询接口。

- **请求对象 (`Request`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/search/{具体功能}/...`
    - **命名**: `接口名Request.java`
    - **规范**:
        - **继承 `OrderSearchBaseRequest`**（不是BaseRequest或OrderTradeBaseRequest）。
        - **使用@Getter/@Setter注解**。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 使用 `@MyValidation` 注解进行字段校验。
        - 查询条件字段支持模糊查询、范围查询等。
        - 自动继承交易账号(txAcctNo)和一账通账号(hbOneNo)等通用查询参数。
        - **必须包含无参构造函数，并在构造函数中设置txCode**：
            ```java
            public 接口名Request() {
                this.setTxCode(TxCodes.对应的查询交易码);
            }
            ```
        - **必须包含完整的APIDOC注释，格式如下**：
            ```java
            /**
             * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.{功能包名}.{接口名}.execute() 接口描述
             * @apiGroup high-order-center
             * @apiDescription 接口详细描述
             *
             * @apiUse orderBaseRequest
             * @apiUse orderSearchBaseRequest
             *
             * @apiParam {类型} [字段名] 字段描述
             * @apiParamExample {json} Request Example
             * dubbo com.howbuy.tms.high.orders.facade.search.{功能包名}.{接口名Request}
             *
             * @apiUse orderBaseResponse
             * @apiUse orderSearchBaseResponse
             *
             * @apiSuccess {类型} 字段名 字段描述
             * @apiSuccessExample {json} Response Example
             * dubbo com.howbuy.tms.high.orders.facade.search.{功能包名}.{接口名Response}
             * {响应示例JSON}
             */
            ```

- **响应对象 (`Response`)**
    - **位置**: `high-order-center-client/src/main/java/com/howbuy/tms/high/orders/facade/search/{具体功能}/...`
    - **命名**: `接口名Response.java`
    - **规范**:
        - **继承 `OrderSearchBaseResponse`**（不是实现Serializable或继承OrderTradeBaseResponse）。
        - **不使用@Getter/@Setter注解，手动编写getter/setter方法**。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 支持分页信息（如需要）。

#### 2. `high-order-center-service` 模块

- **接口实现 (`FacadeImpl`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/facade/search/{具体功能}/...`
    - **命名**: `功能名FacadeImpl.java`
    - **规范**:
        - 实现对应的 `Facade` 接口。
        - **使用 `@Component` 注解**（不是@DubboService）。
        - 注入 `Service` 层并调用其方法，只做参数转换和基本校验，不实现核心查询逻辑。
        - 添加标准的类和方法注释。
        - **无需幂等性支持**。

- **业务逻辑 (`Service`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/service/search/{具体功能}/...`
    - **命名**: `功能名Service.java`
    - **规范**:
        - 使用 `@Service` 和 `@Slf4j` 注解。
        - 实现所有核心查询逻辑、条件构建和数据组装。
        - 调用 `Repository` 层进行数据查询。
        - 方法需有完整的Javadoc注释（`@description`, `@param`, `@return`, `@author`, `@date`）。
        - **专注于只读操作，无需考虑事务边界**。

- **数据仓库 (`Repository`)**
    - **位置**: `high-order-center-service/src/main/java/com/howbuy/tms/high/orders/service/repository/...`
    - **命名**: `表名Repository.java`
    - **规范**:
        - 使用 `@Repository` 注解。
        - 注入 `Mapper` 接口，封装数据库查询操作。
        - **查询方法使用 `@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)`**。
        - 类级别使用 `@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)`。
        - 方法需有完整的Javadoc注释。
        - 支持复杂查询条件构建。

#### 3. `high-order-center-dao` 模块

- **数据实体 (`PO`)**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/po/...`
    - **命名**: `表名Po.java`
    - **规范**:
        - 与数据库表字段一一对应。
        - 使用 `@Getter` / `@Setter` 注解。
        - 字段有中文注释。

- **根据mybatis插件自动生成的Mapper 接口**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/mysql/...`
    - **命名**: `表名AutoMapper.java`

- **根据mybatis插件自动生成的Mapper XML**
    - **位置**: `high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/mysql/...`
    - **命名**: `表名AutoMapper.xml`

- **根据业务需求自定义的Mapper 接口**
    - **位置**: `high-order-center-dao/src/main/java/com/howbuy/tms/high/orders/dao/mapper/mysql/customize/...`
    - **命名**: `表名Mapper.java`，并且该类需要继承自动生成的mapper:`表名AutoMapper`
    - **规范**:
        - 包含复杂查询方法，如条件查询、统计查询、关联查询等。
        - 方法命名清晰，体现查询意图。

- **自定义Mapper XML**
    - **位置**: `high-order-center-dao/src/main/resources/com/howbuy/tms/high/orders/dao/mapper/mysql/customize/...`
    - **命名**: `表名Mapper.xml`
    - **规范**:
        - 包含复杂查询SQL，支持动态条件。
        - 注意SQL性能优化，合理使用索引。
        - 支持分页查询（如需要）。

## 🔍 查询接口专有特性

| 特性                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ✅ **只读操作**      | 专注于数据查询，不涉及数据修改，使用readOnly=true事务。     |
| ✅ **无幂等性要求**   | 查询操作天然幂等，无需实现IdempotentSupport接口。           |
| ✅ **继承OrderSearchBaseRequest** | 请求对象继承OrderSearchBaseRequest，不是BaseRequest或OrderTradeBaseRequest。        |
| ✅ **继承OrderSearchBaseResponse** | 响应对象继承OrderSearchBaseResponse，不是实现Serializable或继承OrderTradeBaseResponse。   |
| ✅ **@Component注解** | FacadeImpl使用@Component注解，不是@DubboService。           |
| ✅ **包路径规范**    | Request和Response都在facade.search.{具体功能}包下，不在client.domain包下。 |
| ✅ **手动getter/setter** | 不使用@Getter/@Setter注解，手动编写getter/setter方法。    |
| ✅ **构造函数设置txCode** | 必须在无参构造函数中设置对应的查询交易码。                |
| ✅ **完整APIDOC注释** | 包含@api、@apiGroup、@apiDescription、@apiUse、@apiParam、@apiSuccess等完整注释。 |
| ✅ **字段校验注解**   | 使用@MyValidation注解进行字段校验，而不是其他校验注解。      |
| ✅ **灵活查询条件**   | 支持多种查询条件组合，如模糊查询、范围查询、排序等。         |
| ✅ **性能优化**      | 关注查询性能，合理使用索引，避免全表扫描。                   |

## 🚫 禁止事项

| 类型                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ❌ **违反项目规范**  | 所有代码的包结构、命名、注释必须符合 `.cursor/rules/` 中的定义。 |
| ❌ **硬编码**        | 禁止在代码中硬编码任何常量、URL、配置项，应使用常量类或配置中心。 |
| ❌ **逻辑泄露**      | 查询逻辑必须封装在 `Service` 层，`FacadeImpl` 只做调度和校验。 |
| ❌ **忽略异常处理**   | 必须对外部调用和数据库操作进行 `try-catch`，并进行合理的异常转换和日志记录。 |
| ❌ **绕过Repository** | `Service` 层禁止直接注入和调用 `Mapper`，必须通过 `Repository` 层访问数据库。 |
| ❌ **禁止使用魔法值** | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举。 |
| ❌ **缺少枚举或常量** | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量。 |
| ❌ **错误包路径**    | Request和Response必须在facade.search.{具体功能}包下，不能放在client.domain包下。 |
| ❌ **错误继承关系**  | 查询接口请求必须继承OrderSearchBaseRequest，不能继承BaseRequest或OrderTradeBaseRequest；响应必须继承OrderSearchBaseResponse，不能实现Serializable或继承OrderTradeBaseResponse。 |
| ❌ **错误注解使用**  | FacadeImpl不能使用@DubboService，必须使用@Component注解；不能使用@Getter/@Setter注解，必须手动编写getter/setter方法。    |
| ❌ **缺少构造函数**  | 请求类必须包含无参构造函数，并在构造函数中设置对应的txCode。  |
| ❌ **不完整APIDOC**  | 必须包含完整的APIDOC注释格式，包括@api、@apiGroup、@apiDescription、@apiUse、@apiParam、@apiSuccess、@apiSuccessExample等。 |
| ❌ **错误校验注解**  | 必须使用@MyValidation注解进行字段校验，不能使用其他校验注解。 |

## 📊 查询接口性能建议

1. **分页查询**: 对于大数据量查询，必须支持分页，避免一次性加载过多数据。
2. **索引优化**: 确保查询条件字段有合适的索引，避免全表扫描。
3. **查询缓存**: 对于频繁查询的数据，考虑使用Redis等缓存方案。
4. **结果集限制**: 设置合理的查询结果数量上限，防止内存溢出。
5. **SQL优化**: 避免使用SELECT *，只查询需要的字段。

**请在我提供详细设计文档后，立即按上述要求生成完整、高质量的查询接口代码。**