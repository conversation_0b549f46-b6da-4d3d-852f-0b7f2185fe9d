package com.howbuy.tms.high.orders.dao.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 非香港待确认卖出订单查询结果VO
 * @Author: yun.lu
 * @Date: 2023/11/15 14:06:24
 */
@Getter
@Setter
public class UnConfirmUnHkSellOrderVo {

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 是否合并上报,1:是,0:不是
     */
    private String mergeSubmitFlag;

    /**
     * 主订单号
     */
    private String mainDealOrderNo;

    /**
     * 订单交易类型,1:直销,2:代销
     */
    private String scaleType;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 业务类型
     */
    private String mBusiCode;

    /**
     * 上报状态
     * 0-无需上报，1-未上报，2-上报完成，3-需重新上报
     */
    private String notifySubmitFlag;
    /**
     * 上报日,yyyyMMdd
     */
    private String submitTaDt;
}