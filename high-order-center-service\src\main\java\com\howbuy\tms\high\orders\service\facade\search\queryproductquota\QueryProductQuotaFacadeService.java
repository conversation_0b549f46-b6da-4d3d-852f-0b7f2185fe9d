package com.howbuy.tms.high.orders.service.facade.search.queryproductquota;

import com.howbuy.tms.cache.service.highquota.HighProductQuotaService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.datasource.RouteHolder;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaRequest;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse.QuotaBean;
import com.howbuy.tms.high.orders.service.task.QueryProductQuotaTask;
import com.howbuy.tms.high.orders.service.repository.CmBlacklistDirectRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @description:(查询产品额度信息)
 * @reason:
 * <AUTHOR>
 * @date 2017年7月18日 下午4:03:58
 * @since JDK 1.7
 */
@DubboService
@Service("queryProductQuotaFacade")
public class QueryProductQuotaFacadeService implements QueryProductQuotaFacade{
    private static final Logger logger = LogManager.getLogger(QueryProductQuotaFacadeService.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    
    @Autowired
    private HighProductQuotaService highProductQuotaService;
    
    @Autowired
    private CmBlacklistDirectRepository cmBlacklistDirectRepository;
    
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade.execute(QueryProductQuotaRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryProductQuotaFacadeService
     * @apiName execute
     * @apiParam (请求参数) {Array} productCodeArr
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=dkBB9J&pageSize=3446&disCode=lvzk&productCodeArr=cNXPTCYNT&txChannel=VV&appTm=DM8mS5kE&subOutletCode=1G&pageNo=5413&operIp=BE&txAcctNo=3UOL7RxGze&appDt=x&dataTrack=yf1BWybDg8&txCode=ul0EGJj3&outletCode=ZkoerLM9l
     * @apiSuccess (响应结果) {Array} quotaBeanList 产品额度列表
     * @apiSuccess (响应结果) {String} quotaBeanList.productCode 产品代码
     * @apiSuccess (响应结果) {Number} quotaBeanList.totalAmt 总金额
     * @apiSuccess (响应结果) {Number} quotaBeanList.totalPlaces 总人数
     * @apiSuccess (响应结果) {Number} quotaBeanList.costTotalAmt 消耗总金额
     * @apiSuccess (响应结果) {Number} quotaBeanList.costTotalPlaces 消耗总人数
     * @apiSuccess (响应结果) {Number} quotaBeanList.leftAmt 剩余额度
     * @apiSuccess (响应结果) {String} quotaBeanList.inDirectBlackList 在直销黑名单中
     * @apiSuccess (响应结果) {Number} quotaBeanList.lockExitCount 锁定退出人数
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"ZFAPR08","quotaBeanList":[{"totalPlaces":2252,"lockExitCount":7923,"productCode":"aHrdV","totalAmt":7087.942641732169,"inDirectBlackList":"OWR7Hn","costTotalPlaces":6496,"leftAmt":9391.122595529385,"costTotalAmt":107.66240416489214}],"totalPage":4646,"pageNo":1635,"description":"hv2RcTs3kT","totalCount":5027}
     */
    @Override
    public QueryProductQuotaResponse execute(QueryProductQuotaRequest request) {
        QueryProductQuotaResponse response = new QueryProductQuotaResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        String[] productCodeList = request.getProductCodeArr();
        if (productCodeList == null || productCodeList.length <= 0) {
            return response;
        }

        QuotaBean quotaBean = null;
        List<QuotaBean> quotaBeanList = new ArrayList<QuotaBean>();
        response.setQuotaBeanList(quotaBeanList);
        for (String productCode : productCodeList) {
            if (StringUtils.isEmpty(productCode)) {
                logger.warn("exist productCode is empty!");
                continue;
            }
            
            quotaBean = new QueryProductQuotaResponse.QuotaBean();
            quotaBean.setProductCode(productCode);
            quotaBeanList.add(quotaBean);
        }
        
        // 一账通账号
        String hbOneNo = request.getHbOneNo();
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(request.getTxAcctNo());
        }

        // 查询客户信息
        QueryCustInfoResult custInfo = null;
        if (com.howbuy.tms.common.utils.StringUtils.isNotEmpty(request.getTxAcctNo())) {
            custInfo = queryCustInfoOuterService.queryCustInfo(request.getTxAcctNo());
        }

        int count = quotaBeanList.size();
        final CountDownLatch latch = new CountDownLatch(count);
        // 多线程异步查询产品额度信息
        for (QuotaBean productean : quotaBeanList) {
            CommonThreadPool.submit(new QueryProductQuotaTask(queryHighProductOuterService, hbOneNo, productean, 
                    RouteHolder.getRouteKey(), highProductQuotaService, cmBlacklistDirectRepository, custInfo, latch));
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryProductQuotaFacadeService|latch.await exception.", e);
            Thread.currentThread().interrupt();
        }

        return response;
    }
}
