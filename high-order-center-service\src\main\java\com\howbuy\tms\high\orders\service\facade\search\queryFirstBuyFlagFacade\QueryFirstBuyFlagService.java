package com.howbuy.tms.high.orders.service.facade.search.queryFirstBuyFlagFacade;

import com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade.QueryFirstBuyFlagFacade;
import com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade.QueryFirstBuyFlagRequest;
import com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade.QueryFirstBuyFlagResponse;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Description:查询首单是否首单
 * @Author: yun.lu
 * Date: 2024/5/29 15:52
 */
@DubboService
@Service("queryFirstBuyFlagFacade")
@Slf4j
public class QueryFirstBuyFlagService implements QueryFirstBuyFlagFacade {
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade.QueryFirstBuyFlagFacade.execute(QueryFirstBuyFlagRequest queryFirstBuyFlagRequest)
     * @apiVersion 1.0.0
     * @apiGroup QueryFirstBuyFlagService
     * @apiName execute
     * @apiDescription 查询首单是否首单
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} queryHzBuyOrderInfoRequest
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=zIkodbY6LO&pageSize=3364&disCode=WyYzC&txChannel=EtBgJ&queryHzBuyOrderInfoRequest=uam&appTm=Zoot&fundCode=JKpv&subOutletCode=m&pageNo=8518&operIp=Mx839&txAcctNo=Y&appDt=YwOqwxg&dataTrack=A8iXOb&txCode=Fp2ow8vGV4&outletCode=SAN
     * @apiSuccess (响应结果) {String} isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"psrhUlgHfk","isFirstPay":"nKMoRLBW","totalPage":608,"pageNo":8681,"description":"yjt7p","totalCount":1883}
     */
    @Override
    public QueryFirstBuyFlagResponse execute(QueryFirstBuyFlagRequest queryFirstBuyFlagRequest) {
        // 是否首次实缴
        String firstBuyFlag = acctBalanceBaseInfoService.getFirstBuyFlag(queryFirstBuyFlagRequest.getFundCode(), queryFirstBuyFlagRequest.getTxAcctNo(), queryFirstBuyFlagRequest.getDisCode());
        // 返回结果
        QueryFirstBuyFlagResponse response = new QueryFirstBuyFlagResponse();
        response.setIsFirstPay(firstBuyFlag);
        return response;
    }
}
