/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryredeemfundlist;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.TxOpenFlagEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductTxOpenCfgBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.CustomerRedeemAppointInfo;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListResponse.RedeemBalanceBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.common.enums.VolLockStatusEnum;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @description:(查询赎回列表)
 * @reason:
 * @date 2017年12月1日 下午2:26:30
 * @since JDK 1.6
 */
@DubboService
@Service("queryRedeemFundListFacade")
public class QueryRedeemFundListFacadeService extends AbstractBusiProcess implements QueryRedeemFundListFacade {
    private static final Logger logger = LogManager.getLogger(QueryRedeemFundListFacadeService.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Autowired
    private CustBooksRepository custBooksRepository;

    @Autowired
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListFacade.execute(QueryRedeemFundListRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryRedeemFundListFacadeService
     * @apiName execute
     * @apiDescription 查询赎回列表
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} fundShareClass 产品份额类型，A-前收费；B-后收费
     * @apiParam (请求参数) {String} protocolNo 协议号
     * @apiParam (请求参数) {String} protocolType 4-高端协议类型
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * fundShareClass=p0Z&hbOneNo=NOLeQaCJJ&pageSize=4858&protocolType=nufgJ&disCode=NVneGyQeg&txChannel=97dR8o&appTm=i&productCode=MXdQN0S&disCodeList=hgo4&subOutletCode=i&pageNo=531&operIp=UvC&protocolNo=4qYte&txAcctNo=gjcWrHR&cpAcctNo=IA0&appDt=Cnwe&dataTrack=W&txCode=R7Uu&outletCode=yRfwwgWJo3
     * @apiSuccess (响应结果) {Array} redeemBalanceList 赎回list
     * @apiSuccess (响应结果) {String} redeemBalanceList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} redeemBalanceList.disCode 分销机构号
     * @apiSuccess (响应结果) {String} redeemBalanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} redeemBalanceList.fundShareClass 份额类型
     * @apiSuccess (响应结果) {String} redeemBalanceList.productName 产品名称
     * @apiSuccess (响应结果) {String} redeemBalanceList.productType 产品类型
     * @apiSuccess (响应结果) {Number} redeemBalanceList.nav 净值
     * @apiSuccess (响应结果) {String} redeemBalanceList.productStatus 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记；          8-红利发放； 9-基金封闭； a-基金终止
     * @apiSuccess (响应结果) {String} redeemBalanceList.navDt 净值日期
     * @apiSuccess (响应结果) {String} redeemBalanceList.protocolNo 协议号
     * @apiSuccess (响应结果) {String} redeemBalanceList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} redeemBalanceList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} redeemBalanceList.bankName 银行名称
     * @apiSuccess (响应结果) {String} redeemBalanceList.bankAcctNo 银行卡号
     * @apiSuccess (响应结果) {String} redeemBalanceList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {Number} redeemBalanceList.availVol 可用份额
     * @apiSuccess (响应结果) {Number} redeemBalanceList.marketValue 市值
     * @apiSuccess (响应结果) {String} redeemBalanceList.openRedeDt 开放赎回日期
     * @apiSuccess (响应结果) {Boolean} redeemBalanceList.canSell 是否可赎回
     * @apiSuccess (响应结果) {String} redeemBalanceList.productPinyinName 产品拼音码
     * @apiSuccess (响应结果) {String} redeemBalanceList.openStartDt 开放开始日期
     * @apiSuccess (响应结果) {String} redeemBalanceList.openEndDt 开放结束日期
     * @apiSuccess (响应结果) {String} redeemBalanceList.appointStartDt 预约开始日期
     * @apiSuccess (响应结果) {String} redeemBalanceList.appointEndDt 预约结束日期
     * @apiSuccess (响应结果) {String} redeemBalanceList.bankAccount bankcode+"|"+accountId 拼接
     * @apiSuccess (响应结果) {String} redeemBalanceList.isBook 是否支持赎回预约
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"KD57","totalPage":3497,"pageNo":5916,"redeemBalanceList":[{"bankAcctNo":"b5YF","bankAcctMask":"GikKjd","canSell":true,"openStartDt":"B1zy5VqRcW","bankName":"Ei","disCode":"FUtkOs","productName":"GtfgBYp","isBook":"nBxVulukG","availVol":5363.*************,"txAcctNo":"Vt2cWj","protocolNo":"3P","cpAcctNo":"n","productPinyinName":"TT8XvK80j","openEndDt":"fnFq3m0Hy","productType":"PvDfqZ","appointStartDt":"G","bankAccount":"e","bankCode":"oM0NER","fundShareClass":"D9PxV5M","nav":3374.************,"navDt":"CUW","productStatus":"il","marketValue":6237.************,"appointEndDt":"4e46","productCode":"B","openRedeDt":"NRxjIk"}],"description":"3","totalCount":2930}
     */
    @Override
    public QueryRedeemFundListResponse execute(QueryRedeemFundListRequest request) {
        logger.info("QueryRedeemFundListFacadeService|execute|查询赎回列表开始");
        String txAcctNo = request.getTxAcctNo();
        List<String> disCodeList = getDisCodeList(request);
        String productCode = request.getProductCode();
        String cpAcctNo = request.getCpAcctNo();
        QueryRedeemFundListResponse response = new QueryRedeemFundListResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        // 私募（tp,qj）查询子账本表
        List<SubCustBooksPo> booklist = subCustBooksRepository.selectSubCustBooksByTxAcctNo(disCodeList, txAcctNo, productCode, cpAcctNo);
        logger.info("QueryRedeemFundListFacadeService|SubCustBooksPo booklist:{}", JSON.toJSONString(booklist));

        // 私募（tp,qj）查询账本明细冻结
        List<CustBooksPo> unConfirmVolList = custBooksRepository.selectUnconfirmedVol(disCodeList, txAcctNo, productCode);
        logger.info("QueryRedeemFundListFacadeService|SubCustBooksPo unConfirmVolList:{}", JSON.toJSONString(unConfirmVolList));

        // 查询专户持仓
        List<SubCustBooksPo> zhBalanceList = subCustBooksRepository.selectZhBalanceDtlWithOutSubBook(disCodeList, txAcctNo, productCode, cpAcctNo);
        logger.info("QueryRedeemFundListFacadeService|BalanceVo zhBalanceList{}", JSON.toJSONString(zhBalanceList));

        if (CollectionUtils.isEmpty(booklist)) {
            booklist = new ArrayList<>();
        }
        // 排除有子账本的专户持仓
        if (!CollectionUtils.isEmpty(zhBalanceList)) {
            booklist.addAll(zhBalanceList);
        }
        if (CollectionUtils.isEmpty(booklist)) {
            return response;
        }
        // 获取赎回份额列表
        List<RedeemBalanceBean> redeemBalanceList = getRedeemBalanceList(booklist, unConfirmVolList, txAcctNo, request);
        response.setRedeemBalanceList(redeemBalanceList);
        logger.info("查询赎回列表结束，redeemBalanceList条数：" + redeemBalanceList.size());
        return response;
    }

    private List<String> getDisCodeList(QueryRedeemFundListRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }


    /**
     * getRedeemBalanceList:(获取可赎回持仓列表)
     *
     * @param bookList
     * @return
     * <AUTHOR>
     * @date 2017年12月11日 下午3:51:20
     */
    private List<RedeemBalanceBean> getRedeemBalanceList(List<SubCustBooksPo> bookList, List<CustBooksPo> unConfirmVolList, String txAcctNo, QueryRedeemFundListRequest request) {

        List<SubCustBooksPo> availVolList = getAvailVolList(bookList);

        if (CollectionUtils.isEmpty(availVolList)) {
            return null;
        }
        // 资金账号set
        Set<String> cpAcctNoSet = new HashSet<>();
        // 产品代码set
        Set<String> fundCodeSet = new HashSet<>();
        // 设置用户银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = getCustBankCardInfo(txAcctNo, request, availVolList, cpAcctNoSet, fundCodeSet);


        Map<String, BigDecimal> unconfirmVolMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unConfirmVolList)) {
            for (CustBooksPo po : unConfirmVolList) {
                unconfirmVolMap.put(po.getProductCode(), po.getUnconfirmedVol());
            }
        }

        // 批量获取高端产品信息
        Map<String, HighProductBaseInfoBean> highProductBaseBeanMap = new HashMap<>();
        for (String productCode : fundCodeSet) {
            HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
            highProductBaseBeanMap.put(productCode, highProductBaseBean);
        }

        // 查询赎回预约日历,组织日历map
        Map<String, ProductAppointmentInfoBean> productAppointmentInfoMap = getStringProductAppointmentInfoBeanMap(request, fundCodeSet, highProductBaseBeanMap);

        // 获取当前交易日
        final String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());
        // 获取产品净值信息
        Map<String, HighProductNavBean> highProductNavMap = getHighProductNavMap(new ArrayList<>(fundCodeSet));

        Map<String, RedeemBalanceBean> redeemBalanceMap = new HashMap<>();
        for (SubCustBooksPo subCustBooksPo : availVolList) {
            String productCode = subCustBooksPo.getFundCode();
            HighProductBaseInfoBean highProductBaseBean = highProductBaseBeanMap.get(productCode);
            ProductAppointmentInfoBean productAppointmentInfoBean = productAppointmentInfoMap.get(productCode);
            QueryCustBankCardResult queryCustBankCardResult = bankCardMap.get(subCustBooksPo.getCpAcctNo());
            HighProductNavBean highProductNavBean = highProductNavMap.get(productCode);
            BigDecimal unconfirmVol = unconfirmVolMap.get(productCode);
            // 构建赎回份额bean
            buildRedeemBalanceInfo(queryCustBankCardResult, subCustBooksPo, redeemBalanceMap, highProductBaseBean, productAppointmentInfoBean, highProductNavBean, taTradeDt, unconfirmVol);
        }
        List<RedeemBalanceBean> redeemBalanceList = new ArrayList<>();
        if (CollectionUtils.isEmpty(redeemBalanceMap.values())) {
            return redeemBalanceList;
        }
        for (RedeemBalanceBean redeemBalanceBean : redeemBalanceMap.values()) {
            redeemBalanceBean.setCanSell(redeemBalanceBean.isCanSell() && redeemBalanceBean.getAvailVol().compareTo(BigDecimal.ZERO) > 0);
            redeemBalanceList.add(redeemBalanceBean);
        }
        return redeemBalanceList;

    }

    /**
     * 设置用户银行卡信息
     */
    private Map<String, QueryCustBankCardResult> getCustBankCardInfo(String txAcctNo, QueryRedeemFundListRequest request, List<SubCustBooksPo> availVolList, Set<String> cpAcctNoSet, Set<String> fundCodeSet) {
        for (SubCustBooksPo subCustBooksPo : availVolList) {
            cpAcctNoSet.add(subCustBooksPo.getCpAcctNo());
            fundCodeSet.add(subCustBooksPo.getFundCode());
        }
        // 获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, txAcctNo, request.getDisCode(), cpAcct, bankCardInfo,
                    request.getOutletCode()));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryRedeemFundListFacadeService|QueryCustBankCardTask|error");
            Thread.currentThread().interrupt();
        }
        return bankCardMap;
    }

    /**
     * 查询赎回预约日历,组织日历map
     */
    private Map<String, ProductAppointmentInfoBean> getStringProductAppointmentInfoBeanMap(QueryRedeemFundListRequest request, Set<String> fundCodeSet, Map<String, HighProductBaseInfoBean> highProductBaseBeanMap) {
        Map<String, ProductAppointmentInfoBean> productAppointmentInfoMap = new HashMap<>();
        String appDtmStr = request.getAppDt() + request.getAppTm();
        Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);

        // 获取赎回预约日历
        for (String fundCode : fundCodeSet) {
            HighProductBaseInfoBean highProductBaseBean = highProductBaseBeanMap.get(fundCode);
            ProductAppointmentInfoBean productAppointmentInfoBean = productAppointmentInfoMap.get(fundCode);
            if (productAppointmentInfoBean == null) {
                productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfo(fundCode, highProductBaseBean.getShareClass(), request.getDisCode(), appDtm, "1");
                if (productAppointmentInfoBean != null) {
                    productAppointmentInfoMap.put(fundCode, productAppointmentInfoBean);
                }
            }
        }
        return productAppointmentInfoMap;
    }

    /**
     * calLockRedeemBalance:(计算锁定期份额)
     *
     * <AUTHOR>
     * @date 2017年12月11日 下午8:03:39
     */
    private void calLockRedeemBalance(QueryCustBankCardResult queryCustBankCardResult, SubCustBooksPo subCustBooksPo, RedeemBalanceBean redeemBalanceBean,
                                      HighProductNavBean highProductNavBean) {
        BigDecimal marketValue = null;
        if (highProductNavBean != null && highProductNavBean.getNav() != null) {
            marketValue = subCustBooksPo.getAvailVol().multiply(highProductNavBean.getNav());
        }
        BigDecimal availVol = add(redeemBalanceBean.getAvailVol(), subCustBooksPo.getAvailVol());
        marketValue = add(marketValue, redeemBalanceBean.getMarketValue());
        redeemBalanceBean.setAvailVol(availVol);
        redeemBalanceBean.setMarketValue(marketValue);

        String newBankAccount = getNewBank(queryCustBankCardResult.getBankCode(),
                StringUtils.substring(queryCustBankCardResult.getBankAcct(), queryCustBankCardResult.getBankAcct().length() - 4));
        // 相同锁定期，相同产品的，不同银行卡合并展示
        if (!redeemBalanceBean.getBankAccount().contains(newBankAccount)) {
            String bankAccount = redeemBalanceBean.getBankAccount() + "@" + newBankAccount;
            redeemBalanceBean.setBankAccount(bankAccount);
        }

    }

    /**
     * add:(加法计算)
     *
     * @param arg1
     * @param arg2
     * @return
     * <AUTHOR>
     * @date 2017年12月11日 下午9:10:43
     */
    private static BigDecimal add(BigDecimal arg1, BigDecimal arg2) {

        if (arg1 == null && arg2 == null) {
            return null;
        }
        if (arg1 == null) {
            return arg2;
        }
        if (arg2 == null) {
            return arg1;
        }
        return arg1.add(arg2);
    }

    /**
     * buildRedeemBalanceInfo:(构建赎回份额bean)
     *
     * @param queryCustBankCardResult
     * @param subCustBooksPo
     * @return
     * <AUTHOR>
     * @date 2017年12月11日 下午5:09:31
     */
    private RedeemBalanceBean buildRedeemBalanceInfo(QueryCustBankCardResult queryCustBankCardResult, SubCustBooksPo subCustBooksPo,
                                                     Map<String, RedeemBalanceBean> redeemBalanceMap, HighProductBaseInfoBean highProductBaseBean,
                                                     ProductAppointmentInfoBean productAppointmentInfoBean, HighProductNavBean highProductNavBean,
                                                     String taTradeDt, BigDecimal unConfirmVol) {
        if (subCustBooksPo == null || redeemBalanceMap == null || highProductBaseBean == null) {
            return null;
        }
        // 是否支持提前赎回
        boolean isBook = isSupportAdvanceRedeem(highProductBaseBean.getIsScheduledTrade());
        boolean canSell = true;
        boolean isLock = false;
        // 如果是循环锁定的,走其他逻辑
        if (YesOrNoEnum.YES.getCode().equals(highProductBaseBean.getIsCyclicLock())) {
            if (ProductChannelEnum.TP_SM.getCode().equals(highProductBaseBean.getProductChannel())
                    || ProductChannelEnum.HIGH_FUND.getCode().equals(highProductBaseBean.getProductChannel())) {
                // 查询高端产品交易开通配置信息
                HighProductTxOpenCfgBean highProductTxOpenCfgBean = queryHighProductOuterService.getHighProductTxOpenCfg(highProductBaseBean.getFundCode(), BusinessCodeEnum.REDEEM.getCode());
                // 校验交易是否开通
                if (highProductTxOpenCfgBean == null || TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
                    canSell = false;
                }
            }
            CustomerRedeemAppointInfo customerRedeemAppointInfo = queryCustomerRedeemAppointInfoLogicService.getCustomerRedeemAppointInfoBySubCustBooks(subCustBooksPo, taTradeDt);
            if (customerRedeemAppointInfo.getCanRedeemVol() == null || BigDecimal.ZERO.compareTo(customerRedeemAppointInfo.getCanRedeemVol()) <= 0) {
                canSell = false;
            }
            if (VolLockStatusEnum.LOCK_REDEEMABLE.getStatus().equals(customerRedeemAppointInfo.getLockStatus())) {
                isLock = false;
            } else {
                isLock = true;
            }
        } else {
            // 可赎回日期
            String openStartDt = (!isBook || productAppointmentInfoBean == null) ? null : productAppointmentInfoBean.getOpenStartDt();
            String currRedeemDay = getCurrRedeemDay(taTradeDt, openStartDt);
            logger.info("QueryRedeemFundListFacadeService|buildRedeemBalanceInfo|productCode：{}, currRedeemDay:{}, openStartDt:{}, " + "taTradeDt:{}, isBook:{}", highProductBaseBean.getFundCode(), currRedeemDay, openStartDt, taTradeDt, isBook);
            // 产品状态
            HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductBaseBean.getFundCode(), currRedeemDay);
            // 是否可赎
            canSell = getCanSell(highProductBaseBean, highProductStatInfoBean, productAppointmentInfoBean);
            isLock = isLock(taTradeDt, openStartDt, subCustBooksPo.getOpenRedeDt(), highProductBaseBean.getHasLockPeriod());
            logger.info("QueryRedeemFundListFacadeService|buildRedeemBalanceInfo|productCode:{},isBook:{},isLock:{},canSell:{}", highProductBaseBean.getFundCode(), isBook, isLock, canSell);
        }
        // 不同产品，不同锁定期，不同银行卡不同的key
        StringBuilder productCodeKey = new StringBuilder(subCustBooksPo.getFundCode());
        if (!isLock) {
            productCodeKey.append(subCustBooksPo.getOpenRedeDt()).append(subCustBooksPo.getCpAcctNo());
        }
        RedeemBalanceBean redeemBalanceBean = redeemBalanceMap.get(productCodeKey.toString());
        if (redeemBalanceBean == null) {
            redeemBalanceBean = getRedeemBalanceBean(queryCustBankCardResult, subCustBooksPo, redeemBalanceMap, highProductBaseBean, productAppointmentInfoBean, highProductNavBean, unConfirmVol, isBook, canSell, isLock, productCodeKey);
        } else {
            // 计算同一个锁定期的份额
            calLockRedeemBalance(queryCustBankCardResult, subCustBooksPo, redeemBalanceBean, highProductNavBean);
        }
        return redeemBalanceBean;
    }

    private RedeemBalanceBean getRedeemBalanceBean(QueryCustBankCardResult queryCustBankCardResult, SubCustBooksPo subCustBooksPo, Map<String, RedeemBalanceBean> redeemBalanceMap, HighProductBaseInfoBean highProductBaseBean, ProductAppointmentInfoBean productAppointmentInfoBean,
                                                   HighProductNavBean highProductNavBean, BigDecimal unconfirmVol, boolean isBook, boolean canSell, boolean isLock, StringBuilder productCodeKey) {
        RedeemBalanceBean redeemBalanceBean;
        redeemBalanceBean = new RedeemBalanceBean();
        // 产品基本信息
        redeemBalanceBean.setProductCode(highProductBaseBean.getFundCode());
        redeemBalanceBean.setProductName(highProductBaseBean.getFundAttr());
        redeemBalanceBean.setProductType(highProductBaseBean.getFundType());
        redeemBalanceBean.setFundShareClass(highProductBaseBean.getShareClass());
        redeemBalanceBean.setOpenRedeDt(subCustBooksPo.getOpenRedeDt());
        // 1-支持提前赎回；0-不支持提前赎回
        redeemBalanceBean.setIsBook(isBook ? "1" : "0");
        // 是否可赎
        redeemBalanceBean.setCanSell(isLock && canSell);
        // 预约信息
        if (null != productAppointmentInfoBean) {
            redeemBalanceBean.setOpenStartDt(productAppointmentInfoBean.getOpenStartDt());
            redeemBalanceBean.setOpenEndDt(productAppointmentInfoBean.getOpenEndDt());
            redeemBalanceBean.setAppointEndDt(productAppointmentInfoBean.getApponitEndDt());
            redeemBalanceBean.setAppointStartDt(productAppointmentInfoBean.getAppointStartDt());
        }
        // 银行账号信息
        if (queryCustBankCardResult != null) {
            redeemBalanceBean.setBankCode(queryCustBankCardResult.getBankCode());
            redeemBalanceBean.setBankName(queryCustBankCardResult.getBankRegionName());
            redeemBalanceBean.setBankAcctNo(StringUtils.substring(queryCustBankCardResult.getBankAcct(), queryCustBankCardResult.getBankAcct().length() - 4));
            redeemBalanceBean.setBankAcctMask(queryCustBankCardResult.getBankAcctMask());
            String newBankAccount = getNewBank(queryCustBankCardResult.getBankCode(), redeemBalanceBean.getBankAcctNo());
            redeemBalanceBean.setBankAccount(newBankAccount);
        }

        // 可用份额和市值,私募产品未确认份额是汇总的，只需要减一次
        if (isLock && !ProductChannelEnum.HIGH_FUND.getCode().equals(subCustBooksPo.getProductChannel()) && unconfirmVol != null) {
            redeemBalanceBean.setAvailVol(subCustBooksPo.getAvailVol().subtract(unconfirmVol));
        } else {
            redeemBalanceBean.setAvailVol(subCustBooksPo.getAvailVol());
        }

        BigDecimal marketValue = null;
        if (highProductNavBean != null && highProductNavBean.getNav() != null) {
            marketValue = redeemBalanceBean.getAvailVol().multiply(highProductNavBean.getNav());
        }
        redeemBalanceBean.setMarketValue(marketValue);
        redeemBalanceMap.put(productCodeKey.toString(), redeemBalanceBean);
        return redeemBalanceBean;
    }

    /**
     * getAvailVolList:(获取可以份额列表)
     *
     * @param booklist
     * @return
     * <AUTHOR>
     * @date 2017年12月11日 下午3:55:01
     */
    private List<SubCustBooksPo> getAvailVolList(List<SubCustBooksPo> booklist) {
        if (CollectionUtils.isEmpty(booklist)) {
            return null;
        }
        List<SubCustBooksPo> availVolList = new ArrayList<SubCustBooksPo>();
        for (SubCustBooksPo book : booklist) {
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getFrznVol()).subtract(book.getJustFrznVol());
            if (availVol.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            book.setAvailVol(availVol);
            availVolList.add(book);
        }

        return availVolList;
    }


    /**
     * isLock:(赎回份额是否锁定)
     *
     * @param taTradeDt
     * @param openStartDt
     * @param openRedeDt
     * @param redeemType(是否控制份额退出)
     * @return
     * <AUTHOR>
     * @date 2017年12月13日 下午2:45:55
     */
    private boolean isLock(String taTradeDt, String openStartDt, String openRedeDt, String redeemType) {
        boolean isLock = false;
        // 不控制
        if (RedeemTypeEnum.NO.getCode().equals(redeemType)) {
            return true;
        }
        String currRedeemDay = getCurrRedeemDay(taTradeDt, openStartDt);
        if (StringUtils.isBlank(openRedeDt)
                || (StringUtils.isNotBlank(openRedeDt) && new BigDecimal(currRedeemDay).subtract(new BigDecimal(openRedeDt)).doubleValue() >= 0)) {
            isLock = true;
        }

        return isLock;
    }

    /**
     * getCurrRedeemDay:(获取当前赎回交易日期)
     *
     * @param openStartDt 赎回开放日
     * @param tradeDt     当前日期
     * @return
     * <AUTHOR>
     * @date 2017年10月10日 下午2:02:40
     */
    private String getCurrRedeemDay(String tradeDt, String openStartDt) {
        if (StringUtils.isNotEmpty(openStartDt)) {
            if (openStartDt.compareTo(tradeDt) > 0) {
                return openStartDt;
            }

        }
        return tradeDt;
    }

    private String getNewBank(String bankCode, String bankAcctNo) {
        if (StringUtils.isNotBlank(bankCode) && StringUtils.isNotBlank(bankAcctNo)) {
            return bankCode + "|" + bankAcctNo;
        } else if (StringUtils.isNotBlank(bankAcctNo) && StringUtils.isBlank(bankCode)) {
            return bankAcctNo;
        } else if (StringUtils.isBlank(bankAcctNo) && StringUtils.isNotBlank(bankCode)) {
            return bankCode;
        }
        return null;
    }

    /**
     * 获取是否预约购买或赎回
     *
     * @param highProductStatInfoBean
     * @param highProductBaseModel
     * @param appointmentInfoModel
     * @return
     */
    public boolean getCanSell(HighProductBaseInfoBean highProductBaseModel, HighProductStatInfoBean highProductStatInfoBean, ProductAppointmentInfoBean appointmentInfoModel) {
        String currentDtm = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDDHHMMSS);
        String appointStartDtm = "", appointEndDtm = "";
        if (highProductBaseModel == null) {
            return false;
        }
        if (ProductChannelEnum.TP_SM.getCode().equals(highProductBaseModel.getProductChannel())
                || ProductChannelEnum.HIGH_FUND.getCode().equals(highProductBaseModel.getProductChannel())) {
            // 查询高端产品交易开通配置信息
            HighProductTxOpenCfgBean highProductTxOpenCfgBean = queryHighProductOuterService.getHighProductTxOpenCfg(highProductBaseModel.getFundCode(), BusinessCodeEnum.REDEEM.getCode());
            // 校验交易是否开通
            if (highProductTxOpenCfgBean == null || TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
                return false;
            }
        }

        // 0-默认不可购买赎回
        boolean cando = false;
        // RedeemStatus 1-可赎回 2-不可赎回
        // 2-不支持提前赎回，基金状态可赎回，可赎回
        if (!isSupportAdvanceRedeem(highProductBaseModel.getIsScheduledTrade())
                && validProductRedeemStatus(highProductStatInfoBean)) {
            return true;
        }
        if (null == appointmentInfoModel) {
            return false;
        }

        if (StringUtils.isNotBlank(appointmentInfoModel.getAppointStartDt())) {
            appointStartDtm = appointmentInfoModel.getAppointStartDt() + appointmentInfoModel.getAppointStartTm();
        }
        if (StringUtils.isNotBlank(appointmentInfoModel.getApponitEndDt())) {
            appointEndDtm = appointmentInfoModel.getApponitEndDt() + appointmentInfoModel.getApponitEndTm();
        }
        // 4-支持提前赎回，当前日期在可预约赎回期限内，可赎回
        if (isSupportAdvanceRedeem(highProductBaseModel.getIsScheduledTrade()) && null != appointmentInfoModel.getAppointStartDt() && null != appointmentInfoModel.getApponitEndDt() && appointStartDtm.compareTo(currentDtm) <= 0 && appointEndDtm.compareTo(currentDtm) >= 0) {
            cando = true;
        }
        return cando;
    }

    public static boolean isSupportAdvanceRedeem(String supportAdvanceFlag) {
        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }

        if (SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)
                || SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
            return true;
        }
        return false;
    }

    /**
     * validProductRedeemStatus:(校验产品赎回状态)
     *
     * @param highProductStatInfoBean
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午2:08:08
     */
    public static boolean validProductRedeemStatus(HighProductStatInfoBean highProductStatInfoBean) {
        if (highProductStatInfoBean == null) {
            return false;
        }

        if (!MDataDic.CAN_REDEEM_SET.contains(highProductStatInfoBean.getFundStat())) {
            return false;
        }

        return true;
    }

}