package com.howbuy.tms.high.orders.service.business.busiprocess.common;

import com.google.common.collect.Lists;
import com.howbuy.acccenter.common.enums.ExamTypeEnum;
import com.howbuy.cc.center.feature.kycinfo.domain.InvestorTypeEnum;
import com.howbuy.common.date.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.tms.cache.service.highquota.HighProductQuotaService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.CurrencyEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.log.pattern.PrivacyUtil;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustBankCardInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.DisAcTxAcctBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryidcardinfo.QueryIdCardInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryidcardinfo.QueryIdCardInfoResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.cms.queryaddress.QueryAddressService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.*;
import com.howbuy.tms.common.validator.account.CustRiskValidator;
import com.howbuy.tms.common.validator.account.InvestorCommitmentValidator;
import com.howbuy.tms.common.validator.account.TxAcctValidator;
import com.howbuy.tms.common.validator.amtorvol.AmtOrVolValidator;
import com.howbuy.tms.common.validator.bankcard.CustBankCardValidator;
import com.howbuy.tms.common.validator.business.ProtocolTypeValidator;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.common.validator.tainfo.TaInfoValidator;
import com.howbuy.tms.high.orders.dao.po.*;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.BaseMergeSubsOrPurRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.bean.PayInfoBean;
import com.howbuy.tms.high.orders.service.business.busiprocess.ValidCustomerInfoStronglyResult;
import com.howbuy.tms.high.orders.service.business.busiprocess.ValidCustomerInfoStronglyService;
import com.howbuy.tms.high.orders.service.business.busiprocess.bean.ValidCustomerInfoStronglyParam;
import com.howbuy.tms.high.orders.service.business.ordercreater.BatchOrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.business.sequence.SequenceService;
import com.howbuy.tms.high.orders.service.business.supplesubs.SuppleSubsService;
import com.howbuy.tms.high.orders.service.repository.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description:disCode维度分类,下单逻辑类
 * @Author: yun.lu
 * Date: 2023/12/12 16:56
 */
public abstract class AbstractDisCodeCreateOrderLogicProcess extends AbstractBusiProcess implements CreateOrder {
    @Autowired
    public CmBlacklistDirectRepository cmBlacklistDirectRepository;
    @Autowired
    public DealOrderRepository dealOrderRepository;
    @Autowired
    private CustProtocolRepository custProtocolRepository;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    public SuppleSubsService suppleSubsService;
    @Autowired
    public HighProductQuotaService highProductQuotaService;
    @Autowired
    public QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    @Autowired
    public QueryAccKycInfoOuterService queryAccKycInfoOuterService;
    @Autowired
    public CustBooksRepository custBooksRepository;
    @Autowired
    public QueryIdCardInfoOuterService queryIdCardInfoOuterService;
    @Autowired
    public ValidCustomerInfoStronglyService validCustomerInfoStronglyService;

    @Autowired
    public QueryCustInfoOuterService queryCustInfoOuterService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private QueryAddressService queryAddressService;

    @Resource
    private CustomerSubmitFormRepository customerSubmitFormRepository;

    @Value("${high.black.txacctnos}")
    private String blackTxAcctNos;

    /**
     * 下单前置校验逻辑
     */
    @Override
    public void createOrderPreCheck(BaseMergeSubsOrPurRequest request, OrderCreateContext context,
                                    String forceSubmitFlag, AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess) {
        // 1.特殊用户不允许交易
        validBlackTxAcctNos(request.getTxAcctNo());
        context.setFormNo(request.getFormNo());
        // 获取高端产品信息
        // 2.反洗钱强控
        HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();
        ValidCustomerInfoStronglyParam validCustomerInfoStronglyParam = new ValidCustomerInfoStronglyParam();
        validCustomerInfoStronglyParam.setDisCode(request.getDisCode());
        validCustomerInfoStronglyParam.setTaCode(highProductInfoBean.getTaCode());
        validCustomerInfoStronglyParam.setCurrentDt(request.getAppDt());
        validCustomerInfoStronglyParam.setTxChannel(request.getTxChannel());
        validCustomerInfoStronglyParam.setHbOneNo(request.getHbOneNo());
        validCustomerInfoStronglyParam.setTxAcctNo(request.getTxAcctNo());
        validCustomerInfoStronglyParam.setCheckByTa(YesOrNoEnum.YES.getCode());
        ValidCustomerInfoStronglyResult validCustomerInfoStronglyResult = this.validCustomerInfoStronglyService.validCustomerInfoStrongly(validCustomerInfoStronglyParam);
        if (YesOrNoEnum.NO.getCode().equals(validCustomerInfoStronglyResult.getValidPass())) {
            throw new ValidateException(ExceptionCodes.QUERY_CUST_INFO_FAILED, validCustomerInfoStronglyResult.getErrorMsg());
        }
        // 3.校验证件上传状态
        QueryIdCardInfoResult result = queryIdCardInfoOuterService.queryIdCardInfoWithDisCode(request.getTxAcctNo(), request.getDisCode());
        if (!(!StringUtil.isEmpty(result.getIdNoDigest()) || YesOrNoEnum.YES.getCode().equals(result.getOfflineUploadFlag()) || YesOrNoEnum.YES.getCode().equals(result.getOnlineUploadFlag()))) {
            throw new ValidateException(ExceptionCodes.ID_IMAGE_NOT_UPLOAD, "下单失败，检测到您开户证件线上线下状态不符合要求,请重试,或咨询您的投顾");
        }
        // 4.账户信息,银行卡信息
        context.getBuyList().forEach(buyBean -> {
            // 验证账户及银行卡信息
            validateAcctAndBank(buyBean.getCustInfo(), buyBean.getPaymentType(), highProductInfoBean.getTaCode());
            // 校验支付方式
            ProductInfoValidator.validatePaymentType(highProductInfoBean.getPaymentTypeList(), buyBean.getPaymentType());
        });

        // 5.校验金额和协议类型
        AmtOrVolValidator.validateAppAmt(request.getAppAmt());
        ProtocolTypeValidator.validateProtocolTypeMatch(request.getProtocolType(), ProtocolTypeEnum.HIGH_FUND.getCode());
        // 6.设置电子合同版本号
        setContractVersion(highProductInfoBean, context);
        // 7.验证产品和Ta信息
        QueryAllCustInfoResult customerInfo = context.getCustInfo();
        validateProductAndTaInfo(highProductInfoBean, request, context.getBuyList().get(0).getAppointmentType(), abstractSubsOrPurLogicProcess);
        // 8.校验客户合格投资者承诺书是否签署
        InvestorCommitmentValidator.validateInvestorCommitment(customerInfo.getCustInfo().getInvstType(), customerInfo.getCustInfo().getQualificationType(), highProductInfoBean.getFundType(), customerInfo.getSignFlag(), customerInfo.getFundFlag(), highProductInfoBean.getIsTrust());
        // 9.查询客户风险评测信息
        QueryAccKycInfoResult queryAccKycInfoResult = queryCustRiskSurvey(request.getTxAcctNo(), request.getDisCode());
        context.getBuyList().forEach(buyBean -> buyBean.setCustRiskLevel(queryAccKycInfoResult == null ? null : queryAccKycInfoResult.getRiskToleranceLevel()));
        if (isValidatorRisk(request.getFundCode(), request.getTxAcctNo(), request.getDisCode(), highProductInfoBean.getPeDivideCallFlag())) {
            // 校验客户风险等级: 是否过期; 是否与购买的产品等级匹配
            validatorHighRiskLevelNew(request.getTxAcctNo(), queryAccKycInfoResult, customerInfo.getCustInfo().getQualificationType(), request.getRiskFlag(), highProductInfoBean.getFundRiskLevel(), getForceRiskMatch(highProductInfoBean), customerInfo.getCustInfo().getInvstType());
        }
        // 10.拆分子订单 申请金额、预估手续费、净申请金额
        splitSubOrderAppAmtAndFee(request, highProductInfoBean.getFeeCalMode(), context.getBuyList(), abstractSubsOrPurLogicProcess);
        // 11.预约日历处理
        txControlProcess(request, context, highProductInfoBean);
        // 12.设置地址信息
        setAddressInfo(context.getOrderExtendBean(), request);
        // 13.设置客户风险评测信息
        if (queryAccKycInfoResult != null) {
            setRiskInfo(context.getOrderExtendBean(), queryAccKycInfoResult);
        }
    }

    public void setAddressInfo(OrderCreateContext.OrderExtendBean orderExtendBean, BaseMergeSubsOrPurRequest request) {
        // 0.前置过滤
        List<String> needList = new ArrayList<>();
        needList.add(TxChannelEnum.WEBSITE.getCode());
        needList.add(TxChannelEnum.WAP.getCode());
        needList.add(TxChannelEnum.APP.getCode());
        needList.add(TxChannelEnum.COUNTER.getCode());
        if (!needList.contains(request.getTxChannel())) {
            logger.info("不是线上下单,不需要保存地址");
            return;
        }
        List<String> needDisCodeList = new ArrayList<>();
        needDisCodeList.add(DisCodeEnum.HZ.getCode());
        needDisCodeList.add(DisCodeEnum.HM.getCode());
        if (!needDisCodeList.contains(request.getDisCode())) {
            logger.info("不是好臻,好买下单,不需要保存地址");
            return;
        }
        // 1.客户联系地址
        QueryCustInfoResult custInfoResult = queryCustInfoOuterService.queryCustInfoPlaintextByDisCode(request.getTxAcctNo(), request.getDisCode());
        if (custInfoResult == null || StringUtils.isBlank(custInfoResult.getInvstType()) || !InvstTypeEnum.INDI.getCode().equals(custInfoResult.getInvstType())) {
            logger.info("用户信息为空,或者不是个人用户,不需要保存地址");
            return;
        }
        // 客户联系地址
        String address = "";
        if (!org.springframework.util.StringUtils.isEmpty(custInfoResult.getCountyCode())) {
            Map<String, String> geographyInfo = queryAddressService.getGeographyInfo(custInfoResult.getProvCode(), custInfoResult.getCityCode(), custInfoResult.getCountyCode());
            if (geographyInfo != null) {
                if (!org.springframework.util.StringUtils.isEmpty(geographyInfo.get(custInfoResult.getProvCode()))) {
                    address += geographyInfo.get(custInfoResult.getProvCode());

                }
                if (!org.springframework.util.StringUtils.isEmpty(geographyInfo.get(custInfoResult.getCityCode()))) {
                    address += geographyInfo.get(custInfoResult.getCityCode());

                }
                if (!org.springframework.util.StringUtils.isEmpty(geographyInfo.get(custInfoResult.getCountyCode()))) {
                    address += geographyInfo.get(custInfoResult.getCountyCode());
                }
            }
        }
        address += custInfoResult.getAddr();
        orderExtendBean.setLiveAddress(address);
        // 2.身份证地址
        String idCardAddress = queryCustInfoOuterService.queryIdCardAddress(request.getHbOneNo(), request.getDisCode());
        orderExtendBean.setCardAddress(idCardAddress);
    }

    /**
     * @param orderExtendBean
     * @param queryAccKycInfoResult
     * @return void
     * @description:(设置风测信息)
     * <AUTHOR>
     * @date 2025/5/30 11:11
     * @since JDK 1.8
     */
    private void setRiskInfo(OrderCreateContext.OrderExtendBean orderExtendBean, QueryAccKycInfoResult queryAccKycInfoResult) {
        if (queryAccKycInfoResult != null) {
            // 专业投资者留痕 投资者类型认证日期
            if (InvestorTypeEnum.PRO.getValue().equals(queryAccKycInfoResult.getInvestorType())) {
                orderExtendBean.setInvestorQualifiedDate(queryAccKycInfoResult.getInvestorQualifiedDate());
            } else {
                // 非专业投资者留痕 风险评测日期
                orderExtendBean.setRiskToleranceDate(queryAccKycInfoResult.getRiskToleranceDate());
            }
        }
    }

    /**
     * 是否是首单
     */
    public abstract void firstBuyFlagProcess(BaseMergeSubsOrPurRequest request, OrderCreateContext context);

    public void validBlackTxAcctNos(String txAcctNo) {
        // 特殊用户不允许购买，展示已售罄
        if (!org.springframework.util.StringUtils.isEmpty(blackTxAcctNos)
                && !org.springframework.util.StringUtils.isEmpty(txAcctNo)
                && blackTxAcctNos.contains(txAcctNo)) {
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_LOCKCOUNT_NOT_ENOUGH,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_LOCKCOUNT_NOT_ENOUGH));
        }
    }


    /**
     * 手续费校验
     *
     * @param request 下单入参
     * @param context 订单上下文
     */
    public void validateFee(BaseMergeSubsOrPurRequest request, OrderCreateContext context, AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess) {
        if (DisCodeEnum.HZ.getCode().equals(request.getDisCode())) {
            context.setFeeRateBusinessCode(BusinessCodeEnum.SUBSCRIBE);
        } else {
            context.setFeeRateBusinessCode(context.getBusinessCode());
        }
        // 获取计算费用的金额
        BigDecimal buildFeeBaseAmt = getBuildFeeBaseAmt(abstractSubsOrPurLogicProcess, request, context);
        BigDecimal totalAppointAmt = getTotalAppointAmt(context);
        // 1.获取费用
        BigDecimal fee = abstractSubsOrPurLogicProcess.getFee(buildFeeBaseAmt, totalAppointAmt, request, context);
        // 2.校验费用
        // 校验预估手续费是否正确
        AmtOrVolValidator.validateEsitmateFee(request.getEsitmateFee(), fee);
    }

    /**
     * 获取计算费用的基础金额
     */
    protected abstract BigDecimal getBuildFeeBaseAmt(AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess, BaseMergeSubsOrPurRequest request, OrderCreateContext context);

    protected abstract BigDecimal getTotalAppointAmt(OrderCreateContext context);

    public HighProductFeeRateBean getFeeRateInfo(String fundCode, String feeRateBusinessCode, String invstType, String shareClass, BigDecimal feeBaseAmt) {
        return queryHighProductOuterService.getFundFeeRateByAmt(fundCode, feeRateBusinessCode, invstType, shareClass, feeBaseAmt);

    }

    /**
     * 预约日历处理
     *
     * @param request             请求入参
     * @param context             下单上下文
     * @param highProductInfoBean 产品信息
     */
    private void txControlProcess(BaseMergeSubsOrPurRequest request, OrderCreateContext context, HighProductInfoBean highProductInfoBean) {
        // 直销黑名单校验
        validateBlacklistDirect(request.getHbOneNo(), highProductInfoBean.getFundCode());
        // 上报TA日
        String submitTaDt;
        // 中台业务码
        String mBusiCode;
        // 交易递延标识
        String advanceFlag;
        // 开放截止日
        String openEndDt = null;
        // 支付截止日期
        String payDeadlineDtm = null;
        // 支付日期
        String pmtDt = null;
        // 产品支持提前下单标志
        String isScheduledTrade = highProductInfoBean.getIsScheduledTrade();
        if (StringUtils.isEmpty(isScheduledTrade)) {
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL));
        }

        // 核心业务处理
        // 根据当前TaTradeDt与募集结束日比较, 得出具体业务码
        // 募集结束日期
        String ipoEndDt = highProductInfoBean.getIpoEndDt();
        if (StringUtils.isEmpty(ipoEndDt)) {
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL));
        }
        mBusiCode = BusinessCodeEnum.SUBS.getMCode();
        if (context.getTaTradeDt().compareTo(ipoEndDt) > 0) {
            mBusiCode = BusinessCodeEnum.PURCHASE.getMCode();
        }
        // 查询高端产品交易开通配置信息
        HighProductTxOpenCfgBean highProductTxOpenCfgBean = queryHighProductOuterService.getHighProductTxOpenCfg(highProductInfoBean.getFundCode(), BusinessCodeEnum.getByMCode(mBusiCode).getCode());
        // 校验交易是否开通
        ProductInfoValidator.validateTxOpenFlag(highProductTxOpenCfgBean);
        // 预约信息处理
        if (isAdvanceBuy(mBusiCode, isScheduledTrade)) {
            // 查询预约信息
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDateWithDeferPurchaseConfig(
                    request.getHbOneNo(), highProductInfoBean.getFundCode(), "0", highProductInfoBean.getShareClass(), request.getDisCode(), context.getAppDtm());
            if (productAppointmentInfoBean == null) {
                throw new ValidateException(ExceptionCodes.APPOINTMENT_INFO_IS_NULL,
                        MessageSource.getMessageByCode(ExceptionCodes.APPOINTMENT_INFO_IS_NULL));
            }
            // 交易是否在可购买期
            checkBuyDate(request, context, productAppointmentInfoBean);

            if (StringUtils.isNotEmpty(productAppointmentInfoBean.getPayDeadlineDtm())) {
                pmtDt = productAppointmentInfoBean.getPayDeadlineDtm().substring(0, 8);
            }
            submitTaDt = productAppointmentInfoBean.getOpenEndDt();
            // 转换日期=min(上报日，打款截止日）
            if (StringUtils.isEmpty(pmtDt) || (StringUtils.isNotEmpty(submitTaDt) && pmtDt.compareTo(submitTaDt) > 0)) {
                pmtDt = submitTaDt;
            }
            openEndDt = productAppointmentInfoBean.getOpenEndDt();
            payDeadlineDtm = productAppointmentInfoBean.getPayDeadlineDtm();
            // 预约日历ID
            context.setAppointId(productAppointmentInfoBean.getAppointId());
            context.setProductAppointmentInfoBean(productAppointmentInfoBean);
        } else {
            submitTaDt = context.getTaTradeDt();
        }
        advanceFlag = AdvanceFlagEnum.TRADE_COMMON.getCode();

        // 查询高端产品状态信息
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductInfoBean.getFundCode(), submitTaDt);
        if (StringUtils.isEmpty(mBusiCode)) {
            mBusiCode = ConvertCodeUtils.convertBuyBusiCode(highProductStatInfoBean.getFundStat()).getMCode();
        }
        // 验证产品状态与业务码是否匹配
        ProductInfoValidator.valdiateProductStat(highProductStatInfoBean, mBusiCode);
        // 设置属性
        context.setSubmitTaDt(submitTaDt);
        context.setBusinessCode(BusinessCodeEnum.getByMCode(mBusiCode));
        for (OrderCreateContext.BuyBean buyBean : context.getBuyList()) {
            buyBean.setAdvanceFlag(advanceFlag);
            buyBean.setOpenEndTime(openEndDt);
            buyBean.setPayDeadlineDtm(payDeadlineDtm);
            buyBean.setSupportAdvanceFlag(isScheduledTrade);
            buyBean.setPmtDt(pmtDt);
            // 支付对账日
            if (isAdvanceBuy(mBusiCode, isScheduledTrade)) {
                // 自划款的预约交易，支付对账日期为远端的开放截止日
                if (PaymentTypeEnum.SELF_DRAWING.getCode().equals(buyBean.getPaymentType())) {
                    buyBean.setPmtCheckDt(openEndDt);
                } else {
                    buyBean.setPmtCheckDt(context.getTaTradeDt());
                }
            } else {
                buyBean.setPmtCheckDt(context.getTaTradeDt());
            }
        }
    }

    /**
     * validateBlacklistDirect:(直销黑名单校验)
     *
     * @param hbOneNo  一账通
     * @param fundCode 产品编码
     */
    private void validateBlacklistDirect(String hbOneNo, String fundCode) {
        CmBlacklistDirectPo cmBlacklistDirectPo = cmBlacklistDirectRepository.selectByHbOneNoAndFundCode(hbOneNo, fundCode);
        if (cmBlacklistDirectPo != null) {
            throw new ValidateException(ExceptionCodes.IN_DIRECT_BLACK_LIST, MessageSource.getMessageByCode(ExceptionCodes.IN_DIRECT_BLACK_LIST));
        }
    }

    /**
     * 是否预约购买
     */
    private boolean isAdvanceBuy(String mBusiCode, String isScheduledTrade) {
        return BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)
                || (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)
                && (IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(isScheduledTrade)
                || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade)));
    }

    /**
     * 校验购买日期
     */
    private static void checkBuyDate(BaseMergeSubsOrPurRequest request, OrderCreateContext context, ProductAppointmentInfoBean productAppointmentInfoBean) {
        if (!TxChannelEnum.COUNTER.getCode().equals(request.getTxChannel())
                && (StringUtils.isNotEmpty(productAppointmentInfoBean.getBuyDay()) && StringUtils.isNotEmpty(productAppointmentInfoBean.getBuyTime()))) {
            String buyDateStr = productAppointmentInfoBean.getBuyDay() + productAppointmentInfoBean.getBuyTime();
            Date buyDate = DateUtils.formatToDate(buyDateStr, DateUtils.YYYYMMDDHHMMSS);
            if (buyDate == null || buyDate.compareTo(context.getAppDtm()) > 0) {
                throw new ValidateException(ExceptionCodes.NOT_PRODUCT_BUY_DATE_ERROR,
                        MessageSource.getMessageByCode(ExceptionCodes.NOT_PRODUCT_BUY_DATE_ERROR));
            }
        }
    }

    /**
     * 拆分申请金额、预估手续费、净申请金额
     *
     * @param request
     * @param feeCalMode
     * @param buyList
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/26 10:29
     * @since JDK 1.8
     */
    private void splitSubOrderAppAmtAndFee(BaseMergeSubsOrPurRequest request, String feeCalMode,
                                           List<OrderCreateContext.BuyBean> buyList, AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess) {
        // 净值购买金额
        BigDecimal totalAppAmt = request.getAppAmt();
        BigDecimal totalEsitmateFee = request.getEsitmateFee();
        BigDecimal accumEsitmateFee = BigDecimal.ZERO;

        // 按比例拆分（不计算最后一张卡）
        for (int i = 0; i < buyList.size() - 1; i++) {
            OrderCreateContext.BuyBean buyBean = buyList.get(i);
            BigDecimal appAmt = buyBean.getAppAmt();
            // 当前卡预估手续费 【总预估手续费 * 当前卡占比】
            BigDecimal esitmateFee = MergeSubmitUtils.subOrderAmtSplitByRate(totalEsitmateFee, appAmt, totalAppAmt);
            buyBean.setEsitmateFee(esitmateFee);
            accumEsitmateFee = accumEsitmateFee.add(esitmateFee);
            // 当前卡净申请金额 【申请金额 - 预估手续费】
            BigDecimal netAppAmt = abstractSubsOrPurLogicProcess.calNetAppAmt(appAmt, esitmateFee, feeCalMode);
            buyBean.setNetAppAmt(netAppAmt);
        }

        // 计算最后一张卡（剩余部分全部计入，避免尾差导致总额不一致）
        OrderCreateContext.BuyBean buyBean = buyList.get(buyList.size() - 1);
        // 预估手续费 【总预估手续费 - 其它卡预估手续费】
        BigDecimal esitmateFee = totalEsitmateFee.subtract(accumEsitmateFee);
        buyBean.setEsitmateFee(esitmateFee);
        // 净申请金额 【申请金额 - 预估手续费】
        BigDecimal netAppAmt = abstractSubsOrPurLogicProcess.calNetAppAmt(buyBean.getAppAmt(), esitmateFee, feeCalMode);
        buyBean.setNetAppAmt(netAppAmt);
    }


    private String getForceRiskMatch(HighProductInfoBean highProductInfoBean) {
        if (StringUtils.isEmpty(highProductInfoBean.getForceMatchRiskFlag())) {
            if (ProductTypeEnum.ZHUANHU.getCode().equals(highProductInfoBean.getFundType())) {
                return YesOrNoEnum.YES.getCode();
            } else if (ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(highProductInfoBean.getFundType())) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        } else {
            return highProductInfoBean.getForceMatchRiskFlag();
        }
    }

    /**
     * validCustKeyInfo:反洗钱校验
     *
     * @param txAcctNo
     * <AUTHOR>
     * @date 2019年6月26日 下午2:55:47
     */
    private void validCustKeyInfo(String txAcctNo, String disCode) {
        QueryCustInfoResult result = queryAllCustInfoOuterService.checkUserInfoComplete(txAcctNo, disCode);
        if (result == null) {
            throw new ValidateException(ExceptionCodes.HIGH_ORDER_CHECK_CUST_KEY_INFO_FAIL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_CHECK_CUST_KEY_INFO_FAIL));
        }
    }

    public static void validatorHighRiskLevelNew(String txAcctNo, QueryAccKycInfoResult queryAccKycInfoResult, String qualificationType,
                                                 String riskFlag, String productRiskLevel, String forceRiskMatch, String invstType) {
        if (StringUtils.isEmpty(txAcctNo) || StringUtils.isEmpty(qualificationType) || StringUtils.isEmpty(productRiskLevel)) {
            throw new ValidateException(ExceptionCodes.PARAM_IS_NULL, MessageSource.getMessageByCode(ExceptionCodes.PARAM_IS_NULL));
        }
        // 产品用户不校验风险等级
        if (InvstTypeEnum.PRODUCT.getCode().equals(invstType)) {
            return;
        }
        if (com.howbuy.interlayer.product.enums.YesOrNoEnum.YES.getCode().equals(forceRiskMatch)) {
            riskFlag = null;// 强制校验风险等级
        }
        // 验证风险等级
        CustRiskValidator.validateRiskLevelNew(txAcctNo, riskFlag, productRiskLevel, queryAccKycInfoResult);
    }

    /**
     * queryCustRiskSurvey:(查询客户风险评测信息)
     *
     * @param txAcctNo
     * @param disCode
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午6:26:10
     */
    private QueryAccKycInfoResult queryCustRiskSurvey(String txAcctNo, String disCode) {
        QueryAccKycInfoResult queryAccKycInfoResult = queryAccKycInfoOuterService.queryAccKycInfoByTxAcctNo(txAcctNo, disCode);
        if (DisCodeEnum.OTC_MIDDLE.getCode().equals(disCode)) {
            // 机构问卷类型为空,或者为机构类型
            if (!StringUtil.isBlank(queryAccKycInfoResult.getRiskToleranceExamType()) && !ExamTypeEnum.INSTITUTION.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType())) {
                logger.error("机构的问卷类型,必须为空或者机构,不然就是问卷缺失");
                return null;
            }
        } else {
            if (!ExamTypeEnum.HIGH_END.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType()) && !ExamTypeEnum.INSTITUTION.getValue().equals(queryAccKycInfoResult.getRiskToleranceExamType())) {
                return null;
            }
        }
        return queryAccKycInfoResult;
    }


    /**
     * validateProductAndTaInfo:(验证产品和TA信息)
     *
     * <AUTHOR>
     * @date 2018年5月21日 下午5:33:58
     */
    private void validateProductAndTaInfo(HighProductInfoBean highProductInfoBean,
                                          BaseMergeSubsOrPurRequest request,
                                          String appointmentType,
                                          AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess) {
        // 校验高端产品信息是否存在
        ProductInfoValidator.validateProductInfoExsit(highProductInfoBean);
        // 校验产品类型是否为高端产品: 专户, 私募
        ProductInfoValidator.validateProductClass(highProductInfoBean.getProductClass());
        // 校验TA状态
        TaInfoValidator.validateTaStat(highProductInfoBean.getTaStat());
        // 定向开放校验
        abstractSubsOrPurLogicProcess.validateDirectOpenFlag(appointmentType, highProductInfoBean.getDirectOpenFlag());
        // 校验TA支持多卡
        validateTaSupportCard(request, highProductInfoBean);
    }


    /**
     * validateTaSupportCard:(TA支持卡校验)
     *
     * @param request
     * @param highProductInfoBean
     * @return
     * <AUTHOR>
     * @date 2018年5月21日 下午5:31:23
     */
    private void validateTaSupportCard(BaseMergeSubsOrPurRequest request, HighProductInfoBean highProductInfoBean) {
        String supportCardType = highProductInfoBean.getSupportCardType();
        if (StringUtils.isEmpty(supportCardType)) {
            throw new ValidateException(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED));
        }

        // ta支持多卡, 不校验
        if (SupportCardTypeEnum.TA_MULTI_CARD.getCode().equals(supportCardType)) {
            return;
        }

        // 产品单卡, ta单卡
        String fundCode = SupportCardTypeEnum.PRODUCT_SINGLE_CARD.getCode().equals(supportCardType) ? highProductInfoBean.getFundCode() : null;
        String taCode = SupportCardTypeEnum.TA_SINGLE_CARD.getCode().equals(supportCardType) ? highProductInfoBean.getTaCode() : null;

        // 获取历史持仓的资金账号
        List<String> hisCpAcctNoList = custBooksRepository.selectHisCpAcctNo(request.getTxAcctNo(), request.getDisCode(), fundCode, taCode);
        if (hisCpAcctNoList.size() > 1) {
            throw new ValidateException(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED));
        }

        String hisCpAcctNo;
        if (hisCpAcctNoList.isEmpty()) {
            // 获取历史交易记录的资金账号
            hisCpAcctNoList = dealOrderRepository.selectHisCpAcctNo(request.getTxAcctNo(), request.getDisCode(), fundCode, taCode);
            if (hisCpAcctNoList.size() > 1) {
                throw new ValidateException(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED));
            }
            if (hisCpAcctNoList.isEmpty()) {
                // 首次合并购买校验多笔支付之间是否同一张卡
                if (request.getPayList().size() > 1) {
                    String cpAcctNo = request.getPayList().get(0).getCpAcctNo();
                    for (PayInfoBean pay : request.getPayList()) {
                        if (!pay.getCpAcctNo().equals(cpAcctNo)) {
                            throw new ValidateException(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED,
                                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED));
                        }
                    }
                }
                return;
            }
            hisCpAcctNo = hisCpAcctNoList.get(0);
        } else {
            hisCpAcctNo = hisCpAcctNoList.get(0);
        }

        for (PayInfoBean pay : request.getPayList()) {
            if (!pay.getCpAcctNo().equals(hisCpAcctNo)) {
                throw new ValidateException(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_MULTICARDFLAG_VALIDATE_FAILED));
            }
        }
    }


    /**
     * validateAcctAndBank:(验证账户及银行卡信息)
     *
     * @param custInfo
     * @param paymentType
     * <AUTHOR>
     * @date 2017年7月7日 下午6:20:24
     */
    private void validateAcctAndBank(QueryAllCustInfoResult custInfo, String paymentType, String taCode) {
        // 校验账户状态:交易账户, 分销交易账户, 基金交易账户
        TxAcctValidator.validatAcctStat(custInfo.getCustInfo().getTxAcctNo(), custInfo.getAcTxAcct(), custInfo.getDisAcTxAcct());
        // 校验银行账户信息
        CustBankCardValidator.validateBankCardPaySignStat(paymentType, custInfo.getCustBankCardInfo());
    }

    /**
     * createOrder:交易订单创建,根据对应业务创建交易需要的订单信息
     *
     * @param context
     * @return OrderCreateBean
     * <AUTHOR>
     * @date 2017年3月16日 下午4:37:12
     */
    @Override
    public OrderCreateBean createOrder(OrderCreateContext context) {
        OrderCreateBean bean = new OrderCreateBean();
        createCustProtocol(context, bean);
        createDealOrder(context, bean);
        createDealOrderDtl(context, bean);
        createPaymentOrder(context, bean);
        createCustBooksDtl(context, bean);
        createDealOrderExtend(context, bean);
        createHighOrderAppointinfo(context, bean);
        // 2.添加表单提交记录
        setFormNo(context, bean.getOrderDtlList().get(0).getDealNo());
        return bean;
    }

    /**
     * 关联表单信息
     */
    private void setFormNo(OrderCreateContext context, String mainOrderNo) {
        // 如果入参里面有表单号,直接更新
        if (StringUtils.isNotEmpty(context.getFormNo())) {
            customerSubmitFormRepository.updateBusinessNoByFormNo(context.getFormNo(), mainOrderNo);
        }
    }

    /**
     * 创建合并上报订单
     */
    @Override
    public List<OrderCreateBean> createMergeOrders(OrderCreateContext context) {
        List<OrderCreateBean> list = null;
        // 获取投资协议信息，如果没有则创建一个
        CustProtocolBean custProtocol = createCustProtocol(context);
        // 创建订单列表
        if (context.getRedeemList() != null && !context.getRedeemList().isEmpty()) {
            list = new ArrayList<>(context.getRedeemList().size());
            // 赎回
            List<OrderCreateBean> finalList = list;
            context.getRedeemList().forEach(redeemBean -> {
                // 设置当前循环的赎回信息
                context.setCustInfo(redeemBean.getCustInfo());
                context.setRedeemBean(redeemBean);

                // 创建合并上报子订单
                OrderCreateBean subOrder = createMergeSubOrder(custProtocol, context);
                finalList.add(subOrder);
            });
        } else if (context.getBuyList() != null && !context.getBuyList().isEmpty()) {
            list = new ArrayList<>(context.getBuyList().size());
            // 购买
            List<OrderCreateBean> finalList = list;
            context.getBuyList().forEach(buyBean -> {
                // 设置当前循环的赎回信息
                context.setCustInfo(buyBean.getCustInfo());
                context.setBuyBean(buyBean);

                // 创建合并上报子订单
                OrderCreateBean subOrder = createMergeSubOrder(custProtocol, context);
                finalList.add(subOrder);
            });
        }
        if (list != null) {
            String mainOrderNo = list.get(0).getOrderDtlList().get(0).getDealNo();
            if (list.size() > 1) {
                // 合并上报订单
                for (int i = 0; i < list.size(); i++) {
                    OrderCreateBean order = list.get(i);
                    // 设置合并上报标识及主订单号
                    HighDealOrderDtlPo orderDtlPo = order.getOrderDtlList().get(0);
                    orderDtlPo.setMergeSubmitFlag(YesOrNoEnum.YES.getCode());
                    orderDtlPo.setMainDealOrderNo(mainOrderNo);
                    if (i > 0) {
                        // 生成非主订单的外部订单号（唯一）
                        order.getOrderPo().setExternalDealNo(order.getOrderPo().getExternalDealNo() + i);
                        // 除主订单外的所有订单设置为非新协议，避免重复插入协议号
                        order.setNewProtocol(false);
                    }
                }
            }
            // 添加表单提交记录
            setFormNo(context, mainOrderNo);
        }
        return list;
    }

    /**
     * 创建合并上报子订单
     *
     * @param custProtocol
     * @param context
     * @return com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean
     * @author: huaqiang.liu
     * @date: 2021/3/1 16:29
     * @since JDK 1.8
     */
    private OrderCreateBean createMergeSubOrder(CustProtocolBean custProtocol, OrderCreateContext context) {
        // 设置协议信息
        OrderCreateBean bean = new OrderCreateBean();
        if (custProtocol != null) {
            bean.setNewProtocol(custProtocol.isNewProtocol);
            bean.setCustProtocolPo(custProtocol.getCustProtocolPo());
        }

        // 生成订单信息
        createDealOrder(context, bean);
        createDealOrderDtl(context, bean);
        createPaymentOrder(context, bean);
        createCustBooksDtl(context, bean);
        createDealOrderExtend(context, bean);
        createHighOrderAppointinfo(context, bean);
        createDealOrderRefund(context, bean);
        return bean;
    }

    /**
     * 回可用信息
     */
    private void createDealOrderRefund(OrderCreateContext context, OrderCreateBean bean) {
        HighDealOrderDtlPo orderDtl = bean.getOrderDtlList().get(0);
        if (!RedeemDirectionEnum.isRefundFinaAvail(orderDtl.getRedeemDirection())) {
            return;
        }
        DealOrderRefundPo refundPo = new DealOrderRefundPo();
        refundPo.setDealNo(bean.getOrderPo().getDealNo());
        refundPo.setRefundDirection(orderDtl.getRedeemDirection());
        refundPo.setRefundAmt(context.getRedeemBean().getRefundAmt());
        refundPo.setRefundMemo(context.getRedeemBean().getRefundMemo());
        refundPo.setSubmitFlag(NotifySubmitFlagEnum.NO_NEED.getCode());
        refundPo.setCreateDtm(context.getNow());
        refundPo.setUpdateDtm(context.getNow());
        bean.setDealOrderRefundPo(refundPo);
    }

    /**
     * createCustBooksDtl:创建账本明细信息，当前只有赎回、转换、跨TA转换才生成账本明细信息
     *
     * @param context
     * @param bean
     * @return void
     * <AUTHOR>
     * @date 2017年3月17日 下午4:49:33
     */
    private void createCustBooksDtl(OrderCreateContext context, OrderCreateBean bean) {
        String mBusiCode = context.getBusinessCode().getMCode();
        if (!BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode) && !BusinessCodeEnum.FUND_EXCHANGE.getMCode().equals(mBusiCode)
                && !BusinessCodeEnum.FUND_TRANSFER.getMCode().equals(mBusiCode) && !BusinessCodeEnum.FUND_SHARE_MERGE.getMCode().equals(mBusiCode)
                && !BusinessCodeEnum.FUND_SHARE_TRANSFER.getMCode().equals(mBusiCode)) {
            return;
        }
        List<HighDealOrderDtlPo> orderDtlList = bean.getOrderDtlList();

        if (CollectionUtils.isEmpty(orderDtlList)) {
            return;
        }

        DealOrderPo dealOrderPo = bean.getOrderPo();

        List<CustBooksDtlPo> booksDtlList = new ArrayList<CustBooksDtlPo>();
        for (HighDealOrderDtlPo po : orderDtlList) {
            CustBooksDtlPo custBooksDtlPo = new CustBooksDtlPo();
            Object[] objs = CustBooksAmtOrVolUtils.calculate(po.getAppVol(), mBusiCode, false);
            BigDecimal appVol = (BigDecimal) objs[0];
            ChangeBusiCodeEnum changeBusiCode = (ChangeBusiCodeEnum) objs[1];
            custBooksDtlPo.setAppVol(appVol);
            custBooksDtlPo.setChangeBusiCode(changeBusiCode.getCode());
            custBooksDtlPo.setRecordNo(sequenceService.getCustBooksDtlNo(po.getTxAcctNo()));
            custBooksDtlPo.setDealNo(po.getDealNo());
            custBooksDtlPo.setDealDtlNo(po.getDealDtlNo());
            custBooksDtlPo.setTxAcctNo(dealOrderPo.getTxAcctNo());
            custBooksDtlPo.setDisCode(dealOrderPo.getDisCode());
            custBooksDtlPo.setDisTxAcctNo(dealOrderPo.getDisTxAcctNo());
            custBooksDtlPo.setTradeDt(dealOrderPo.getAppDate());
            custBooksDtlPo.setTradeTm(dealOrderPo.getAppTime());
            custBooksDtlPo.setTaTradeDt(po.getSubmitTaDt());
            custBooksDtlPo.setProductType(po.getFundType());
            custBooksDtlPo.setProductCode(po.getFundCode());
            custBooksDtlPo.setFundShareClass(po.getFundShareClass());
            custBooksDtlPo.setProductName(po.getFundName());
            if (BusinessCodeEnum.FUND_SHARE_MERGE.getMCode().equals(po.getmBusiCode())
                    || BusinessCodeEnum.FUND_SHARE_TRANSFER.getMCode().equals(po.getmBusiCode())) {
                custBooksDtlPo.setProtocolNo(po.getProtocolNo());
                custBooksDtlPo.setProtocolType(po.getProtocolType());
                custBooksDtlPo.setCpAcctNo(po.getCpAcctNo());
            } else {
                custBooksDtlPo.setProtocolNo(dealOrderPo.getProtocolNo());
                custBooksDtlPo.setProtocolType(dealOrderPo.getProtocolType());
                custBooksDtlPo.setCpAcctNo(dealOrderPo.getCpAcctNo());
            }
            custBooksDtlPo.setProductClass(po.getProductClass());
            custBooksDtlPo.setTaCode(po.getTaCode());
            custBooksDtlPo.setProductChannel(po.getProductChannel());
            booksDtlList.add(custBooksDtlPo);
        }
        bean.setBooksDtlList(booksDtlList);
    }

    /**
     * 生成外部信息
     *
     * @param context
     * @param bean
     */
    public abstract void createDealOrderExtend(OrderCreateContext context, OrderCreateBean bean);


    /**
     * createHighOrderAppointinfo:(创建预约信息)
     */
    private void createHighOrderAppointinfo(OrderCreateContext context, OrderCreateBean bean) {

        OrderCreateContext.HighOrderAppointInfoBean highOrderAppointinfoBean = context.getHighOrderAppointInfoBean();
        if (highOrderAppointinfoBean == null) {
            return;
        }

        HighOrderAppointinfoPo highOrderAppointinfoPo = new HighOrderAppointinfoPo();
        DealOrderPo DealOrderPo = bean.getOrderPo();
        highOrderAppointinfoPo.setDealNo(DealOrderPo.getDealNo());
        highOrderAppointinfoPo.setAppointmentDealNo(highOrderAppointinfoBean.getAppointmentDealNo());
        highOrderAppointinfoPo.setCreateDtm(context.getNow());
        highOrderAppointinfoPo.setUpdateDtm(context.getNow());

        bean.setHighOrderAppointinfoPo(highOrderAppointinfoPo);


    }

    /**
     * createDealOrder:创建订单信息
     *
     * @param context
     * @param bean
     * @return void
     * <AUTHOR>
     * @date 2017年3月17日 下午4:51:31
     */
    private void createDealOrder(OrderCreateContext context, OrderCreateBean bean) {
        OrderTradeBaseRequest request = context.getOrderTradeBaseRequest();
        CustInfoBean custInfo = context.getCustInfo().getCustInfo();
        DisAcTxAcctBean disAcTxAcct = context.getCustInfo().getDisAcTxAcct();
        CustBankCardInfoBean custBankCardInfo = context.getCustInfo().getCustBankCardInfo();
        HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();
        CustProtocolPo protocolPo = bean.getCustProtocolPo();

        DealOrderPo dealOrderPo = new DealOrderPo();
        // 客户订单号
        dealOrderPo.setDealNo(sequenceService.getDealNo(request.getTxAcctNo()));
        // 交易账号
        dealOrderPo.setTxAcctNo(request.getTxAcctNo());
        // 分销机构
        dealOrderPo.setDisCode(request.getDisCode());
        // 分销交易账号
        dealOrderPo.setDisTxAcctNo(disAcTxAcct.getDisTxAcctNo());
        // 网点号
        dealOrderPo.setOutletCode(request.getOutletCode());
        // 资金账号
        dealOrderPo.setCpAcctNo(custBankCardInfo.getCpAcctNo());
        // 子交易账号
        dealOrderPo.setSubTxAcctNo(custBankCardInfo.getSubTxAcctNo());
        // 银行编码
        dealOrderPo.setBankCode(custBankCardInfo.getBankCode());
        // 银行账号
        dealOrderPo.setBankAcct(PrivacyUtil.encryptBankAcct(custBankCardInfo.getBankAcct()));
        // 客户姓名
        dealOrderPo.setCustName(custInfo.getCustName());
        // 证件类型
        dealOrderPo.setIdType(custInfo.getIdType());
        // 证件号码
        dealOrderPo.setIdNo(PrivacyUtil.encryptIdCard(custInfo.getIdNo()));
        // 中台交易代码
        dealOrderPo.setTxCode(request.getTxCode());
        // 产品名称
        dealOrderPo.setProductName(highProductInfoBean.getFundAttr());
        // 产品代码
        dealOrderPo.setProductCode(highProductInfoBean.getFundCode());
        // 产品风险等级
        dealOrderPo.setProductRiskLevel(highProductInfoBean.getFundRiskLevel());
        // 基金类型
        dealOrderPo.setProductType(highProductInfoBean.getFundType());
        // 基金二级类型
        dealOrderPo.setProductSubType(highProductInfoBean.getFundSubType());
        if (protocolPo != null) {
            // 协议名称
            dealOrderPo.setProtocolName(protocolPo.getProtocolName());
            // 协议号
            dealOrderPo.setProtocolNo(protocolPo.getProtocolNo());
            // 协议号类型
            dealOrderPo.setProtocolType(protocolPo.getProtocolType());
        } else {
            // 协议号(修改分红方式)
            dealOrderPo.setProtocolNo(context.getProtocolNo());
            // 协议类型(修改分红方式)
            dealOrderPo.setProtocolType(context.getProtocolType());
        }
        // 产品类别
        dealOrderPo.setProductClass(highProductInfoBean.getProductClass());
        OrderCreateContext.BuyBean buyBean = context.getBuyBean();
        // 购买属性
        if (buyBean != null) {
            // 申请金额
            dealOrderPo.setAppAmt(buyBean.getAppAmt());
            // 支付方式
            dealOrderPo.setPaymentType(buyBean.getPaymentType());
            // 递延标识
            String advanceFlag = buyBean.getAdvanceFlag();
            if (StringUtils.isEmpty(advanceFlag)) {
                advanceFlag = AdvanceFlagEnum.TRADE_COMMON.getCode();
            }
            // 递延标识
            dealOrderPo.setAdvanceFlag(advanceFlag);
        }

        OrderCreateContext.RedeemBean redeemBean = context.getRedeemBean();
        // 赎回属性
        if (redeemBean != null) {
            dealOrderPo.setAppVol(redeemBean.getAppVol());
        }

        String mBusiCode = context.getBusinessCode().getMCode();
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
            // 付款状态
            dealOrderPo.setPayStatus(PayStatusEnum.UN_PAY.getCode());
        } else {
            // 付款状态
            dealOrderPo.setPayStatus(PayStatusEnum.NO_NEED.getCode());
        }
        // 订单状态
        dealOrderPo.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());
        // 交易渠道
        dealOrderPo.setTxChannel(request.getTxChannel());
        // 投资者类型
        dealOrderPo.setInvstType(custInfo.getInvstType());
        // IP地址
        dealOrderPo.setIpAddress(request.getOperIp());
        // 数据跟踪
        dealOrderPo.setDataTrack(request.getDataTrack());
        // ta交易日
        dealOrderPo.setTaTradeDt(context.getTaTradeDt());
        // 订单类型
        dealOrderPo.setDealType(DealTypeEnum.FUND_HIGH.getCode());
        // 外部订单号
        dealOrderPo.setExternalDealNo(request.getExternalDealNo());
        // 产品通道
        dealOrderPo.setProductChannel(highProductInfoBean.getProductChannel());
        // 申请日期
        dealOrderPo.setAppDate(request.getAppDt());
        // 申请时间
        dealOrderPo.setAppTime(request.getAppTm());
        // 交易申请日期时间
        dealOrderPo.setAppDtm(context.getAppDtm());
        // 更新日期时间
        dealOrderPo.setUpdateDtm(context.getNow());
        dealOrderPo.setCreateDtm(context.getNow());
        // 中台业务码
        dealOrderPo.setzBusiCode(context.getzBusiCode());
        // 投资者类型
        dealOrderPo.setQualificationType(custInfo.getQualificationType());
        bean.setOrderPo(dealOrderPo);
    }

    /**
     * createDealOrderDtl:创建订单明细信息
     */
    private void createDealOrderDtl(OrderCreateContext context, OrderCreateBean bean) {
        DealOrderPo dealOrderPo = bean.getOrderPo();
        HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();

        HighDealOrderDtlPo orderDtl = new HighDealOrderDtlPo();
        // 客户订单明细号
        orderDtl.setDealDtlNo(sequenceService.getDealDtlNo(dealOrderPo.getTxAcctNo()));
        // 交易账号
        orderDtl.setTxAcctNo(dealOrderPo.getTxAcctNo());
        // 分销机构号
        orderDtl.setDisCode(dealOrderPo.getDisCode());
        // 网点号
        orderDtl.setOutletCode(dealOrderPo.getOutletCode());
        // 客户订单号
        orderDtl.setDealNo(dealOrderPo.getDealNo());
        // 基金代码
        orderDtl.setFundCode(highProductInfoBean.getFundCode());
        // ta代码
        orderDtl.setTaCode(highProductInfoBean.getTaCode());
        // 基金检查名称
        orderDtl.setFundName(highProductInfoBean.getFundAttr());
        // 基金类型
        orderDtl.setFundType(highProductInfoBean.getFundType());
        // 基金二级类型
        orderDtl.setFundSubType(highProductInfoBean.getFundSubType());
        // 基金份额类型
        orderDtl.setFundShareClass(highProductInfoBean.getShareClass());
        // 申请金额
        orderDtl.setAppAmt(dealOrderPo.getAppAmt());
        // 申请份额
        orderDtl.setAppVol(dealOrderPo.getAppVol());
        // 预申请份额
        orderDtl.setPreAppVol(dealOrderPo.getAppVol());
        // 订单申请标记
        orderDtl.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());
        // 手续费计算模式
        orderDtl.setFeeCalMode(highProductInfoBean.getFeeCalMode());

        // 设置电子合同版本号
        orderDtl.setContractVersion(context.getContractVersion());

        CustInfoBean custInfo = context.getCustInfo().getCustInfo();
        // 投资者类别
        orderDtl.setQualificationType(custInfo.getQualificationType());

        String mBusiCode = context.getBusinessCode().getMCode();
        // 只有认申购才需要支付
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
            // 通知上报标记
            orderDtl.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NEED.getCode());
            // 冷静期
            orderDtl.setCalmTime(highProductInfoBean.getCalmTime());
        } else {
            // 通知上报标记
            orderDtl.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NOTIFY.getCode());
        }
        // Ta交易日
        orderDtl.setTaTradeDt(dealOrderPo.getTaTradeDt());
        // 上报ta日期
        orderDtl.setSubmitTaDt(context.getSubmitTaDt());
        // 中台业务代码
        orderDtl.setmBusiCode(context.getBusinessCode().getMCode());
        // 基金分红方式
        orderDtl.setFundDivMode(context.getFundDivMode());
        // 产品类别
        orderDtl.setProductClass(highProductInfoBean.getProductClass());
        // 产品通道
        orderDtl.setProductChannel(highProductInfoBean.getProductChannel());
        // 设置赎回信息
        setRedeemInfo(context, orderDtl);
        // 设置下单购买信息
        setBuyInfo(context, highProductInfoBean, orderDtl);
        // 备注
        orderDtl.setMemo("");
        orderDtl.setUpdateDtm(context.getNow());
        // 创建日期时间
        orderDtl.setCreateDtm(context.getNow());
        // 上报TA日期干预标识
        orderDtl.setSubmitTaDtInterposeFlag(InterposeFlagEnum.UN_DONE.getCode());
        // 预约日历ID
        orderDtl.setAppointId(context.getAppointId());
        // 是否高端定投
        orderDtl.setHighFundInvPlanFlag(context.getHighFundInvPlanFlag());
        List<HighDealOrderDtlPo> orderDtlList = new ArrayList<HighDealOrderDtlPo>();
        orderDtlList.add(orderDtl);
        bean.setOrderDtlList(orderDtlList);

    }

    /**
     * 设置下单购买信息
     */
    private void setBuyInfo(OrderCreateContext context, HighProductInfoBean highProductInfoBean, HighDealOrderDtlPo orderDtl) {
        OrderCreateContext.BuyBean buyBean = context.getBuyBean();
        // 购买属性
        if (buyBean != null) {
            // 预约折扣
            orderDtl.setAppointmentDiscount(buyBean.getAppointmentDiscount());
            orderDtl.setEsitmateFee(buyBean.getEsitmateFee());
            orderDtl.setFee(buyBean.getFee());
            // 首次购买标识
            orderDtl.setFirstBuyFlag(buyBean.getFirstBuyFlag());
            // 预约开放截止日
            orderDtl.setOpenEndTime(buyBean.getOpenEndTime());
            // 预约金额
            orderDtl.setAdvanceAmt(buyBean.getAppointmentAmt());
            // 提前下单标识
            orderDtl.setSupportAdvanceFlag(buyBean.getSupportAdvanceFlag());
            // 风险确认标记
            orderDtl.setRiskFlag(buyBean.getRiskFlag());
            // 折扣
            orderDtl.setDiscountRate(buyBean.getDiscountRate());
            // 产品风险等级
            orderDtl.setFundRiskLevel(buyBean.getFundRiskLevel());
            // 客户风险等级
            orderDtl.setCustRiskLevel(buyBean.getCustRiskLevel());
            // 成单方式
            orderDtl.setOrderFormType(buyBean.getOrderFormType());
            // 折扣金额
            orderDtl.setDiscountAmt(buyBean.getDiscountAmt());
            // 预约单类型
            orderDtl.setAppointmentDealNoType(buyBean.getAppointmentType());
            orderDtl.setDualentryStatus(buyBean.getDualentryStatus());
            orderDtl.setDualentryInterposeFlag(buyBean.getDualentryInterposeFlag());
            orderDtl.setCallbackStatus(buyBean.getCallbackStatus());
            orderDtl.setCallbackInterposeFlag(buyBean.getCallbackInterposeFlag());
            orderDtl.setCalmdtmInterposeFlag(buyBean.getCalmdtmInterposeFlag());
            orderDtl.setAssetcertificateStatus(buyBean.getAssetcertificateStatus());
            orderDtl.setAssetInterposeFlag(buyBean.getAssetInterposeFlag());
            // 预计到期日期
            orderDtl.setPreExpireDate(buyBean.getPreExpireDate());
            // 双录完成时间
            orderDtl.setDualentryFinishDtm(buyBean.getDualentryFinishDtm());
            // 净申请金额
            orderDtl.setNetAppAmt(context.getBuyBean().getNetAppAmt());
            // 认缴金额
            orderDtl.setSubsAmt(buyBean.getSubsAmt());

            // 设置固收产品到期赎回标识
            if (YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getFixedIncomeFlag())) {
                if (StringUtils.isEmpty(context.getBuyBean().getIsRedeemExpire())) {
                    // 到期是否赎回
                    orderDtl.setIsRedeemExpire(YesOrNoEnum.YES.getCode());
                } else {
                    // 到期是否赎回
                    orderDtl.setIsRedeemExpire(buyBean.getIsRedeemExpire());
                }
            }
        }
    }

    /**
     * 设置赎回信息
     *
     * @param context
     * @param orderDtl
     */
    private void setRedeemInfo(OrderCreateContext context, HighDealOrderDtlPo orderDtl) {
        OrderCreateContext.RedeemBean redeemBean = context.getRedeemBean();
        // 赎回属性
        if (redeemBean != null) {
            // 赎回去向
            orderDtl.setRedeemDirection(redeemBean.getRedeemDirection());
            // 大额赎回标志
            orderDtl.setLargeRedmFlag(redeemBean.getLargeRedmFlag());
            // 可赎回日期
            orderDtl.setAllowDt(redeemBean.getAllowDt());
            // 特殊交易标识
            orderDtl.setUnusualTransType(redeemBean.getUnusualTransType());
            // 是否支持提前下单标识
            orderDtl.setSupportAdvanceFlag(redeemBean.getSupportAdvanceFlag());
            // 成单方式
            orderDtl.setOrderFormType(redeemBean.getOrderFormType());
            // 预约订单ID创建方式
            orderDtl.setAppointmentDealNoType(redeemBean.getAppointmentType());
            // 复购意向
            orderDtl.setRepurchaseProtocolNo(redeemBean.getRepurchaseProtocolNo());
        }
    }

    /**
     * createPaymentOrder:创建支付订单信息，目前只有认申购交易才创建支付订单信息
     */
    private void createPaymentOrder(OrderCreateContext context, OrderCreateBean bean) {
        String mBusiCode = context.getBusinessCode().getMCode();
        // 只有认申购才需要支付
        if (!BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) && !BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
            return;
        }

        DealOrderPo dealOrderPo = bean.getOrderPo();

        PaymentOrderPo paymentOrderPo = new PaymentOrderPo();
        paymentOrderPo.setPmtDealNo(sequenceService.getPmtDealNo(dealOrderPo.getTxAcctNo()));// 支付订单号
        paymentOrderPo.setDealNo(dealOrderPo.getDealNo());// 客户订单号
        paymentOrderPo.setDisCode(dealOrderPo.getDisCode());// 分销机构
        paymentOrderPo.setOutletCode(dealOrderPo.getOutletCode());// 网点
        paymentOrderPo.setProtocolNo(dealOrderPo.getProtocolNo());// 协议号
        paymentOrderPo.setTxChannel(dealOrderPo.getTxChannel());// 交易渠道
        paymentOrderPo.setPaymentType(dealOrderPo.getPaymentType());// 支付方式
        paymentOrderPo.setTxAcctNo(dealOrderPo.getTxAcctNo());// 交易账号
        paymentOrderPo.setCpAcctNo(dealOrderPo.getCpAcctNo());// 资金账号
        paymentOrderPo.setSubTxAcctNo(dealOrderPo.getSubTxAcctNo());// 子交易账号
        paymentOrderPo.setCustName(dealOrderPo.getCustName());// 客户姓名
        paymentOrderPo.setIdType(dealOrderPo.getIdType());// 证件类型
        paymentOrderPo.setIdNo(dealOrderPo.getIdNo());// 证件号码
        paymentOrderPo.setBankAcct(dealOrderPo.getBankAcct());// 银行账号
        paymentOrderPo.setBankCode(dealOrderPo.getBankCode());// 银行代码
        paymentOrderPo.setSubBankName(context.getBuyBean().getSubBankName()); // 支行名称
        paymentOrderPo.setPmtAmt(dealOrderPo.getAppAmt());// 支付金额
        paymentOrderPo.setTxPmtFlag(TxPmtFlagEnum.UN_PAY.getCode());// 交易支付标记
        paymentOrderPo.setPmtCompFlag(PmtCompFlagEnum.NOT_NEED.getCode());// 支付对账状态
        paymentOrderPo.setCurrency(CurrencyEnum.RMB.getCode());// 币种
        paymentOrderPo.setAppDtm(context.getAppDtm());
        paymentOrderPo.setUpdateDtm(context.getNow());// 更新日期时间
        paymentOrderPo.setCreateDtm(context.getNow());// 更新日期时间
        paymentOrderPo.setPmtCheckDt(context.getBuyBean().getPmtCheckDt()); // 支付对账日期
        paymentOrderPo.setDealType(dealOrderPo.getDealType()); // 订单类型
        paymentOrderPo.setProductChannel(dealOrderPo.getProductChannel()); // 产品通道

        String payDeadlineDtm = context.getBuyBean().getPayDeadlineDtm(); // 打款截止日期时间
        // 获取一账通号
        String hboneNo = null;
        if (StringUtils.isNotEmpty(dealOrderPo.getTxAcctNo())) {
            hboneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(dealOrderPo.getTxAcctNo());
        }

        // 不支持预约的产品，
        // 且认购订单，打款截止日需要按照日历上的打款截止日发起支付，
        // 如果是申购订单，自划款支付接口中打款截止日默认为20991231。
        if (IsScheduledTradeEnum.NotSupportAdvance.getCode().equals(context.getHighProductInfoBean().getIsScheduledTrade())) {
            if (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
                if (PaymentTypeEnum.SELF_DRAWING.getCode().equals(dealOrderPo.getPaymentType())) {
                    payDeadlineDtm = "20991231000000";
                }
            } else if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)) {
                HighProductInfoBean product = context.getHighProductInfoBean();
                // 查询预约信息
                ProductAppointmentInfoBean appointDate = queryHighProductOuterService.queryAppointmentInfoByAppointDateWithDeferPurchaseConfig(
                        hboneNo, product.getFundCode(), "0", product.getShareClass(), dealOrderPo.getDisCode(), context.getAppDtm());
                if (StringUtils.isEmpty(payDeadlineDtm)) {
                    payDeadlineDtm = appointDate.getPayDeadlineDtm();
                }
            }
        }

        if (StringUtils.isEmpty(payDeadlineDtm)) {
            payDeadlineDtm = context.getSubmitTaDt() + "150000";
        }

        String pmtDt = context.getBuyBean().getPmtDt();
        // 判断是否满足储蓄罐预约支付条件，满足，设置支付状态为未冻结
        if (PaymentTypeEnum.PIGGY.getCode().equals(dealOrderPo.getPaymentType()) && context.getHighProductInfoBean().getPaymentTypeList().length() >= 4) {
            String pmtType = "" + context.getHighProductInfoBean().getPaymentTypeList().charAt(3);
            if (PaymentTypeEnum.PIGGY.getCode().equals(dealOrderPo.getPaymentType()) && YesOrNoEnum.YES.getCode().equals(pmtType) && StringUtils.isNotEmpty(pmtDt) && pmtDt.compareTo(dealOrderPo.getAppDate()) > 0) {
                paymentOrderPo.setTxPmtFlag(TxPmtFlagEnum.PIGGY_UN_FRZ.getCode());// 未冻结
            }
        }

        paymentOrderPo.setPayDeadlineDtm(DateUtil.formatToDate(payDeadlineDtm, DateUtil.YYYYMMDDHHMMSS));
        paymentOrderPo.setPmtDt(pmtDt);
        bean.setPaymentOrderPo(paymentOrderPo);
    }

    /**
     * createCustProtocol:获取投资协议信息，如果没有投资协议信息则创建一个投资协议
     *
     * @param context
     * @param bean
     * @return void
     * <AUTHOR>
     * @date 2017年3月17日 下午4:49:06
     */
    private void createCustProtocol(OrderCreateContext context, OrderCreateBean bean) {
        CustProtocolBean custProtocol = createCustProtocol(context);
        bean.setNewProtocol(custProtocol.isNewProtocol);
        bean.setCustProtocolPo(custProtocol.getCustProtocolPo());
    }

    /**
     * 获取投资协议信息，如果没有投资协议信息则创建一个投资协议
     */
    private CustProtocolBean createCustProtocol(OrderCreateContext context) {
        // 修改分红方式, 不涉及客户协议
        OrderTradeBaseRequest orderTradeBaseRequest = context.getOrderTradeBaseRequest();
        String txAcctNo = orderTradeBaseRequest.getTxAcctNo();
        String disCode = orderTradeBaseRequest.getDisCode();
        String protocolNo = context.getProtocolNo();
        String protocolType = context.getProtocolType();

        CustProtocolPo custProtocol = null;
        CustProtocolBean protocolBean = new CustProtocolBean();
        protocolBean.setNewProtocol(false);
        if (StringUtils.isNotEmpty(protocolNo)) {
            custProtocol = custProtocolRepository.selectByProtocolAndDisCode(txAcctNo, protocolNo, disCode);
            if (custProtocol == null) {
                throw new BusinessException(ExceptionCodes.ORDER_CENTER_CUST_PROTOCOL_IS_NULL_ERROR,
                        MessageSource.getMessageByCode(ExceptionCodes.ORDER_CENTER_CUST_PROTOCOL_IS_NULL_ERROR));
            }
            protocolBean.setCustProtocolPo(custProtocol);
            return protocolBean;
        }

        custProtocol = custProtocolRepository.selectByProtocolTypeAndDisCode(txAcctNo, protocolType, disCode, MDataDic.DEFAULT_PRODUCT_CODE);
        if (custProtocol == null) {
            custProtocol = new CustProtocolPo();
            protocolNo = sequenceService.getProtocolNo(txAcctNo);
            // 协议号
            custProtocol.setProtocolNo(protocolNo);
            // 交易账号
            custProtocol.setTxAcctNo(txAcctNo);
            // 分销机构
            custProtocol.setDisCode(disCode);
            // 协议号名称
            custProtocol.setProtocolName("");
            // 协议号类型
            custProtocol.setProtocolType(protocolType);
            // 组合产品代码
            custProtocol.setProductCode(MDataDic.DEFAULT_PRODUCT_CODE);
            custProtocol.setSubTypeCode("1");
            // 产品变更版本
            custProtocol.setChangeVersion(1);
            // 创建时间
            custProtocol.setCreateDtm(context.getNow());
            // 更新时间
            custProtocol.setUpdateDtm(context.getNow());
            // 协议开关
            custProtocol.setOpenFlag(SwitchEnum.OPEN.getCode());
            protocolBean.setNewProtocol(true);
        }
        protocolBean.setCustProtocolPo(custProtocol);
        return protocolBean;
    }

    /**
     * createShareMergeOrder:创建份额合并/迁移订单
     */
    public BatchOrderCreateBean createShareMergeOrder(OrderCreateContext context) {
        BatchOrderCreateBean bean = new BatchOrderCreateBean();
        createInShareMergeDealOrder(context, bean);
        createOutShareMergeDealOrder(context, bean);
        createShareMergeDealOrderDtl(context, bean);
        createShareMergeCustBooksDtl(context, bean);
        createShareMergeDealOrderExtend(context, bean);
        return bean;
    }

    /**
     * createInShareMergeDealOrder:创建份额合并/迁移订单信息
     */
    private void createInShareMergeDealOrder(OrderCreateContext context, BatchOrderCreateBean bean) {
        OrderTradeBaseRequest request = context.getOrderTradeBaseRequest();
        CustInfoBean custInfo = context.getCustInfo().getCustInfo();
        DisAcTxAcctBean disAcTxAcct = context.getCustInfo().getDisAcTxAcct();
        CustBankCardInfoBean custBankCardInfo = context.getTargetCustInfo().getCustBankCardInfo();
        Map<String, HighProductBaseInfoBean> map = context.getHighProductBaseInfoBeanMap();
        List<DealOrderPo> dealOrderList = new ArrayList<>();
        List<OrderCreateContext.ShareMergeBean> shareMergeList = context.getOutShareMergeBeanList();
        for (OrderCreateContext.ShareMergeBean shareMergeBean : shareMergeList) {
            HighProductBaseInfoBean productBean = map.get(shareMergeBean.getFundCode());
            DealOrderPo dealOrderPo = new DealOrderPo();
            // 客户订单号
            dealOrderPo.setDealNo(sequenceService.getDealNo(request.getTxAcctNo()));
            // 交易账号
            dealOrderPo.setTxAcctNo(request.getTxAcctNo());
            // 分销机构
            dealOrderPo.setDisCode(request.getDisCode());
            // 分销交易账号
            dealOrderPo.setDisTxAcctNo(disAcTxAcct.getDisTxAcctNo());
            // 网点号
            dealOrderPo.setOutletCode(request.getOutletCode());
            // 资金账号
            dealOrderPo.setCpAcctNo(custBankCardInfo.getCpAcctNo());
            // 子交易账号
            dealOrderPo.setSubTxAcctNo(custBankCardInfo.getSubTxAcctNo());
            // 银行编码
            dealOrderPo.setBankCode(custBankCardInfo.getBankCode());
            // 银行账号
            dealOrderPo.setBankAcct(PrivacyUtil.encryptBankAcct(custBankCardInfo.getBankAcct()));
            // 客户姓名
            dealOrderPo.setCustName(custInfo.getCustName());
            // 证件类型
            dealOrderPo.setIdType(custInfo.getIdType());
            // 证件号码
            dealOrderPo.setIdNo(PrivacyUtil.encryptIdCard(custInfo.getIdNo()));
            // 中台交易代码
            dealOrderPo.setTxCode(request.getTxCode());
            // 产品名称
            dealOrderPo.setProductName(productBean.getFundAttr());
            // 产品代码
            dealOrderPo.setProductCode(productBean.getFundCode());
            // 产品风险等级
            dealOrderPo.setProductRiskLevel(productBean.getFundRiskLevel());
            // 基金类型
            dealOrderPo.setProductType(productBean.getFundType());
            // 基金二级类型
            dealOrderPo.setProductSubType(productBean.getFundSubType());
            // 产品类别
            dealOrderPo.setProductClass(productBean.getProductClass());
            // 协议号
            dealOrderPo.setProtocolNo(context.getProtocolNo());
            // 协议类型
            dealOrderPo.setProtocolType(context.getProtocolType());
            // 付款状态
            dealOrderPo.setPayStatus(PayStatusEnum.NO_NEED.getCode());
            // 订单状态
            dealOrderPo.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());
            // 交易渠道
            dealOrderPo.setTxChannel(request.getTxChannel());
            // 投资者类型
            dealOrderPo.setInvstType(custInfo.getInvstType());
            // IP地址
            dealOrderPo.setIpAddress(request.getOperIp());
            // 数据跟踪
            dealOrderPo.setDataTrack(request.getDataTrack());
            // ta交易日
            dealOrderPo.setTaTradeDt(context.getTaTradeDt());
            // 订单类型
            dealOrderPo.setDealType(DealTypeEnum.FUND_HIGH.getCode());
            // 外部订单号
            dealOrderPo.setExternalDealNo(dealOrderPo.getDealNo());
            // 产品通道
            dealOrderPo.setProductChannel(productBean.getProductChannel());

            dealOrderPo.setAppVol(shareMergeBean.getAppVol());
            // 申请日期
            dealOrderPo.setAppDate(request.getAppDt());
            // 申请时间
            dealOrderPo.setAppTime(request.getAppTm());
            // 交易申请日期时间
            dealOrderPo.setAppDtm(context.getAppDtm());
            // 更新日期时间
            dealOrderPo.setUpdateDtm(context.getNow());
            dealOrderPo.setCreateDtm(context.getNow());
            // 中台业务码
            dealOrderPo.setzBusiCode(context.getzBusiCode());
            dealOrderList.add(dealOrderPo);
        }
        bean.setOrderPoList(dealOrderList);
    }

    /**
     * createOutShareMergeDealOrder:创建份额合并/迁移订单信息
     *
     * @param context
     * @param bean
     * <AUTHOR>
     * @date 2018年5月10日 下午4:28:14
     */
    private void createOutShareMergeDealOrder(OrderCreateContext context, BatchOrderCreateBean bean) {
        OrderTradeBaseRequest request = context.getOrderTradeBaseRequest();
        CustInfoBean custInfo = context.getCustInfo().getCustInfo();
        DisAcTxAcctBean disAcTxAcct = context.getCustInfo().getDisAcTxAcct();
        CustBankCardInfoBean custBankCardInfo = context.getCustInfo().getCustBankCardInfo();
        Map<String, HighProductBaseInfoBean> map = context.getHighProductBaseInfoBeanMap();
        List<DealOrderPo> dealOrderList = new ArrayList<>();
        List<OrderCreateContext.ShareMergeBean> shareMergeList = context.getOutShareMergeBeanList();
        for (OrderCreateContext.ShareMergeBean shareMergeBean : shareMergeList) {
            HighProductBaseInfoBean productBean = map.get(shareMergeBean.getFundCode());
            DealOrderPo dealOrderPo = new DealOrderPo();
            dealOrderPo.setDealNo(sequenceService.getDealNo(request.getTxAcctNo()));// 客户订单号
            dealOrderPo.setTxAcctNo(request.getTxAcctNo());// 交易账号
            dealOrderPo.setDisCode(request.getDisCode());// 分销机构
            dealOrderPo.setDisTxAcctNo(disAcTxAcct.getDisTxAcctNo());// 分销交易账号
            dealOrderPo.setOutletCode(request.getOutletCode());// 网点号
            dealOrderPo.setCpAcctNo(custBankCardInfo.getCpAcctNo());// 资金账号
            dealOrderPo.setSubTxAcctNo(custBankCardInfo.getSubTxAcctNo());// 子交易账号
            dealOrderPo.setBankCode(custBankCardInfo.getBankCode());// 银行编码
            dealOrderPo.setBankAcct(PrivacyUtil.encryptBankAcct(custBankCardInfo.getBankAcct()));// 银行账号
            dealOrderPo.setCustName(custInfo.getCustName());// 客户姓名
            dealOrderPo.setIdType(custInfo.getIdType());// 证件类型
            dealOrderPo.setIdNo(PrivacyUtil.encryptIdCard(custInfo.getIdNo()));// 证件号码
            dealOrderPo.setTxCode(request.getTxCode());// 中台交易代码
            dealOrderPo.setProductName(productBean.getFundAttr());// 产品名称
            dealOrderPo.setProductCode(productBean.getFundCode());// 产品代码
            dealOrderPo.setProductRiskLevel(productBean.getFundRiskLevel()); // 产品风险等级
            dealOrderPo.setProductType(productBean.getFundType()); // 基金类型
            dealOrderPo.setProductSubType(productBean.getFundSubType()); // 基金二级类型
            dealOrderPo.setProductClass(productBean.getProductClass());// 产品类别
            dealOrderPo.setProtocolNo(context.getProtocolNo()); // 协议号
            dealOrderPo.setProtocolType(context.getProtocolType()); // 协议类型
            dealOrderPo.setPayStatus(PayStatusEnum.NO_NEED.getCode());// 付款状态
            dealOrderPo.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());// 订单状态
            dealOrderPo.setTxChannel(request.getTxChannel());// 交易渠道
            dealOrderPo.setInvstType(custInfo.getInvstType());// 投资者类型
            dealOrderPo.setIpAddress(request.getOperIp());// IP地址
            dealOrderPo.setDataTrack(request.getDataTrack());// 数据跟踪
            dealOrderPo.setTaTradeDt(context.getTaTradeDt());// ta交易日
            dealOrderPo.setDealType(DealTypeEnum.FUND_HIGH.getCode()); // 订单类型
            dealOrderPo.setExternalDealNo(dealOrderPo.getDealNo());// 外部订单号
            dealOrderPo.setProductChannel(productBean.getProductChannel()); // 产品通道

            dealOrderPo.setAppVol(shareMergeBean.getAppVol());
            dealOrderPo.setAppDate(request.getAppDt());// 申请日期
            dealOrderPo.setAppTime(request.getAppTm());// 申请时间
            dealOrderPo.setAppDtm(context.getAppDtm());// 交易申请日期时间
            dealOrderPo.setUpdateDtm(context.getNow());// 更新日期时间
            dealOrderPo.setCreateDtm(context.getNow());

            dealOrderPo.setzBusiCode(context.getzBusiCode()); // 中台业务码
            dealOrderList.add(dealOrderPo);
        }
        bean.getOrderPoList().addAll(dealOrderList);
    }


    /**
     * createShareMergeDealOrderDtl:创建份额合并/迁移订单明细信息
     */
    private void createShareMergeDealOrderDtl(OrderCreateContext context, BatchOrderCreateBean bean) {
        List<HighDealOrderDtlPo> orderDtlList = Lists.newArrayList();
        Map<String, HighProductBaseInfoBean> highProductBaseBeanMap = context.getHighProductBaseInfoBeanMap();
        Map<String, String> fundCodeAndDtlNoMap = new HashMap<>();
        for (DealOrderPo po : bean.getOrderPoList()) {
            CustBankCardInfoBean custBankCardInfo = null;
            String mBusiCode = null;
            String dealDtlNo = sequenceService.getDealDtlNo(po.getTxAcctNo());
            // 资金账号是出，则明细记录入；资金账号是入，则明细记录出
            if (po.getCpAcctNo().equals(context.getCustInfo().getCustBankCardInfo().getCpAcctNo())) {
                custBankCardInfo = context.getTargetCustInfo().getCustBankCardInfo();
                mBusiCode = BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode();
            } else {
                custBankCardInfo = context.getCustInfo().getCustBankCardInfo();
                mBusiCode = BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode();
                fundCodeAndDtlNoMap.put(po.getProductCode(), dealDtlNo);
            }
            HighProductBaseInfoBean highProductBaseBean = highProductBaseBeanMap.get(po.getProductCode());

            HighDealOrderDtlPo outOrderDtl = new HighDealOrderDtlPo();
            outOrderDtl.setDealDtlNo(dealDtlNo);// 客户订单明细号
            outOrderDtl.setTxAcctNo(po.getTxAcctNo());// 交易账号
            outOrderDtl.setDisCode(po.getDisCode());// 分销机构号
            outOrderDtl.setOutletCode(po.getOutletCode());// 网点号
            outOrderDtl.setDealNo(po.getDealNo());// 客户订单号
            outOrderDtl.setFundCode(highProductBaseBean.getFundCode());// 基金代码
            outOrderDtl.setTaCode(highProductBaseBean.getTaCode());// ta代码
            outOrderDtl.setFundName(highProductBaseBean.getFundAttr());// 基金名称
            outOrderDtl.setFundType(highProductBaseBean.getFundType());// 基金类型
            outOrderDtl.setFundSubType(highProductBaseBean.getFundSubType());// 基金二级类型
            outOrderDtl.setFundShareClass(highProductBaseBean.getShareClass());// 基金份额类型
            outOrderDtl.setAppVol(po.getAppVol());// 转出份额
            outOrderDtl.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());// 订单申请标记
            outOrderDtl.setmBusiCode(mBusiCode);// 中台业务名称代码
            outOrderDtl.setCpAcctNo(custBankCardInfo.getCpAcctNo());// 资金账号
            outOrderDtl.setBankCode(custBankCardInfo.getBankCode());// 银行编码
            outOrderDtl.setBankAcct(PrivacyUtil.encryptBankAcct(custBankCardInfo.getBankAcct()));// 银行账号
            outOrderDtl.setProtocolNo(po.getProtocolNo()); // 协议号
            outOrderDtl.setProtocolType(po.getProtocolType());
            outOrderDtl.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NOTIFY.getCode());// 通知上报标记
            outOrderDtl.setTaTradeDt(po.getTaTradeDt());// Ta交易日
            outOrderDtl.setSubmitTaDt(po.getTaTradeDt());// 上报ta日期
            outOrderDtl.setMemo("");// 备注
            outOrderDtl.setUpdateDtm(context.getNow());// 更新日期时间
            outOrderDtl.setCreateDtm(context.getNow());// 创建日期时间
            outOrderDtl.setProductChannel(po.getProductChannel()); // 产品通道
            outOrderDtl.setProductClass(highProductBaseBean.getProductClass());// 产品类别
            // 转出订单明细
            orderDtlList.add(outOrderDtl);
        }
        bean.setOrderDtlList(orderDtlList);
        bean.setFundCodeAndDealDtlNoMap(fundCodeAndDtlNoMap);
    }

    /**
     * createCustBooksDtl:创建账本明细信息，当前只有赎回、转换、跨TA转换才生成账本明细信息
     *
     * @param context
     * @param bean
     * @return void
     * <AUTHOR>
     * @date 2017年3月17日 下午4:49:33
     */
    private void createShareMergeCustBooksDtl(OrderCreateContext context, BatchOrderCreateBean bean) {
        String mBusiCode = context.getBusinessCode().getMCode();
        if (!BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode) && !BusinessCodeEnum.FUND_EXCHANGE.getMCode().equals(mBusiCode)
                && !BusinessCodeEnum.FUND_TRANSFER.getMCode().equals(mBusiCode) && !BusinessCodeEnum.FUND_SHARE_MERGE.getMCode().equals(mBusiCode)
                && !BusinessCodeEnum.FUND_SHARE_TRANSFER.getMCode().equals(mBusiCode)) {
            return;
        }
        List<HighDealOrderDtlPo> orderDtlList = bean.getOrderDtlList();

        if (CollectionUtils.isEmpty(orderDtlList)) {
            return;
        }

        DealOrderPo dealOrderPo = bean.getOrderPoList().get(0);
        List<CustBooksDtlPo> booksDtlList = new ArrayList<CustBooksDtlPo>();
        for (HighDealOrderDtlPo po : orderDtlList) {
            if (!BusinessCodeEnum.FUND_SHARE_TRANSFER_OUT.getMCode().equals(po.getmBusiCode())) {
                continue;
            }
            CustBooksDtlPo custBooksDtlPo = new CustBooksDtlPo();
            Object[] objs = CustBooksAmtOrVolUtils.calculate(po.getAppVol(), mBusiCode, false);
            BigDecimal appVol = (BigDecimal) objs[0];
            ChangeBusiCodeEnum changeBusiCode = (ChangeBusiCodeEnum) objs[1];
            custBooksDtlPo.setAppVol(appVol);
            custBooksDtlPo.setChangeBusiCode(changeBusiCode.getCode());
            custBooksDtlPo.setRecordNo(sequenceService.getCustBooksDtlNo(po.getTxAcctNo()));
            custBooksDtlPo.setDealNo(po.getDealNo());
            custBooksDtlPo.setDealDtlNo(po.getDealDtlNo());
            custBooksDtlPo.setTxAcctNo(po.getTxAcctNo());
            custBooksDtlPo.setDisCode(po.getDisCode());
            custBooksDtlPo.setDisTxAcctNo(dealOrderPo.getDisTxAcctNo());
            custBooksDtlPo.setTradeDt(dealOrderPo.getAppDate());
            custBooksDtlPo.setTradeTm(dealOrderPo.getAppTime());
            custBooksDtlPo.setTaTradeDt(po.getSubmitTaDt());
            custBooksDtlPo.setProductType(po.getFundType());
            custBooksDtlPo.setProductCode(po.getFundCode());
            custBooksDtlPo.setFundShareClass(po.getFundShareClass());
            custBooksDtlPo.setProductName(po.getFundName());
            custBooksDtlPo.setProtocolNo(po.getProtocolNo());
            custBooksDtlPo.setProtocolType(po.getProtocolType());
            custBooksDtlPo.setCpAcctNo(po.getCpAcctNo());
            custBooksDtlPo.setProductClass(po.getProductClass());
            custBooksDtlPo.setTaCode(po.getTaCode());
            custBooksDtlPo.setProductChannel(po.getProductChannel());
            booksDtlList.add(custBooksDtlPo);
        }
        bean.setBooksDtlList(booksDtlList);
    }

    /**
     * createDealOrderExtend:(创建交易订单扩展信息)
     *
     * @param context
     * @param bean
     * <AUTHOR>
     * @date 2017年3月29日 下午2:43:40
     */
    public void createShareMergeDealOrderExtend(OrderCreateContext context, BatchOrderCreateBean bean) {
        OrderCreateContext.OrderExtendBean orderExtendBean = context.getOrderExtendBean();
        if (orderExtendBean == null) {
            return;
        }
        List<DealOrderExtendPo> extendPos = new ArrayList<>();
        for (DealOrderPo po : bean.getOrderPoList()) {
            DealOrderExtendPo dealOrderExtendPo = new DealOrderExtendPo();
            dealOrderExtendPo.setDealNo(po.getDealNo());
            dealOrderExtendPo.setTransactorIdNo(orderExtendBean.getTransactorIdNo());
            dealOrderExtendPo.setTransactorIdType(orderExtendBean.getTransactorIdType());
            dealOrderExtendPo.setTransactorName(orderExtendBean.getTransactorName());
            dealOrderExtendPo.setOperatorNo(orderExtendBean.getOperatorNo());
            dealOrderExtendPo.setConsCode(orderExtendBean.getConsCode());
            dealOrderExtendPo.setAppointmentDealNo(orderExtendBean.getAppointmentDealNo());

            dealOrderExtendPo.setRiskAckDtm(orderExtendBean.getRiskAckDtm());
            dealOrderExtendPo.setNormalCustTipDtm(orderExtendBean.getNormalCustTipDtm());
            dealOrderExtendPo.setHighRiskTipDtm(orderExtendBean.getHighRiskTipDtm());
            dealOrderExtendPo.setPortfolioFlag(PortfolioFlagEnum.NOT_PORTFOLIO.getCode());

            dealOrderExtendPo.setCreateDtm(context.getNow());
            dealOrderExtendPo.setUpdateDtm(context.getNow());
            dealOrderExtendPo.setRecStat(RecStatEnum.EFFECTIVE.getCode());
            extendPos.add(dealOrderExtendPo);
        }
        bean.setDealOrderExtendPoList(extendPos);
    }

    @Getter
    @Setter
    private static class CustProtocolBean {
        /**
         * 协议是否新增,true:是，false：否
         */
        private boolean isNewProtocol = true;
        /**
         * 客户投资协议
         */
        private CustProtocolPo custProtocolPo;
    }


    /**
     * disCode是否匹配
     */
    public abstract boolean isThisDisCode(String disCode);

}
