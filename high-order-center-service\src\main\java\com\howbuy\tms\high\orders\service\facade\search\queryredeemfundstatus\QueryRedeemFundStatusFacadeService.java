/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryredeemfundstatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.WorkDayModel;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.TxOpenFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductTxOpenCfgBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse.RedeemFundStatusBean;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询产品赎回状态)
 * @reason:
 * @date 2017年11月30日 下午2:23:40
 * @since JDK 1.6
 */
@DubboService
@Service("queryRedeemFundStatusFacade")
public class QueryRedeemFundStatusFacadeService implements QueryRedeemFundStatusFacade {
    private static final Logger logger = LogManager.getLogger(QueryRedeemFundStatusFacadeService.class);

    @Autowired
    private TradeDayService tradeDayService;
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade.execute(QueryRedeemFundStatusRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryRedeemFundStatusFacadeService
     * @apiName execute
     * @apiDescription 查询产品赎回状态
     * @apiParam (请求参数) {Array} productCodeList 产品代码
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=nSs&productCodeList=a&pageSize=696&disCode=q95n26D&txChannel=L&appTm=yth&disCodeList=bpr&subOutletCode=pr8R&pageNo=5248&operIp=z08thGe&txAcctNo=ES&appDt=sR9g0SP&dataTrack=HM&txCode=CfQYcYJF&outletCode=8tDX1X
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {Array} redeemFundStatusList 产品状态list
     * @apiSuccess (响应结果) {String} redeemFundStatusList.productCode 产品代码
     * @apiSuccess (响应结果) {String} redeemFundStatusList.shareClass 收费类型A-前收费;B-后收费
     * @apiSuccess (响应结果) {String} redeemFundStatusList.sellAppointStartDt 赎回预约开始日期
     * @apiSuccess (响应结果) {String} redeemFundStatusList.sellAppointEndDt
     * @apiSuccess (响应结果) {Number} redeemFundStatusList.availVol
     * @apiSuccess (响应结果) {Number} redeemFundStatusList.lockVol 锁定份额
     * @apiSuccess (响应结果) {String} redeemFundStatusList.redeemStatus 产品状态0-不可谁 1-可赎回
     * @apiSuccess (响应结果) {String} redeemFundStatusList.redeemStatusType 状态标识1-正常 99-其它
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"95KqCypV","totalPage":3545,"pageNo":2538,"txAcctNo":"Ilezcfg","description":"R1iZ22IN","totalCount":9866,"redeemFundStatusList":[{"redeemStatusType":"3iTrbEqTN9","productCode":"MFQh13WeW","availVol":6692.310856307906,"shareClass":"QgovSNF","lockVol":6569.07809455086,"redeemStatus":"Gf","sellAppointStartDt":"0QIXE","sellAppointEndDt":"5nX7C"}]}
     */
    @Override
    public QueryRedeemFundStatusResponse execute(QueryRedeemFundStatusRequest request) {
        QueryRedeemFundStatusResponse response = new QueryRedeemFundStatusResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        List<RedeemFundStatusBean> list = new ArrayList<RedeemFundStatusBean>();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setRedeemFundStatusList(list);
        String appDtmStr = request.getAppDt() + request.getAppTm();
        Date appDate = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
        if (CollectionUtils.isNotEmpty(request.getProductCodeList())) {
            RedeemFundStatusBean bean = null;
            for (String productCode : request.getProductCodeList()) {
                bean = new RedeemFundStatusBean();
                // 获取高端产品信息
                HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
                if (highProductBaseInfoBean == null) {
                    bean.setProductCode(productCode);
                    bean.setShareClass("");
                    // 不可购买
                    bean.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                    // 参数配置有误
                    bean.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                    list.add(bean);
                    continue;
                }

                // 查询持仓接口
                String txAcctNo = request.getTxAcctNo();
                List<String> disCodeList = getDisCodeList(request);
                // 查询持仓
                CustBooksPo custBooksPo = custBooksRepository.selectCustAllBooksByTxAcctNoAndFundCode(txAcctNo, productCode, disCodeList);
                logger.info("QueryRedeemFundStatusFacadeService|productCode:{}, custBooksPo:{}", productCode, JSON.toJSONString(custBooksPo));
                if (custBooksPo == null) {
                    bean.setProductCode(productCode);
                    bean.setShareClass("");
                    // 不可购买
                    bean.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                    // 参数配置有误
                    bean.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                    list.add(bean);
                    continue;
                }
                if (DisCodeEnum.HZ.getCode().equals(custBooksPo.getDisCode()) && StringUtils.isNotBlank(custBooksPo.getDisCode())) {
                    logger.info("QueryRedeemFundStatusFacadeService-好臻产品不可以赎回");
                    bean.setProductCode(productCode);
                    bean.setShareClass("");
                    // 不可购买
                    bean.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                    // 参数配置有误
                    bean.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                    list.add(bean);
                    continue;
                }
                // 如果是循环锁定的产品,是否可以赎回用新逻辑
                if (YesOrNoEnum.YES.getCode().equals(highProductBaseInfoBean.getIsCyclicLock())) {
                    CustomerFundRedeemInfo customerFundRedeemInfo = queryCustomerRedeemAppointInfoLogicService.queryCustomerFundRedeemInfo(txAcctNo, productCode, appDate);
                    bean.setLockVol(customerFundRedeemInfo.getLockVol());
                    bean.setAvailVol(customerFundRedeemInfo.getTotalVol());
                    bean.setProductCode(productCode);
                    bean.setShareClass(highProductBaseInfoBean.getShareClass());
                    bean.setRedeemStatus(customerFundRedeemInfo.getRedeemStatus());
                    bean.setRedeemStatusType(customerFundRedeemInfo.getRedeemStatusType());
                    bean.setErrorMsg(customerFundRedeemInfo.getErrorMsg());
                    list.add(bean);
                    continue;
                }
                // 查询赎回预约日历
                ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(productCode, "1",
                        highProductBaseInfoBean.getShareClass(), request.getDisCode(), appDate);
                String openStartDt = null;
                if (isSupportAdvanceRedeem(highProductBaseInfoBean.getIsScheduledTrade()) && productAppointmentInfoBean != null) {
                    openStartDt = productAppointmentInfoBean.getOpenStartDt();
                }
                String currRedeemDay = getCurrRedeemDay(appDate, openStartDt);
                logger.info("QueryRedeemFundStatusFacadeService|productCode:{}, currRedeemDay:{}", productCode, currRedeemDay);
                BigDecimal lockVol = getLockVol(txAcctNo, productCode, currRedeemDay, disCodeList, highProductBaseInfoBean.getHasLockPeriod());
                logger.info("QueryRedeemFundStatusFacadeService|productCode:{}, lockVol:{}", productCode, lockVol);

                // 可用份额
                BigDecimal availVol = custBooksPo.getBalanceVol().subtract(custBooksPo.getUnconfirmedVol()).subtract(lockVol);
                bean.setAvailVol(availVol);

                // 锁定份额
                bean.setLockVol(lockVol);

                logger.info("QueryRedeemFundStatusFacadeService|productCode:{}, avail:{}, lockVol:{}", productCode, availVol, lockVol);

                //产品状态
                HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(productCode, currRedeemDay);

                // 是否可以赎回
                boolean canSell = getCanSell(highProductBaseInfoBean, highProductStatInfoBean, productAppointmentInfoBean, appDtmStr);
                if (canSell && BigDecimal.ZERO.compareTo(availVol) < 0) {
                    bean.setProductCode(productCode);
                    bean.setShareClass(highProductBaseInfoBean.getShareClass());
                    // 可赎回
                    bean.setRedeemStatus(RedeemStatusEnum.ALLOW.getCode());
                    // 正常
                    bean.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
                    list.add(bean);
                } else {
                    bean.setProductCode(productCode);
                    bean.setShareClass(highProductBaseInfoBean.getShareClass());
                    // 不可赎回
                    bean.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                    bean.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                    list.add(bean);
                }
            }
        }

        return response;

    }


    /**
     * getLockVol:(获取锁定份额)
     *
     * @param txAcctNo
     * @param fundCode
     * @param taTradeDt
     * @param disCodeList
     * @param hasLockPeriod
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午3:18:49
     */
    private BigDecimal getLockVol(String txAcctNo, String fundCode, String taTradeDt, List<String> disCodeList, String hasLockPeriod) {
        List<SubCustBooksPo> lockList = subCustBooksRepository.selectSubCustBooksByOpenRedeDt(disCodeList, txAcctNo, fundCode, null, null);
        BigDecimal lockVol = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(lockList)) {

            for (SubCustBooksPo subCustBooksPo : lockList) {
                if (RedeemTypeEnum.YES.getCode().equals(hasLockPeriod)) {
                    if (subCustBooksPo.getOpenRedeDt() != null && subCustBooksPo.getOpenRedeDt().compareTo(taTradeDt) > 0) {
                        lockVol = lockVol.add(subCustBooksPo.getBalanceVol() != null ? subCustBooksPo.getBalanceVol() : BigDecimal.ZERO);
                    }
                }
            }
        }

        return lockVol;
    }


    /**
     * 获取是否预约购买或赎回
     *
     * @param productAppointmentInfoBean 1-购买 2-赎回
     * @param highProductStatInfoBean
     * @param highProductBaseInfoBean
     * @param appDtmStr
     * @return
     */
    public boolean getCanSell(HighProductBaseInfoBean highProductBaseInfoBean, HighProductStatInfoBean highProductStatInfoBean,
                              ProductAppointmentInfoBean productAppointmentInfoBean, String appDtmStr) {
        if (highProductBaseInfoBean == null) {
            logger.info("产品基本信息为空,不可赎回");
            return false;
        }
        // 0-默认不可购买赎回
        boolean cando = false;

        if (ProductChannelEnum.TP_SM.getCode().equals(highProductBaseInfoBean.getProductChannel())
                || ProductChannelEnum.HIGH_FUND.getCode().equals(highProductBaseInfoBean.getProductChannel())) {
            // 查询高端产品交易开通配置信息
            HighProductTxOpenCfgBean highProductTxOpenCfgBean =
                    queryHighProductOuterService.getHighProductTxOpenCfg(highProductBaseInfoBean.getFundCode(), BusinessCodeEnum.REDEEM.getCode());

            // 校验交易是否开通
            if (highProductTxOpenCfgBean == null || TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
                return false;
            }
        }

        // 基金状态不可赎
        if (!validProductRedeemStatus(highProductStatInfoBean)) {
            return false;
        }

        // 不支持提前赎回，基金状态可赎回，可赎回
        if (!isSupportAdvanceRedeem(highProductBaseInfoBean.getIsScheduledTrade())) {
            return true;
        }

        // 支持提前赎回，没有预约开放日历
        if (null == productAppointmentInfoBean) {
            return false;
        }

        // 预约开始时间
        String appointStartDtm = "";
        // 预约结束时间
        String appointEndDtm = "";
        if (StringUtils.isNotBlank(productAppointmentInfoBean.getAppointStartDt())) {
            appointStartDtm = productAppointmentInfoBean.getAppointStartDt() + productAppointmentInfoBean.getAppointStartTm();
        }

        if (StringUtils.isNotBlank(productAppointmentInfoBean.getApponitEndDt())) {
            appointEndDtm = productAppointmentInfoBean.getApponitEndDt() + productAppointmentInfoBean.getApponitEndTm();
        }

        // 4-支持提前赎回，当前日期在可预约赎回期限内，可赎回
        if (isSupportAdvanceRedeem(highProductBaseInfoBean.getIsScheduledTrade()) && null != productAppointmentInfoBean.getAppointStartDt() && null != productAppointmentInfoBean.getApponitEndDt() && appointStartDtm.compareTo(appDtmStr) <= 0 && appointEndDtm.compareTo(appDtmStr) >= 0) {
            cando = true;
        }
        return cando;
    }

    /**
     * getCurrRedeemDay:(获取当前赎回交易日期)
     *
     * @param openStartDt 赎回开放日
     * @param nowDate     当前日期
     * @return
     * <AUTHOR>
     * @date 2017年10月10日 下午2:02:40
     */
    private String getCurrRedeemDay(Date nowDate, String openStartDt) {
        logger.info("getCurrRedeemDay nowDate:{},openStartDt:{}", nowDate, openStartDt);

        // 查询当前Ta交易日期
        WorkDayModel workDayModel = tradeDayService.getWorkDayModel(nowDate);
        String tradeDt = null;
        if (workDayModel != null) {
            tradeDt = workDayModel.getWorkday();
            logger.info("getCurrRedeemDay tradeDt:{}", tradeDt);
        }

        if (StringUtils.isNotEmpty(openStartDt)) {
            if (openStartDt.compareTo(tradeDt) > 0) {
                return openStartDt;
            }
        }

        return tradeDt;
    }

    public static boolean isSupportAdvanceRedeem(String supportAdvanceFlag) {
        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }

        if (SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)
                || SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
            return true;
        }
        return false;
    }

    /**
     * validProductRedeemStatus:(校验产品赎回状态)
     *
     * @param highProductStatInfoBean
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午2:08:08
     */
    public static boolean validProductRedeemStatus(HighProductStatInfoBean highProductStatInfoBean) {
        if (highProductStatInfoBean == null || StringUtils.isEmpty(highProductStatInfoBean.getFundStat())) {
            return false;
        }

        if (!MDataDic.CAN_REDEEM_SET.contains(highProductStatInfoBean.getFundStat())) {
            return false;
        }

        return true;
    }

    /**
     * @param request
     * @return java.util.List<java.lang.String>
     * @description:(获取分销机构代码)
     * @author: haiguang.chen
     * @date: 2022/1/4 11:04
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryRedeemFundStatusRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

}