package com.howbuy.tms.high.orders.service.config;

import com.howbuy.tms.high.orders.service.common.enums.HighOrderConstants;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description:线程池配置
 * @Author: yun.lu
 * Date: 2023/5/23 14:11
 */
@Configuration
public class ThreadPoolConfig {


    @Bean
    @Primary
    public ThreadPoolTaskExecutor threadPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(HighOrderConstants.HIGH_ORDER_DEFAULT_EVENT_POOL);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }


    @Bean(name = HighOrderConstants.HIGH_ORDER_DEFAULT_EVENT_POOL)
    public ThreadPoolTaskExecutor eventPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(HighOrderConstants.HIGH_ORDER_DEFAULT_EVENT_POOL);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }
}
