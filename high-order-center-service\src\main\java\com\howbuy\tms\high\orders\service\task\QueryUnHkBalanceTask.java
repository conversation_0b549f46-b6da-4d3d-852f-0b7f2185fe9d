package com.howbuy.tms.high.orders.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew.QueryBalanceContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

/**
 * @Description:非海外持仓
 * @Author: yun.lu
 * Date: 2025/8/1 18:18
 */
public class QueryUnHkBalanceTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryUnHkBalanceTask.class);
    private QueryBalanceContext queryBalanceContext;
    private QueryAcctBalanceRequest request;
    private Boolean hkProduct;
    private QueryAcctBalanceFacade queryAcctBalanceFacade;

    @Override
    protected void callTask() {
        log.info("queryUnHkAcctBalance-查询非海外持仓接口,queryBalanceParamCmd={}", JSON.toJSONString(request));
        if (hkProduct != null && hkProduct) {
            log.info("查询持仓的产品有值,而且是香港产品,不需要查询非香港持仓接口");
            QueryAcctBalanceResponse unHkBalanceResponse = new QueryAcctBalanceResponse();
            unHkBalanceResponse.setReturnCode(ExceptionCodes.SUCCESS);
            queryBalanceContext.setUnHkBalance(unHkBalanceResponse);
            return;
        }
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-start,queryAcctBalanceRequest={}", JSON.toJSONString(request));
        QueryAcctBalanceRequest queryAcctBalanceRequest = new QueryAcctBalanceRequest();
        BeanUtils.copyProperties(request, queryAcctBalanceRequest);
        // 查询非海外持仓固定过滤掉香港的
        queryAcctBalanceRequest.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
        QueryAcctBalanceResponse response = queryAcctBalanceFacade.execute(queryAcctBalanceRequest);
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-结果,response={}", JSON.toJSONString(response));
        queryBalanceContext.setUnHkBalance(response);
    }

    public QueryUnHkBalanceTask(QueryBalanceContext queryBalanceContext, QueryAcctBalanceRequest request, Boolean hkProduct, QueryAcctBalanceFacade queryAcctBalanceFacade) {
        this.queryBalanceContext = queryBalanceContext;
        this.request = request;
        this.hkProduct = hkProduct;
        this.queryAcctBalanceFacade = queryAcctBalanceFacade;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public QueryAcctBalanceRequest getRequest() {
        return request;
    }

    public void setRequest(QueryAcctBalanceRequest request) {
        this.request = request;
    }

    public Boolean getHkProduct() {
        return hkProduct;
    }

    public void setHkProduct(Boolean hkProduct) {
        this.hkProduct = hkProduct;
    }

    public QueryAcctBalanceFacade getQueryAcctBalanceFacade() {
        return queryAcctBalanceFacade;
    }

    public void setQueryAcctBalanceFacade(QueryAcctBalanceFacade queryAcctBalanceFacade) {
        this.queryAcctBalanceFacade = queryAcctBalanceFacade;
    }
}
