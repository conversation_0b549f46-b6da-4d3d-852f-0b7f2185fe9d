package com.howbuy.tms.high.orders.service.facade.search.queryfundackvolandamtinfo;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.TxAckFlagEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.high.orders.dao.vo.FundAckVolAndAmtVo;
import com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo.FundAckVolAndAmtInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo.QueryFundAckVolAndAmtInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo.QueryFundAckVolAndAmtInfoRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo.QueryFundAckVolAndAmtInfoResponse;
import com.howbuy.tms.high.orders.service.config.CommonValueConfig;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 查询基金确认金额与确认份额实现
 * @author: yun.lu
 * @date: 2025/3/20 14:30
 * @since JDK 1.8
 */
@Service
@Component
public class QueryFundAckVolAndAmtInfoFacadeService implements QueryFundAckVolAndAmtInfoFacade {

    private static final Logger logger = LogManager.getLogger(QueryFundAckVolAndAmtInfoFacadeService.class);

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private CommonValueConfig commonValueConfig;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfundackvolandamtinfo.QueryFundAckVolAndAmtInfoFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryFundAckVolAndAmtInfoFacadeService
     * @apiName execute
     * @apiDescription 查询基金确认金额与确认份额接口
     * @apiParam (请求参数) {Array} fundCodeList 基金代码列表
     * @apiParam (请求参数) {String} ackStartDt 确认开始日期，格式YYYYMMDD
     * @apiParam (请求参数) {String} ackEndDt 确认结束日期，格式YYYYMMDD
     * @apiParam (请求参数) {Array} mBusiCodeList 业务类型
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParamExample 请求参数示例
     * {"fundCodeList":["HM001","HM002"],"ackStartDt":"20250301","ackEndDt":"20250320","txAcctNo":"123456789","hbOneNo":"HB123456","disCode":"HM","outletCode":"001","appDt":"20250320","appTm":"143000","operIp":"***********","txCode":"HIGH_FUND_QUERY_ACK_VOL_AND_AMT_INFO","txChannel":"2"}
     * @apiSuccess (响应结果) {Array} fundAckVolAndAmtInfoList 基金确认金额与确认份额信息列表
     * @apiSuccess (响应结果) {String} fundAckVolAndAmtInfoList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} fundAckVolAndAmtInfoList.mBusiCode 业务类型
     * @apiSuccess (响应结果) {String} fundAckVolAndAmtInfoList.mBusiCodeStr 业务类型中文
     * @apiSuccess (响应结果) {Number} fundAckVolAndAmtInfoList.ackAmt 确认金额
     * @apiSuccess (响应结果) {Number} fundAckVolAndAmtInfoList.ackVol 确认份额
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"fundAckVolAndAmtInfoList":[{"fundCode":"HM001","mBusiCode":"SUBS","mBusiCodeStr":"认购","ackAmt":"100000.00","ackVol":"100000.00"},{"fundCode":"HM002","mBusiCode":"PURCHASE","mBusiCodeStr":"申购","ackAmt":"50000.00","ackVol":"50000.00"}],"returnCode":"0000","description":"成功"}
     */
    @Override
    public QueryFundAckVolAndAmtInfoResponse execute(QueryFundAckVolAndAmtInfoRequest request) {
        logger.info("查询基金确认金额与确认份额开始，请求参数：{}", JSON.toJSONString(request));
        QueryFundAckVolAndAmtInfoResponse response = new QueryFundAckVolAndAmtInfoResponse();
        try {
            // 1.参数校验,产品不是新方程的,直接报错
            List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(request.getFundCodeList());
            if(CollectionUtils.isEmpty(highProductBaseInfoList)){
                response.setReturnCode(ExceptionCodes.SUCCESS);
                return response;
            }
            String xinFangeChengFundManCode = commonValueConfig.getXinFangeChengFundManCode();
            for (HighProductBaseInfoBean highProductBaseInfoBean : highProductBaseInfoList) {
                if (StringUtils.isNotBlank(xinFangeChengFundManCode) && !xinFangeChengFundManCode.equals(highProductBaseInfoBean.getFundManCode())) {
                    response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
                    response.setDescription("基金:" + highProductBaseInfoBean.getFundCode() + "不是新方程的,请勿查询!");
                    return response;
                }
            }
            // 2.构建确认状态列表
            List<String> txAckFlagList = Arrays.asList(
                    TxAckFlagEnum.CONFIRM_PART.getCode(),
                    TxAckFlagEnum.CONFIRM_SUCCESS.getCode()
            );

            // 3.查询基金确认金额与确认份额
            List<FundAckVolAndAmtVo> fundAckVolAndAmtVoList = highDealOrderDtlRepository.selectFundAckVolAndAmtInfo(request.getFundCodeList(), request.getAckStartDt(), request.getAckEndDt(), txAckFlagList,request.getMBusiCodeList());

            // 4.转换为DTO并设置业务类型中文
            List<FundAckVolAndAmtInfoDto> fundAckVolAndAmtInfoDtoList = fundAckVolAndAmtVoList.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            response.setFundAckVolAndAmtInfoList(fundAckVolAndAmtInfoDtoList);
            logger.info("查询基金确认金额与确认份额成功，返回{}条记录", fundAckVolAndAmtInfoDtoList.size());
            response.setReturnCode(ExceptionCodes.SUCCESS);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
            return response;

        } catch (Exception e) {
            logger.error("查询基金确认金额与确认份额异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * @param vo FundAckVolAndAmtVo对象
     * @return FundAckVolAndAmtInfoDto DTO对象
     * @description: 转换VO为DTO
     * @author: hongdong.xie
     * @date: 2025/3/20 14:30
     * @since JDK 1.8
     */
    private FundAckVolAndAmtInfoDto convertToDto(FundAckVolAndAmtVo vo) {
        FundAckVolAndAmtInfoDto dto = new FundAckVolAndAmtInfoDto();
        dto.setFundCode(vo.getFundCode());
        dto.setMBusiCode(vo.getMBusiCode());
        dto.setAckAmt(vo.getAckAmt());
        dto.setAckVol(vo.getAckVol());
        // 转换业务类型中文
        BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(vo.getMBusiCode());
        if (businessCodeEnum != null) {
            dto.setMBusiCodeStr(businessCodeEnum.getDescription());
        } else {
            dto.setMBusiCodeStr(vo.getMBusiCode());
        }

        return dto;
    }
}