<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.tms</groupId>
		<artifactId>high-order-center</artifactId>
		<version>4.9.24-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>high-order-center-service</artifactId>
	<packaging>jar</packaging>
	<name>high-order-center-service</name>


	<dependencies>

		<!-- demo-center相关 -->
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-dao</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-service</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>howbuy-message-mq</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-message-service</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jcl-over-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>dubbo</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-cachemanagement-1</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-cachemanagement</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-lang</artifactId>
		</dependency>


		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-outerservice</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>howbuy-cachemanagement</artifactId>
					<groupId>com.howbuy</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jcl-over-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jul-to-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>dubbo</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>mybatis-spring</artifactId>
					<groupId>org.mybatis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-validator</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j-slf4j-impl</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-message-service</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuyUtil</artifactId>
			<version>1.0.0</version>
			<scope>test</scope>
		</dependency>
		
		<dependency>
			<groupId>net.sourceforge.pinyin4j</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.0</version>
		</dependency>

		<dependency>
			<groupId>org.softamis</groupId>
			<artifactId>cluster4spring</artifactId>
			<version>0.85</version>
		</dependency>
		<!-- trade -->


		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-log-pattern</artifactId>
		</dependency>

		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.jacoco</groupId>
			<artifactId>org.jacoco.agent</artifactId>
			<version>0.8.5</version>
			<classifier>runtime</classifier>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument</artifactId>
		</dependency>
<!--spring-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-boot-starter-logging</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-configuration</artifactId>
					<groupId>commons-configuration</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
				<exclusion>
					<artifactId>nacos-client</artifactId>
					<groupId>com.alibaba.nacos</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-configuration</artifactId>
					<groupId>commons-configuration</groupId>
				</exclusion>
				<exclusion>
					<artifactId>archaius-core</artifactId>
					<groupId>com.netflix.archaius</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-dependencies-zookeeper</artifactId>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>asm</artifactId>
					<groupId>org.ow2.asm</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>jul-to-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>javassist</artifactId>
					<groupId>org.javassist</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<!-- Dubbo Hessian 协议支持 -->
		<dependency>
			<groupId>org.apache.dubbo.extensions</groupId>
			<artifactId>dubbo-rpc-hessian</artifactId>
		</dependency>
		<dependency>
			<groupId>com.caucho</groupId>
			<artifactId>hessian</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.lmax</groupId>
			<artifactId>disruptor</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>${javax.servlet.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>jexcelapi</groupId>
			<artifactId>jxl</artifactId>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
		</dependency>

		<!-- unit test -->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.howbuy.pa.framework.mq</groupId>
			<artifactId>howbuy-mq-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-amq</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-message-rocket</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-ccms-watcher</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>notifyUtil</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-dynamicds</artifactId>
			<version>${com.howbuy.tms-common-dynamicds.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-cache</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>dubbo</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- trace -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-trace</artifactId>
		</dependency>

	</dependencies>

	<build>
		<finalName>high-order-center</finalName>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.7</version>
				<executions>
					<execution>
						<id>default-instrument</id>
						<goals>
							<goal>instrument</goal>
						</goals>
					</execution>
					<execution>
						<id>default-restore-instrumented-classes</id>
						<goals>
							<goal>restore-instrumented-classes</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<systemPropertyVariables>
						<jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<reporting>
		<plugins>
			<plugin>
				<!-- mvn jacoco:report-->
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<reportSets>
					<reportSet>
						<reports>
							<report>report</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>
		</plugins>
	</reporting>

</project>