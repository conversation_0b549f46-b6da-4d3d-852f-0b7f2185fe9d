<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.otc</groupId>
		<artifactId>otc-center-search</artifactId>
		<version>7.9.3-RELEASE</version>
	</parent>
	<artifactId>otc-center-search-client</artifactId>
	<dependencies>
		<dependency>
			<groupId>com.howbuy.otc.common</groupId>
			<artifactId>otc-commons-client</artifactId>
			<version>${com.howbuy.otc-commons.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-common-api</artifactId>
			<version>${com.howbuy.otc-common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-common-entity</artifactId>
			<version>${com.howbuy.otc-common.version}</version>
		</dependency>
	</dependencies>
	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
</project>