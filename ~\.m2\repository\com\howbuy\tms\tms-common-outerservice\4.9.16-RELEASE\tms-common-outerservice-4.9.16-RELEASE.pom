<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

	<groupId>com.howbuy.tms</groupId>
	<artifactId>tms-common-outerservice</artifactId>
	<version>4.9.16-RELEASE</version>
	<packaging>jar</packaging>

    <name>tms-common-outerservice</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <spring.version>3.2.16.RELEASE</spring.version>
        <log4j.version>2.17.0</log4j.version>
        <ccms.version>1.0-SNAPSHOT</ccms.version>
        <spring.version>3.2.16.RELEASE</spring.version>
        <fastjson.version>1.2.17</fastjson.version>
        <dubbo.version>2.5.3</dubbo.version>
        <zkclient.version>0.1</zkclient.version>
        <cache.version>1.2.1-RELEASE</cache.version>
        <log4j.version>2.17.0</log4j.version>
        <zookeeper.version>3.4.6</zookeeper.version>
        <cluster4spring.version>0.85</cluster4spring.version>

		<payonline.version>RELEASE</payonline.version>
		<acccenter.version>RELEASE</acccenter.version>
		<ftxconsole.version>RELEASE</ftxconsole.version>
		<interlayer.version>1.0.0-release</interlayer.version>
		<finonline.version>2.2.5-RELEASE</finonline.version>
		<es.web.console.version>RELEASE</es.web.console.version>
		<lct.online.verdion>RELEASE</lct.online.verdion>
		<robot.order.center.version>3.5.53-RELEASE</robot.order.center.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
		<com.howbuy.tms-common-service.version>4.9.16-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
		<com.howbuy.pay-online-facade.version>20250729-RELEASE</com.howbuy.pay-online-facade.version>
        <com.howbuy.ftx-order-facade.version>2.1.1-RELEASE</com.howbuy.ftx-order-facade.version>
        <com.howbuy.ftx-batch-facade.version>2.1.1-RELEASE</com.howbuy.ftx-batch-facade.version>
        <com.howbuy.fbs-online-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-facade.version>
		<com.howbuy.fbs-online-search-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-search-facade.version>
		<com.howbuy.pdc-online-facade.version>3.30.0-RELEASE</com.howbuy.pdc-online-facade.version>
		<com.howbuy.acc-center-facade.version>20250702-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.acc-center-common.version>20250702-RELEASE</com.howbuy.acc-center-common.version>
		<com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
		<com.howbuy.pay-common-model.version>2.5.8-RELEASE</com.howbuy.pay-common-model.version>
		<com.howbuy.ftx-console-facade.version>1.1.32-RELEASE</com.howbuy.ftx-console-facade.version>
		<com.howbuy.fin-online-facade.version>20250724-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.elasticsearch-center-client.version>4.8.25-RELEASE</com.howbuy.elasticsearch-center-client.version>
		<com.howbuy.howbuy-fund-client.version>release-20250415-xcxeq-RELEASE</com.howbuy.howbuy-fund-client.version>
		<com.howbuy.center-client.version>6.4.10-RELEASE</com.howbuy.center-client.version>
		<com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
		<com.howbuy.crm-core-client.version>bugfix-20250807-RELEASE</com.howbuy.crm-core-client.version>
		<com.howbuy.crm-nt-client.version>1.9.6.5-RELEASE</com.howbuy.crm-nt-client.version>
		<com.howbuy.message-public-client.version>5.1.16-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.es-web-facade.version>3.5.0-RELEASE</com.howbuy.es-web-facade.version>
		<com.howbuy.otc-center-search-client.version>7.9.3-RELEASE</com.howbuy.otc-center-search-client.version>
        <com.howbuy.elasticsearch-center.version>4.8.25-RELEASE</com.howbuy.elasticsearch-center.version>
		<com.howbuy.howbuy-simu-client.version>release-20250717-oldtable-offline-RELEASE</com.howbuy.howbuy-simu-client.version>
		<com.howbuy.howbuy-simu-client.version>release-20250717-oldtable-offline-RELEASE</com.howbuy.howbuy-simu-client.version>
        <com.howbuy.product-center.version>4.9.24-RELEASE</com.howbuy.product-center.version>
		<com.howbuy.product-center-client.version>4.9.24-RELEASE</com.howbuy.product-center-client.version>
		<com.howbuy.product-center-model.version>4.9.24-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.param-server-facade.version>3.41.0-RELEASE</com.howbuy.param-server-facade.version>
		<com.howbuy.howbuy-cms-client.version>release-20250805-khhx-RELEASE</com.howbuy.howbuy-cms-client.version>
		<com.howbuy.high-order-center-client.version>4.8.88-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.howbuy-trace.version>1.0.5-RELEASE</com.howbuy.howbuy-trace.version>
		<com.howbuy.dtms-order-client.version>2.0.6.0-RELEASE</com.howbuy.dtms-order-client.version>
        <com.howbuy.asset-client.version> 3.16.21-RELEASE</com.howbuy.asset-client.version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>asset-client</artifactId>
            <version>${com.howbuy.asset-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.dtms</groupId>
            <artifactId>dtms-order-client</artifactId>
            <version>${com.howbuy.dtms-order-client.version}</version>
        </dependency>
        <!-- Spring相关 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
            <version>${zkclient.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${zookeeper.version}</version>
        </dependency>
        <dependency>
            <groupId>org.softamis</groupId>
            <artifactId>cluster4spring</artifactId>
            <version>${cluster4spring.version}</version>
        </dependency>
        <!-- log4j2 -->

        <!--核心log4j2jar包 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>${log4j.version}</version>
        </dependency>

        <!--用于与slf4j保持桥接 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.2</version>
        </dependency>

        <!-- ccms -->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-ccms-independent</artifactId>
            <version>1.0.0-release</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cms-client</artifactId>
            <version>${com.howbuy.howbuy-cms-client.version}</version>
        </dependency>

        <dependency>
            <groupId>net.unicon.springframework</groupId>
            <artifactId>springframework-addons</artifactId>
            <version>${ccms.version}</version>
        </dependency>
        <!-- ccms -->

        <!-- log4j2 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>

        <!-- HOWBUY -->
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>tms-common-service</artifactId>
            <version>${com.howbuy.tms-common-service.version}</version>
        </dependency>
        <!-- 外部系统包 -->
        <!-- TP依赖包 -->
        <dependency>
            <groupId>com.howbuy.common</groupId>
            <artifactId>common-facade</artifactId>
            <version>${com.howbuy.common-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.payonline</groupId>
            <artifactId>pay-online-facade</artifactId>
            <version>${com.howbuy.pay-online-facade.version}</version>
        </dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>ftx-order-facade</artifactId>
			<version>${com.howbuy.ftx-order-facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>ftx-batch-facade</artifactId>
			<version>${com.howbuy.ftx-batch-facade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.howbuy.interlayer</groupId>
                    <artifactId>product-center-model</artifactId>
                </exclusion>
            </exclusions>
		</dependency>


        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>param-server-facade</artifactId>
            <version>${com.howbuy.param-server-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-auth-facade</artifactId>
            <version>${com.howbuy.howbuy-auth-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.fbs</groupId>
            <artifactId>fbs-online-facade</artifactId>
            <version>${com.howbuy.fbs-online-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.fbs</groupId>
            <artifactId>fbs-online-search-facade</artifactId>
            <version>${com.howbuy.fbs-online-search-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pdc</groupId>
            <artifactId>pdc-online-facade</artifactId>
            <version>${com.howbuy.pdc-online-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.acccenter</groupId>
            <artifactId>acc-center-facade</artifactId>
            <version>${com.howbuy.acc-center-facade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy.acccenter</groupId>
            <artifactId>acc-center-common</artifactId>
            <version>${com.howbuy.acc-center-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.acc</groupId>
            <artifactId>acc-common-utils</artifactId>
            <version>${com.howbuy.acc-common-utils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>pay-common-model</artifactId>
            <version>${com.howbuy.pay-common-model.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>ftx-console-facade</artifactId>
            <version>${com.howbuy.ftx-console-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.finonline</groupId>
            <artifactId>fin-online-facade</artifactId>
            <version>${com.howbuy.fin-online-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.interlayer</groupId>
            <artifactId>product-center-client</artifactId>
            <version>${com.howbuy.product-center-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.interlayer</groupId>
            <artifactId>product-center-model</artifactId>
            <version>${com.howbuy.product-center-model.version}</version>
        </dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>elasticsearch-center-client</artifactId>
			<version>${com.howbuy.elasticsearch-center-client.version}</version>
		</dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-fund-client</artifactId>
            <version>${com.howbuy.howbuy-fund-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.cc</groupId>
            <artifactId>center-client</artifactId>
            <version>${com.howbuy.center-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>HowbuyServiceBus</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>HowbuyServiceCommon</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-simu-client</artifactId>
            <version>${com.howbuy.howbuy-simu-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.lct</groupId>
            <artifactId>lct-online-facade-hd</artifactId>
            <version>${lct.online.verdion}</version>
        </dependency>

        <!-- cc 依赖包 -->
        <dependency>
            <groupId>com.howbuy.cc</groupId>
            <artifactId>hbone-facade</artifactId>
            <version>2.5.0</version>
        </dependency>
        <!-- CRM 依赖包 -->
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-td-client</artifactId>
            <version>${com.howbuy.crm-td-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-core-client</artifactId>
            <version>${com.howbuy.crm-core-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-nt-client</artifactId>
            <version>${com.howbuy.crm-nt-client.version}</version>
        </dependency>
        <!-- cc message -->
        <dependency>
            <groupId>com.howbuy.cc.message</groupId>
            <artifactId>message-public-client</artifactId>
            <version>${com.howbuy.message-public-client.version}</version>
        </dependency>

		<!-- es 电子签名依赖-->
		<dependency>
			<groupId>com.howbuy.es</groupId>
			<artifactId>es-web-facade</artifactId>
			<version>${com.howbuy.es-web-facade.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.otc</groupId>
			<artifactId>otc-center-search-client</artifactId>
			<version>${com.howbuy.otc-center-search-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>high-order-center-client</artifactId>
			<version>${com.howbuy.high-order-center-client.version}</version>
		</dependency>
		<!-- trace -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-trace</artifactId>
			<version>${com.howbuy.howbuy-trace.version}</version>
		</dependency>
	</dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- Java源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-source</id>
                        <phase>install</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>