/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.service.cacheservice.alarm.HighBalanceAlarmService;
import com.howbuy.tms.high.orders.service.common.utils.OpsSysMonitor;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:持仓相关参数检查告警
 *
 * <AUTHOR>
 * @reason:
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class CheckBalanceAlarmTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(CheckBalanceAlarmTask.class);
    private static Logger monitorLogger = LogManager.getLogger("monitorlog");

    private HighBalanceAlarmService highBalanceAlarmService;

    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    private CmCusttradeDirectRepository cmCusttradeDirectRepository;

    private BalanceBean balanceBean;
    private String txAcctNo;
    private String hbOneNo;

    private CountDownLatch latch;

    private String dataSourceKey;

    private List<String> crisisFundList;

    private String noAlarmFundCodes;

    public CheckBalanceAlarmTask(BalanceBean balanceBean, HighBalanceAlarmService highBalanceAlarmService,
                                 HighDealOrderDtlRepository highDealOrderDtlRepository,
                                 String txAcctNo, String hbOneNo, CountDownLatch latch, String dataSourceKey, List<String> crisisFundList,
                                 CmCusttradeDirectRepository cmCusttradeDirectRepository, String noAlarmFundCodes) {
        this.balanceBean = balanceBean;
        this.highBalanceAlarmService = highBalanceAlarmService;
        this.highDealOrderDtlRepository = highDealOrderDtlRepository;
        this.txAcctNo = txAcctNo;
        this.hbOneNo = hbOneNo;
        this.latch = latch;
        this.dataSourceKey = dataSourceKey;
        this.crisisFundList = crisisFundList;
        this.cmCusttradeDirectRepository = cmCusttradeDirectRepository;
        this.noAlarmFundCodes = noAlarmFundCodes;
    }

    @Override
    public RuntimeException call() throws Exception {

        try {
            if (CollectionUtils.isNotEmpty(crisisFundList) && crisisFundList.contains(balanceBean.getProductCode())) {
                return null;
            }
            if (StringUtils.isNotEmpty(noAlarmFundCodes) && noAlarmFundCodes.contains(balanceBean.getProductCode())) {
                return null;
            }
            // 分期标识
            String getStageEstablishFlag = balanceBean.getStageEstablishFlag();
            // 产品相关告警
            productCodeAlarm(balanceBean, getStageEstablishFlag);
            // 客户相关告警
            custNoAlarm(txAcctNo, hbOneNo, balanceBean, getStageEstablishFlag);
        } catch (RuntimeException ex) {
            logger.error("CheckBalanceAlarmTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

    private Map<String, String> getMsgMap(HighBalanceAlarmEnum alarmEnum, String txAcctNo, String productCode) {
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("time", DateUtils.formatToString(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS_SSS));
        msgMap.put("msg", alarmEnum.getName());
        msgMap.put("fundCode", productCode);
        msgMap.put("txAcctNo", txAcctNo);
        msgMap.put("type", alarmEnum.getType());
        return msgMap;
    }

    private void productCodeAlarm(BalanceBean balanceBean, String getStageEstablishFlag) {
        if (!ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType()) && !"PE0035".equalsIgnoreCase(balanceBean.getProductCode())) {
            String productCode = YesOrNoEnum.YES.getCode().equals(getStageEstablishFlag) ? balanceBean.getSubProductCode() : balanceBean.getProductCode();
            // 【最新净值】为空
            if (balanceBean.getNav() == null) {
                if (highBalanceAlarmService.putProductAlarmCache(productCode, HighBalanceAlarmEnum.PRODUCT_3.getCode())) {
                    logger.warn("产品{}的净值为空", productCode);
                    String msg = String.format("产品%s的净值为空", productCode);
                    OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                }
                return;
            }
            // 【最新净值日期】为空
            if (StringUtils.isEmpty(balanceBean.getNavDt())) {
                if (highBalanceAlarmService.putProductAlarmCache(productCode, HighBalanceAlarmEnum.PRODUCT_4.getCode())) {
                    logger.warn("产品{}的净值日期为空", productCode);
                    String msg = String.format("产品%s的净值日期为空", productCode);
                    OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                }
                return;
            }
            // 【最新净值】=0 或者小于0
            if (balanceBean.getNav().compareTo(BigDecimal.ZERO) == 0) {
                if (highBalanceAlarmService.putProductAlarmCache(productCode, HighBalanceAlarmEnum.PRODUCT_1.getCode())) {
                    logger.warn("产品{}的净值为0", productCode);
                    String msg = String.format("产品%s的净值为0", productCode);
                    OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                }
            } else if (balanceBean.getNav().compareTo(BigDecimal.ZERO) < 0) {
                if (highBalanceAlarmService.putProductAlarmCache(productCode, HighBalanceAlarmEnum.PRODUCT_2.getCode())) {
                    logger.warn("产品{}的净值小于0", productCode);
                    String msg = String.format("产品%s的净值小于0", productCode);
                    OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                }
            }
        }
    }

    private void custNoAlarm(String txAcctNo, String hbOneNo, BalanceBean balanceBean, String getStageEstablishFlag) {
        // 【最新净值日期】＞第一笔交易【确认日期】
        firstAlarm(txAcctNo, hbOneNo, balanceBean, getStageEstablishFlag);

        // 【最新净值日期】比当前持仓份额中最近一笔份额的【确认时间】早3个月以上
        lasterAlarm(txAcctNo, hbOneNo, balanceBean, getStageEstablishFlag);

        // 其他
        otherAlarm(balanceBean, getStageEstablishFlag);
    }


    public static boolean daysBetweenMore90(String navDt, String ackDt) {
        try {
            if (StringUtils.isEmpty(navDt) || StringUtils.isEmpty(ackDt)) {
                return false;
            }

            if (navDt.compareTo(ackDt) == 0) {
                return false;
            } else if (navDt.compareTo(ackDt) > 0) {
                return false;
            } else {
                Date smdate = DateUtils.formatToDate(navDt, DateUtils.YYYYMMDD);
                Date bdate = DateUtils.formatToDate(ackDt, DateUtils.YYYYMMDD);
                int num = DateUtils.daysBetween(smdate, bdate);
                if (num > 90) {
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("异常", e);
        }
        return false;
    }

    private void lasterAlarm(String txAcctNo, String hbOneNo, BalanceBean balanceBean, String getStageEstablishFlag) {
        // 特殊产品指标控制需求 20221125
        // 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上(即【首笔交易记录的确认日期】-【DB最新净值日期】＞90天 )
        // 阳光私募：ProductSubType != 2 && ProductSubType != 5
        // 非货基型券商集合：【产品分类(cpfl)】="1 固定收益" 且【固收类型(gslx)】 =“3 券商集合” 且【净值披露方式】= “1-净值”
        // 【产品分类(cpfl)】=1  等价于  【好买产品线(hmcpx)】=2
        if ((!ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                && !ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType()))
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag())
                && NavDisclosureTypeEnum.JZ.getCode().equals(balanceBean.getNavDisclosureType()))) {
            if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
                String fundCode = balanceBean.getProductCode();
                if (YesOrNoEnum.YES.getCode().equals(getStageEstablishFlag)) {
                    fundCode = balanceBean.getSubProductCode();
                }
                CmCusttradeDirectPo cmCusttradeDirectPo = cmCusttradeDirectRepository.getLastAckInfoForDirect(hbOneNo, fundCode);
                if (cmCusttradeDirectPo != null) {
                    if (balanceBean.getNavDt() != null && daysBetweenMore90(balanceBean.getNavDt(), cmCusttradeDirectPo.getTradedt())) {
                        if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, fundCode, HighBalanceAlarmEnum.CUST_2.getCode())) {
                            logger.warn("一账通号{}，产品{}的最新净值日期小于最新交易的确认日期3个月以上", hbOneNo, fundCode);
                            String msg = String.format("一账通号%s，产品%s的最新净值日期小于最新交易的确认日期3个月以上", hbOneNo, fundCode);
                            OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                        }
                    }
                }
            } else {
                HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.getLastAckInfoForConsignment(txAcctNo, balanceBean.getProductCode());
                if (highDealOrderDtlPo != null) {
                    if (balanceBean.getNavDt() != null && daysBetweenMore90(balanceBean.getNavDt(), highDealOrderDtlPo.getAckDt())) {
                        if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_2.getCode())) {
                            logger.warn("一账通号{}，产品{}的最新净值日期小于最新交易的确认日期3个月以上", hbOneNo, balanceBean.getProductCode());
                            String msg = String.format("一账通号%s，产品%s的最新净值日期小于最新交易的确认日期3个月以上", hbOneNo, balanceBean.getProductCode());
                            OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
                        }
                    }
                }
            }
        }
    }

    private void firstAlarm(String txAcctNo, String hbOneNo, BalanceBean balanceBean, String getStageEstablishFlag) {
        // 特殊产品指标控制需求 20221125
        // 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上(即【首笔交易记录的确认日期】-【DB最新净值日期】＞90天 )
        // 阳光私募：ProductSubType != 2 && ProductSubType != 5
        // 非货基型券商集合：【产品分类(cpfl)】="1 固定收益" 且【固收类型(gslx)】 =“3 券商集合” 且【净值披露方式】= “1-净值”
        // 【产品分类(cpfl)】=1  等价于  【好买产品线(hmcpx)】=2
        if ((!ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                && !ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType()))
                || (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(balanceBean.getStandardFixedIncomeFlag())
                && NavDisclosureTypeEnum.JZ.getCode().equals(balanceBean.getNavDisclosureType()))) {

            if (ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
                String fundCode = balanceBean.getProductCode();
                if (YesOrNoEnum.YES.getCode().equals(getStageEstablishFlag)) {
                    fundCode = balanceBean.getSubProductCode();
                }
                CmCusttradeDirectPo cmCusttradeDirectPo = cmCusttradeDirectRepository.getFirstAckInfoForDirect(hbOneNo, fundCode);
                if (cmCusttradeDirectPo != null) {
                    if (balanceBean.getNavDt() != null && daysBetweenMore90(balanceBean.getNavDt(), cmCusttradeDirectPo.getTradedt())) {
                        // 更新异常标识 20221125
                        balanceBean.setAbnormalFlag("1");
                        if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, fundCode, HighBalanceAlarmEnum.CUST_1.getCode())) {
                            logger.warn("一账通号{}，产品{}的最新净值日期小于第一笔交易的确认日期3个月以上", hbOneNo, fundCode);
                            monitorLogger.info(JSON.toJSONString(getMsgMap(HighBalanceAlarmEnum.CUST_1, hbOneNo, fundCode)));
                        }
                    }
                }
            } else {
                HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.getFirstAckInfoForConsignment(txAcctNo, balanceBean.getProductCode());
                if (highDealOrderDtlPo != null) {
                    if (balanceBean.getNavDt() != null && daysBetweenMore90(balanceBean.getNavDt(), highDealOrderDtlPo.getAckDt())) {
                        // 更新异常标识 20221125
                        balanceBean.setAbnormalFlag("1");
                        if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_1.getCode())) {
                            logger.warn("一账通号{}，产品{}的最新净值日期小于第一笔交易的确认日期3个月以上", hbOneNo, balanceBean.getProductCode());
                            monitorLogger.info(JSON.toJSONString(getMsgMap(HighBalanceAlarmEnum.CUST_1, hbOneNo, balanceBean.getProductCode())));
                        }
                    }
                }
            }
        }
    }

    private void otherAlarm(BalanceBean balanceBean, String getStageEstablishFlag) {
        String productCode = YesOrNoEnum.YES.getCode().equals(getStageEstablishFlag) ? balanceBean.getSubProductCode() : balanceBean.getProductCode();
        // 以下任一指标数值＜0，则 市值/已发生回款(私募股权&股权固收&)/累计应收管理费/累计应收业绩报酬
        if (balanceBean.getMarketValue() != null && balanceBean.getMarketValue().compareTo(BigDecimal.ZERO) < 0) {
            if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_3.getCode())) {
                logger.warn("一账通客户号{}，产品{}的市值小于0", hbOneNo, productCode);
                String msg = String.format("一账通客户号%s，产品%s的市值小于0", hbOneNo, productCode);
                OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
            }
        }
        if (balanceBean.getCashCollection() != null && balanceBean.getCashCollection().compareTo(BigDecimal.ZERO) < 0) {
            if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_4.getCode())) {
                logger.warn("一账通客户号{}，产品{}的已发生回款小于0", hbOneNo, productCode);
                String msg = String.format("一账通客户号%s，产品%s的已发生回款小于0", hbOneNo, productCode);
                OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
            }
        }
        if (balanceBean.getReceivManageFee() != null && balanceBean.getReceivManageFee().compareTo(BigDecimal.ZERO) < 0) {
            if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_5.getCode())) {
                logger.warn("一账通客户号{}，产品{}的累计应收管理费小于0", hbOneNo, productCode);
                String msg = String.format("一账通客户号%s，产品%s的累计应收管理费小于0", hbOneNo, productCode);
                OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
            }
        }
        if (balanceBean.getReceivPreformFee() != null && balanceBean.getReceivPreformFee().compareTo(BigDecimal.ZERO) < 0) {
            if (highBalanceAlarmService.putCustAlarmCache(hbOneNo, balanceBean.getProductCode(), HighBalanceAlarmEnum.CUST_6.getCode())) {
                logger.warn("一账通号{}，产品{}的累计应收业绩报酬小于0", hbOneNo, productCode);
                String msg = String.format("一账通号%s，产品%s的累计应收业绩报酬小于0", hbOneNo, productCode);
                OpsSysMonitor.businessWarn(msg, OpsSysMonitor.WARN);
            }
        }
    }

}

