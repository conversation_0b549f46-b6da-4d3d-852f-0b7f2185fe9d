package com.howbuy.tms.high.orders.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew.QueryBalanceContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;

/**
 * @Description:查询海外持仓
 * @Author: yun.lu
 * Date: 2025/8/1 18:40
 */
public class QueryHkBalanceTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryHkBalanceTask.class);
    private QueryAcctBalanceRequest request;
    private Boolean hkProduct;
    private QueryBalanceContext queryBalanceContext;
    private QueryBalanceFacade queryBalanceFacade;
    /**
     * HK成功的返回编码
     */
    private static final String HK_SUCCESS_CODE = "0000";
    private static final String RMB = "156";
    private static final String HZ_NUM = "5";

    @Override
    protected void callTask() {
        if (StringUtils.isNotBlank(request.getNotFilterHkFund()) && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
            log.info("queryHkBalance-没有香港权限,不查询香港持仓");
            queryBalanceContext.setHkBalance(new QueryBalanceResponse());
            return;
        }
        if (hkProduct != null && !hkProduct) {
            log.info("queryHkBalance-不是香港产品,不查询香港持仓");
            queryBalanceContext.setHkBalance(new QueryBalanceResponse());
            return;
        }
        if (StringUtils.isBlank(request.getHbOneNo())) {
            log.info("queryHkBalance-没有一账通号,不查询海外持仓接口");
            queryBalanceContext.setHkBalance(new QueryBalanceResponse());
            return;
        }
        QueryBalanceRequest queryBalanceRequest = new QueryBalanceRequest();
        queryBalanceRequest.setHbOneNo(request.getHbOneNo());
        queryBalanceRequest.setFundCode(request.getProductCode());
        queryBalanceRequest.setCurrencyDisType(RMB);
        // 兼容老逻辑,如果传5,说明查询好臻的
        if (HZ_NUM.equals(request.getProductSubType()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            queryBalanceRequest.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        } else {
            if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
                queryBalanceRequest.setDisCodeList(request.getDisCodeList());
            } else if (StringUtils.isNotBlank(request.getDisCode())) {
                queryBalanceRequest.setDisCodeList(Collections.singletonList(request.getDisCode()));
            } else {
                queryBalanceRequest.setDisCodeList(request.getDisCodeList());
            }
        }
        queryBalanceRequest.setBalanceStatus(request.getBalanceStatus());
        log.info("queryHkBalance-查询海外持仓接口-start,queryBalanceRequest={}", JSON.toJSONString(queryBalanceRequest));
        Response<QueryBalanceResponse> result = queryBalanceFacade.execute(queryBalanceRequest);
        log.info("queryHkBalance-查询海外持仓接口-结果:hkBalanceResponse={}", JSON.toJSONString(result));
        if (!HK_SUCCESS_CODE.equals(result.getCode()) || result.getData() == null) {
            log.info("queryHkBalance-海外持仓接口,返回非成功状态,当做没有海外持仓处理");
            queryBalanceContext.setHkBalance(new QueryBalanceResponse());
            return;
        }
        queryBalanceContext.setHkBalance(result.getData());
    }

    public QueryHkBalanceTask(QueryAcctBalanceRequest request, Boolean hkProduct, QueryBalanceContext queryBalanceContext, QueryBalanceFacade queryBalanceFacade) {
        this.request = request;
        this.hkProduct = hkProduct;
        this.queryBalanceContext = queryBalanceContext;
        this.queryBalanceFacade = queryBalanceFacade;
    }

    public QueryAcctBalanceRequest getRequest() {
        return request;
    }

    public void setRequest(QueryAcctBalanceRequest request) {
        this.request = request;
    }

    public Boolean getHkProduct() {
        return hkProduct;
    }

    public void setHkProduct(Boolean hkProduct) {
        this.hkProduct = hkProduct;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public QueryBalanceFacade getQueryBalanceFacade() {
        return queryBalanceFacade;
    }

    public void setQueryBalanceFacade(QueryBalanceFacade queryBalanceFacade) {
        this.queryBalanceFacade = queryBalanceFacade;
    }
}
