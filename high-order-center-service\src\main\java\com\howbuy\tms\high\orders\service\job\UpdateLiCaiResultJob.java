package com.howbuy.tms.high.orders.service.job;

import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.utils.PartListUtil;
import com.howbuy.tms.high.orders.service.business.message.HighOrderMessageProcessor;
import com.howbuy.tms.high.orders.service.business.queryneedshowlicai.QueryNeedShowLiCaiServiceImpl;
import com.howbuy.tms.high.orders.service.repository.CmCustFundDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.UserShowResultInfoRepository;
import com.howbuy.tms.high.orders.service.task.HowBuyRunTaskUil;
import com.howbuy.tms.high.orders.service.task.HowbuyBaseTask;
import com.howbuy.tms.high.orders.service.task.UpdateLiCaiResultTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:更新理财分析结果任务
 * @Author: yun.lu
 * Date: 2024/12/9 17:45
 */
@Service("updateLiCaiResultJob")
@Slf4j
public class UpdateLiCaiResultJob extends HighOrderMessageProcessor {
    @Autowired
    private QueryNeedShowLiCaiServiceImpl queryNeedShowLiCaiService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private CmCustFundDirectRepository cmCustFundDirectRepository;
    @Autowired
    private UserShowResultInfoRepository userShowResultInfoRepository;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    /**
     * 更新理财分析结果任务
     */
    @Value("${update.liCai.result.queue}")
    private String updateLiCaiResultQueue;

    @Override
    protected String getQuartMessageChannel() {
        return updateLiCaiResultQueue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        setUnDirectBalance();
        setDirectBalance();
    }

    private void setUnDirectBalance() {
        log.info("更新代销持仓的理财分析信息");
        // 1.先分页查询有持仓的用户
        int pageSize = 500;
        int pageNo = 1;
        int num = 1;
        // 防止死循环,设置一个最大值
        while (num < 1000) {
            log.info("更新代销持仓的理财分析信息,pageNo={},pageSize={}", pageNo, pageSize);
            // 查询所有数据源分页数据
            List<String> txAcctNoList = custBooksRepository.queryBalanceTxAcctNoByPage(pageNo, pageSize);
            if (CollectionUtils.isEmpty(txAcctNoList)) {
                log.info("更新代销持仓的理财分析信息,分页查询数据结束,pageNo={},pageSize={}", pageNo, pageSize);
                return;
            }
            num++;
            pageNo++;
            List<List<String>> list = PartListUtil.partition(txAcctNoList, 20);
            List<HowbuyBaseTask> taskList = new ArrayList<>();
            for (List<String> subList : list) {
                UpdateLiCaiResultTask updateLiCaiResultTask = new UpdateLiCaiResultTask();
                updateLiCaiResultTask.setQueryNeedShowLiCaiService(queryNeedShowLiCaiService);
                updateLiCaiResultTask.setUserShowResultInfoRepository(userShowResultInfoRepository);
                updateLiCaiResultTask.setHbOneNoList(null);
                updateLiCaiResultTask.setTxAcctNoList(subList);
                taskList.add(updateLiCaiResultTask);
            }
            howBuyRunTaskUil.runTask(taskList);
        }
    }

    private void setDirectBalance() {
        log.info("更新直销持仓的理财分析信息");
        // 1.先分页查询有持仓的用户
        int pageSize = 500;
        int pageNo = 1;
        int num = 1;
        // 防止死循环,设置一个最大值
        while (num < 1000) {
            log.info("更新直销持仓的理财分析信息,pageNo={},pageSize={}", pageNo, pageSize);
            // 查询所有数据源分页数据
            List<String> hbOneNoList = cmCustFundDirectRepository.queryBalanceHbOneNoByPage(pageNo, pageSize);
            if (CollectionUtils.isEmpty(hbOneNoList)) {
                log.info("更新直销持仓的理财分析信息,分页查询数据结束,pageNo={},pageSize={}", pageNo, pageSize);
                return;
            }
            num++;
            pageNo++;
            List<List<String>> list = PartListUtil.partition(hbOneNoList, 20);
            List<HowbuyBaseTask> taskList = new ArrayList<>();
            for (List<String> subList : list) {
                UpdateLiCaiResultTask updateLiCaiResultTask = new UpdateLiCaiResultTask();
                updateLiCaiResultTask.setQueryNeedShowLiCaiService(queryNeedShowLiCaiService);
                updateLiCaiResultTask.setUserShowResultInfoRepository(userShowResultInfoRepository);
                updateLiCaiResultTask.setHbOneNoList(subList);
                updateLiCaiResultTask.setTxAcctNoList(null);
                taskList.add(updateLiCaiResultTask);
            }
            howBuyRunTaskUil.runTask(taskList);
        }
    }
}
