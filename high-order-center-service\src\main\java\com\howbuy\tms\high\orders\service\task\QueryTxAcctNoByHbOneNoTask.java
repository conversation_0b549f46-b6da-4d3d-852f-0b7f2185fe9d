/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryTxAcctByHboneResult;
import com.howbuy.tms.common.utils.TradeParamLocalUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:(根据一帐通号查询客户信息)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月9日 上午11:34:11
 * @since JDK 1.6
 */
public class QueryTxAcctNoByHbOneNoTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryTxAcctNoByHbOneNoTask.class);

    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    private QueryTxAcctByHboneResult  queryTxAcctByHboneResult;

    private CountDownLatch latch;

    
    private BaseRequest request;

    public QueryTxAcctNoByHbOneNoTask(QueryHbOneNoOuterService queryHbOneNoOuterService, QueryTxAcctByHboneResult  queryTxAcctByHboneResult, CountDownLatch latch, BaseRequest request) {
        this.queryHbOneNoOuterService = queryHbOneNoOuterService;
        this.queryTxAcctByHboneResult = queryTxAcctByHboneResult;
        this.latch = latch;
        this.request = request;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            TradeParamLocalUtils.setAppDt(request.getAppDt());
            TradeParamLocalUtils.setAppTm(request.getAppTm());
            TradeParamLocalUtils.setDisCode(request.getDisCode());
            TradeParamLocalUtils.setOutletCode(request.getOutletCode());
            TradeParamLocalUtils.setTxChannel(request.getTxChannel());
            String hbOneNo = queryTxAcctByHboneResult.getHboneNo();
            QueryTxAcctByHboneResult bean = queryHbOneNoOuterService.queryTxAcctByHbOneNo(hbOneNo);
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, queryTxAcctByHboneResult);
            
        }catch(RuntimeException ex){
            logger.error("QueryTxAcctNoByHbOneNoTask|RuntimeException.", ex);
            return ex;
        } finally {
           
            latch.countDown();
            TradeParamLocalUtils.removeTradeParam();
        }
        return null;
    }

}

