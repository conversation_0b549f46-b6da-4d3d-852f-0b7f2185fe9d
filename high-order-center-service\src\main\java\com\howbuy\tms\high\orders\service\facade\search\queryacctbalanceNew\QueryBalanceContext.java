package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew;

import com.howbuy.dtms.order.client.domain.response.cash.QueryCashBalanceByFundTxAcctResponse;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.tms.high.orders.facade.common.BaseDto;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import lombok.Data;

/**
 * @Description:查询市场的上线文
 * @Author: yun.lu
 * Date: 2025/8/4 9:10
 */
@Data
public class QueryBalanceContext extends BaseDto {
    /**
     * 非海外持仓
     */
    private QueryAcctBalanceResponse unHkBalance=new QueryAcctBalanceResponse();
    /**
     * 海外持仓
     */
    private QueryBalanceResponse hkBalance=new QueryBalanceResponse();
    /**
     * 现金余额信息
     */
    private QueryCashBalanceByFundTxAcctResponse cashBalance=new QueryCashBalanceByFundTxAcctResponse();
}
