/**
 * Copyright (c) 2018, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.queryprebookdtl;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebookdtl.QueryPreBookDtlOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebookdtl.QueryPreBookDtlOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractBusiProcess;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:(查询预约详细信息)
 * @reason:
 * @date 2018年1月5日 下午7:46:30
 * @since JDK 1.6
 */
@DubboService
@Service("queryPreBookDtlFacade")
public class QueryPreBookDtlFacadeService extends AbstractBusiProcess implements QueryPreBookDtlFacade {

    private static final Logger logger = LoggerFactory.getLogger(QueryPreBookDtlFacadeService.class);

    @Autowired
    private QueryPreBookDtlOuterService queryPreBookDtlOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;
    @Autowired
    private QueryAccHboneInfoOuterService queryAccHboneInfoOuterService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade.execute(QueryPreBookDtlRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryPreBookDtlFacadeService
     * @apiName execute
     * @apiDescription 查询预约详细信息
     * @apiParam (请求参数) {String} preId 预约id
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * preId=i&hbOneNo=DHaQa&pageSize=5322&disCode=VAOl1hv&txChannel=4ala&appTm=yNg0h&subOutletCode=Ox&pageNo=9518&operIp=OW5wTE&txAcctNo=Jvp&appDt=49p3JfvlK&dataTrack=63nOS&txCode=1&outletCode=i
     * @apiSuccess (响应结果) {String} preId 预约单标识，CRM唯一
     * @apiSuccess (响应结果) {Number} fee 手续费
     * @apiSuccess (响应结果) {Number} feeRate 费率
     * @apiSuccess (响应结果) {String} openStartDt 开放开始日
     * @apiSuccess (响应结果) {String} openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} payEndDate 截止打款时间
     * @apiSuccess (响应结果) {String} supportAdvanceFlag 支持提前下单 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} hboneNo 一帐通号
     * @apiSuccess (响应结果) {String} custName 客户姓名
     * @apiSuccess (响应结果) {String} fundCode 产品代码
     * @apiSuccess (响应结果) {String} fundName 产品名称
     * @apiSuccess (响应结果) {String} bankCode 银行代码
     * @apiSuccess (响应结果) {String} bankAcct 银行卡号
     * @apiSuccess (响应结果) {String} bankAddr 分支行
     * @apiSuccess (响应结果) {String} accountHolder 开户人
     * @apiSuccess (响应结果) {String} bankProv 银行卡省份
     * @apiSuccess (响应结果) {String} bankCity *      银行卡城市
     * @apiSuccess (响应结果) {Number} ackAmt 预约金额
     * @apiSuccess (响应结果) {Number} sellVol 预约赎回份额
     * @apiSuccess (响应结果) {Number} discountRate 预约折扣
     * @apiSuccess (响应结果) {String} tradeType 交易类型      1-购买 2-追加 3-赎回
     * @apiSuccess (响应结果) {String} preType 预约类型      1：纸质成单；2：电子成单；3：无纸化
     * @apiSuccess (响应结果) {String} idType 证件类型
     * @apiSuccess (响应结果) {String} idNo 证件号
     * @apiSuccess (响应结果) {String} mobile 手机号
     * @apiSuccess (响应结果) {String} validity 证件是否长期有效      1-长期；      2-非长期
     * @apiSuccess (响应结果) {String} validityDt 证件有效截止日期
     * @apiSuccess (响应结果) {String} sex 性别
     * @apiSuccess (响应结果) {String} birth 出生日期
     * @apiSuccess (响应结果) {String} province 省份
     * @apiSuccess (响应结果) {String} city 城市
     * @apiSuccess (响应结果) {String} post 邮编
     * @apiSuccess (响应结果) {String} tel 电话
     * @apiSuccess (响应结果) {String} email 电子邮箱
     * @apiSuccess (响应结果) {String} vocation 职业      01-政府部门     02-教科文     03-金融     04-商贸     05-房地产     06-制造业     07-自由职业     08-其它
     * @apiSuccess (响应结果) {String} education 学历
     * @apiSuccess (响应结果) {String} incomeOfFamily 家庭收入
     * @apiSuccess (响应结果) {String} addr 地址
     * @apiSuccess (响应结果) {Number} preDueDt 截止日期
     * @apiSuccess (响应结果) {String} nature 机构客户性质      0:国企;1:民营 ;2:合资;3:其它
     * @apiSuccess (响应结果) {String} aptitude 机构客户资质      1:金融机构;2:金融机构产品;3:社会保障基金;      4:企业年金等养老基金;5:慈善基金等社会公益基金;6:合格境外机构投资者（QFII）;7:人民币合格境外机构投资者（RQFII）;0:其他
     * @apiSuccess (响应结果) {String} scopeBusiness 经营范围
     * @apiSuccess (响应结果) {String} actualController 实际控制人
     * @apiSuccess (响应结果) {String} prebookState 预约状态      1-未确认；     2-已确认；     4-已撤销
     * @apiSuccess (响应结果) {String} creDt 创建日期
     * @apiSuccess (响应结果) {String} modDt 修改日期
     * @apiSuccess (响应结果) {String} investType 客户类型
     * @apiSuccess (响应结果) {String} frName 法人姓名
     * @apiSuccess (响应结果) {String} frIdType 法人证件类型
     * @apiSuccess (响应结果) {String} frIdNo 法人证件号
     * @apiSuccess (响应结果) {String} jbrName 经办人姓名
     * @apiSuccess (响应结果) {String} jbrIdType 经办人证件类型
     * @apiSuccess (响应结果) {String} jbrIdNo 经办人证件号
     * @apiSuccess (响应结果) {String} mark mark
     * @apiSuccess (响应结果) {String} doubleNeedFlag
     * @apiSuccess (响应结果) {String} doubleHandleFlag
     * @apiSuccess (响应结果) {Number} doubleHandleDt
     * @apiSuccess (响应结果) {String} firstPreId
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"discountRate":2931.************,"modDt":"LMnx0ZY4","payEndDate":"lftjH","education":"Ss7vwUzlnD","preType":"2O9WD40Q","jbrIdType":"JgYPX2","fee":5252.************,"doubleNeedFlag":"C8ZqZwmlE","idNo":"SxPHEpuWwN","frName":"3xCYaFw","accountHolder":"Z","returnCode":"f8QdS3xVcl","province":"D1WqMOVWY","post":"TJNEw","fundCode":"MBD5WexKs","supportAdvanceFlag":"fI8U","pageNo":214,"frIdType":"Aj","preDueDt":************,"tel":"ygHNX74","openEndDt":"Ugb9Oj","hboneNo":"JwtZc0TJs","jbrName":"tFVtcn","investType":"OJF1xAmR","idType":"Gd2XCNNp","birth":"7YGk0X","creDt":"rMEy77iY","validityDt":"CM","fundName":"kDnGB4A","preId":"wmne","city":"HYEFaLwx1","openStartDt":"m","bankAcct":"8iDfA277y","description":"eprJ","sellVol":6107.************,"totalCount":9247,"feeRate":4292.*************,"actualController":"lwlBHp","ackAmt":2546.************,"firstPreId":"NG9BMISIN","aptitude":"KaCYiPY","addr":"tLto7u","tradeType":"o","email":"RKaa","frIdNo":"moMe0R","bankCode":"te43KOQii","nature":"3","totalPage":9987,"sex":"23Zu6i","mobile":"T5wPa","bankAddr":"dT9OsU0K33","bankProv":"8c4sMX7","custName":"Ab7F1np","scopeBusiness":"K","doubleHandleDt":************,"jbrIdNo":"6xm98tgS","bankCity":"hG9XNbR","vocation":"Ne","prebookState":"1tZl3vGc","doubleHandleFlag":"E","validity":"C8u","mark":"upELEHEldm","incomeOfFamily":"ZGoOYmjte"}
     */
    @Override
    public QueryPreBookDtlResponse execute(QueryPreBookDtlRequest request) {

        QueryPreBookDtlResponse resp = new QueryPreBookDtlResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        QueryPreBookDtlOuterResult queryPreBookDtlOuterResult = queryPreBookDtlOuterService.queryPreBook(request.getPreId());
        if (queryPreBookDtlOuterResult == null || StringUtils.isEmpty(queryPreBookDtlOuterResult.getPreId())) {
            return resp;
        }

        //根据一帐通号查询客户信息
        QueryAccHboneInfoResult queryAccHboneInfoResult = queryAccHboneInfoOuterService.queryAccHboneInfo(queryPreBookDtlOuterResult.getHboneNo());
        if (queryAccHboneInfoResult == null) {
            throw new BusinessException(ExceptionCodes.HIGH_ORDER_HBONE_ERROR, MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_HBONE_ERROR));
        }
        //0-机构；1-个人
        String invstType = queryAccHboneInfoResult.getUserType();

        //查询产品基本信息
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(queryPreBookDtlOuterResult.getFundCode());
        if (highProductBaseBean == null) {
            return resp;
        }

        //请求时间
        String dateStr = new StringBuilder(request.getAppDt()).append(request.getAppTm()).toString();
        Date appDtm = DateUtils.formatToDate(dateStr, DateUtils.YYYYMMDDHHMMSS);
        //TA交易日期
        String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());

        //查询预约信息
        String busiType = convertBusiType(queryPreBookDtlOuterResult.getTradeType());
        ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoWithDeferPurchaseConfig(request.getHbOneNo(), highProductBaseBean.getFundCode(), highProductBaseBean.getShareClass(), request.getDisCode(), appDtm, busiType);

        //查询费率
        HighProductFeeRateBean highProductFeeRateBean = null;
        if ("0".equals(convertBusiType(queryPreBookDtlOuterResult.getTradeType()))) {
            String busiCode = getBusiCode(highProductBaseBean, productAppointmentInfoBean, taTradeDt);
            highProductFeeRateBean = queryHighProductOuterService.getFundFeeRateByAmt(highProductBaseBean.getFundCode(), busiCode, invstType, highProductBaseBean.getShareClass(), queryPreBookDtlOuterResult.getAckAmt());
        }

        buildPreBookDetailInfo(resp, queryPreBookDtlOuterResult, highProductBaseBean, invstType, productAppointmentInfoBean, highProductFeeRateBean);

        return resp;

    }

    /**
     * buildPreBookDetailInfo:(构建预约详细信息)
     *
     * <AUTHOR>
     * @date 2018年1月9日 下午4:42:56
     */
    private void buildPreBookDetailInfo(QueryPreBookDtlResponse resp, QueryPreBookDtlOuterResult queryPreBookDtlOuterResult, HighProductBaseInfoBean highProductBaseBean, String invstType, ProductAppointmentInfoBean productAppointmentInfoBean, HighProductFeeRateBean highProductFeeRateBean) {

        //构建预约详细信息
        BeanUtils.copyProperties(queryPreBookDtlOuterResult, resp);

        //手续费
        if (highProductFeeRateBean != null) {
            BigDecimal fee = calFee(queryPreBookDtlOuterResult.getAckAmt(), queryPreBookDtlOuterResult.getDiscountRate(), highProductFeeRateBean,queryPreBookDtlOuterResult.getDiscountUseType(),queryPreBookDtlOuterResult.getDiscountAmt());
            resp.setFee(fee);
            resp.setFeeRate(highProductFeeRateBean.getFeeRate());
        }

        //预约信息
        if (productAppointmentInfoBean != null) {
            resp.setOpenStartDt(productAppointmentInfoBean.getOpenStartDt());
            resp.setOpenEndDt(productAppointmentInfoBean.getOpenEndDt());
        }

        resp.setInvestType(invstType);
    }

}

