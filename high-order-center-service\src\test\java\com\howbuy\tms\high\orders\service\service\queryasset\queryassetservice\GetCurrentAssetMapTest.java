package com.howbuy.tms.high.orders.service.service.queryasset.queryassetservice;

import com.howbuy.tms.high.orders.facade.search.queryasset.HighClassifyAssetResponse;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.service.queryasset.QueryAssetService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.times;

/**
 *
 * http://localhost:63342/high-order-center/high-order-center-service/target/site/jacoco/com.howbuy.tms.high.orders.service.service.queryasset/QueryAssetService.html
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({QueryAssetService.class})
@PowerMockIgnore({"javax.security.*", "javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class GetCurrentAssetMapTest {

    @InjectMocks
    private QueryAssetService server;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 产品代码为空
     */
    @Test
    public void test1() {
        server = PowerMockito.spy(server);

        server.getCurrentAssetMap(null, null, null, "1");

        Mockito.verify(server, times(0)).queryCurrentNewAsset(any(), any(), any());
    }

    /**
     * 产品代码不为空 && 获取基金收益list对象异常 && 基金收益list对象为空
     */
    @Test
    public void test2() throws Exception {
        server = PowerMockito.spy(server);

        List<String> fundCodeList = new ArrayList<>();
        fundCodeList.add("001");

        PowerMockito.doThrow(new RuntimeException("查询收益异常")).
                when(server, "queryCurrentAsset", Mockito.any(), Mockito.any(), Mockito.any());

        server.getCurrentAssetMap(fundCodeList, null, null, "1");

    }

    /**
     * 产品代码不为空 && 获取基金收益list对象 && 基金收益list对象为空
     */
    @Test
    public void test3() throws Exception {
        server = PowerMockito.spy(server);

        List<String> fundCodeList = new ArrayList<>();
        fundCodeList.add("001");

        PowerMockito.doReturn(null).
                when(server, "queryCurrentAsset", Mockito.any(), Mockito.any(), Mockito.any());

        server.getCurrentAssetMap(fundCodeList, null, null, "1");

    }

    /**
     * 产品代码不为空 && 获取基金收益list对象 && 基金收益list对象不为空 && 基金收益对象为空
     */
    @Test
    public void test4() throws Exception {
        server = PowerMockito.spy(server);

        List<String> fundCodeList = new ArrayList<>();
        fundCodeList.add("001");

        PowerMockito.doReturn(new HighClassifyAssetResponse()).
                when(server, "queryCurrentAsset", Mockito.any(), Mockito.any(), Mockito.any());

        server.getCurrentAssetMap(fundCodeList, null, null, "1");

    }

    /**
     * 产品代码不为空 && 获取基金收益list对象 && 基金收益list对象不为空 && 基金收益对象不为空
     */
    @Test
    public void test5() throws Exception {
        server = PowerMockito.spy(server);

        List<String> fundCodeList = new ArrayList<>();
        fundCodeList.add("001");

        HighClassifyAssetResponse result = new HighClassifyAssetResponse();
        List<HighFundAssetIncomeDomain> asset = new ArrayList<>();
        HighFundAssetIncomeDomain currentAssetDto = new HighFundAssetIncomeDomain();
        asset.add(currentAssetDto);
        result.setAssets(asset);

        PowerMockito.doReturn(result).
                when(server, "queryCurrentAsset", Mockito.any(), Mockito.any(), Mockito.any());

        Map<String, HighFundAssetIncomeDomain> currentAssetMap = server.getCurrentAssetMap(fundCodeList, null, null, "1");

        Assert.assertTrue(currentAssetMap.size() > 0);
    }
}
