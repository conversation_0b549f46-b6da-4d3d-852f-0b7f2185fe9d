package com.howbuy.tms.high.orders.service.service.redeemLogicService;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.interlayer.product.model.HighProductControlModel;
import com.howbuy.interlayer.product.model.WorkDayModel;
import com.howbuy.interlayer.product.model.fund.ProductAppointmentInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.ProductAppointmentInfoService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.RedeemStatusEnum;
import com.howbuy.tms.common.enums.busi.RedeemStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductChannelEnum;
import com.howbuy.tms.common.enums.database.TxOpenFlagEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductTxOpenCfgBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.dao.vo.CustBooksDtlVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.CustomerRedeemAppointInfo;
import com.howbuy.tms.high.orders.service.common.enums.VolLockStatusEnum;
import com.howbuy.tms.high.orders.service.common.utils.OpsSysMonitor;
import com.howbuy.tms.high.orders.service.facade.search.queryredeemfundlist.QueryRedeemFundListFacadeService;
import com.howbuy.tms.high.orders.service.facade.search.queryredeemfundstatus.CustomerCpAcctRedeemInfo;
import com.howbuy.tms.high.orders.service.facade.search.queryredeemfundstatus.CustomerFundRedeemInfo;
import com.howbuy.tms.high.orders.service.repository.CustBooksDtlRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.service.subcustbooks.SubCustBooksService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:查询用户赎回日历信息逻辑类
 * @Author: yun.lu
 * Date: 2024/12/23 17:20
 */
@Slf4j
@Service
public class QueryCustomerRedeemAppointInfoLogicService {
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private SubCustBooksService subCustBooksService;
    @Autowired
    private ProductAppointmentInfoService productAppointmentInfoService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    private static final String OPEN_REDEEM_DT = "00010101";

    /**
     * 查询用户产品可赎回信息
     *
     * @param txAcctNo 交易账号
     * @param fundCode 产品编码
     * @return 可赎回信息
     */
    public CustomerFundRedeemInfo queryCustomerFundRedeemInfo(String txAcctNo, String fundCode, Date currentDate) {
        log.info("QueryCustomerRedeemAppointInfoLogicService-queryCustomerFundRedeemInfo,查询用户产品可赎回信息,txAcctNo={},fundCode={},currentDate={}", txAcctNo, fundCode,currentDate);
        CustomerFundRedeemInfo customerFundRedeemInfo = new CustomerFundRedeemInfo();
        customerFundRedeemInfo.setFundCode(fundCode);
        customerFundRedeemInfo.setTxAcctNo(txAcctNo);
        customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.ALLOW.getCode());
        customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
        // 1.查询产品基础信息
        List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(highProductBaseInfoList)) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,根据产品编码查不到产品基础信息,fundCode={}", fundCode);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setErrorMsg("根据产品编码查不到产品基础信息");
            return customerFundRedeemInfo;
        }
        HighProductBaseInfoBean highProductBaseInfoBean = highProductBaseInfoList.get(0);
        // 2.是否开通赎回交易
        if (ProductChannelEnum.TP_SM.getCode().equals(highProductBaseInfoBean.getProductChannel()) || ProductChannelEnum.HIGH_FUND.getCode().equals(highProductBaseInfoBean.getProductChannel())) {
            // 查询高端产品交易开通配置信息
            HighProductTxOpenCfgBean highProductTxOpenCfgBean = queryHighProductOuterService.getHighProductTxOpenCfg(fundCode, BusinessCodeEnum.REDEEM.getCode());
            // 校验交易是否开通
            if (highProductTxOpenCfgBean == null || TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
                log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,产品没开通赎回交易,不允许赎回,fundCode={},highProductTxOpenCfgBean={}", fundCode, JSON.toJSONString(highProductTxOpenCfgBean));
                customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
                customerFundRedeemInfo.setErrorMsg("产品没开通赎回交易");
                return customerFundRedeemInfo;
            }
        }
        // 3.如果产品控制信息没有,不允许赎回
        HighProductControlModel highProductControlInfo = highProductService.getHighProductControlInfo(fundCode);
        if (highProductControlInfo == null) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,产品控制信息不存在,不允许赎回,fundCode={}", fundCode);
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setErrorMsg("产品控制信息不存在,不允许赎回");
            return customerFundRedeemInfo;
        }

        // 在途份额
        List<CustBooksDtlVo> unConfirmRedeemBalanceVolList = custBooksDtlRepository.getUnConfirmRedeemBalanceVol(txAcctNo, fundCode);
        Map<String, BigDecimal> cptRedeemUnConfirmVolMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unConfirmRedeemBalanceVolList)) {
            for (CustBooksDtlVo custBooksDtlVo : unConfirmRedeemBalanceVolList) {
                cptRedeemUnConfirmVolMap.merge(custBooksDtlVo.getCpAcctNo(), custBooksDtlVo.getUnconfirmedVol(), BigDecimal::add);
            }
        }
        if (YesOrNoEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
            hasLockPeriodFundRedeemInfo(txAcctNo, fundCode, customerFundRedeemInfo, highProductBaseInfoBean, cptRedeemUnConfirmVolMap, currentDate);
        } else {
            noLockPeriodFundRedeemInfo(highProductBaseInfoBean, customerFundRedeemInfo, highProductControlInfo, currentDate);
        }
        log.info("QueryCustomerRedeemAppointInfoLogicService-queryCustomerFundRedeemInfo,查询用户产品可赎回信息结果,customerFundRedeemInfo{}", JSON.toJSONString(customerFundRedeemInfo));
        return customerFundRedeemInfo;
    }

    /**
     * 有份额锁定期,可赎回信息构建
     */
    private void hasLockPeriodFundRedeemInfo(String txAcctNo, String fundCode, CustomerFundRedeemInfo customerFundRedeemInfo,
                                             HighProductBaseInfoBean highProductBaseInfoBean,
                                             Map<String, BigDecimal> cptRedeemUnConfirmVolMap, Date currentDate) {
        // 获取份额锁定信息
        String taTradeDt = queryTradeDayOuterService.getWorkDay(currentDate);
        List<CustomerRedeemAppointInfo> customerRedeemAppointInfoList = getCustomerRedeemAppointInfo(highProductBaseInfoBean, fundCode, txAcctNo, taTradeDt);
        if (CollectionUtils.isEmpty(customerRedeemAppointInfoList)) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-hasLockPeriodFundRedeemInfo,可赎回日历查不到,不允许赎回,fundCode={}", fundCode);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
            customerFundRedeemInfo.setErrorMsg("查询不到可赎回日历");
            return;
        }
        // 资金维度的赎回信息
        Map<String, List<CustomerRedeemAppointInfo>> customerRedeemAppointInfoMap = customerRedeemAppointInfoList.stream().collect(Collectors.groupingBy(CustomerRedeemAppointInfo::getCpAcctNo));
        List<CustomerCpAcctRedeemInfo> customerCpAcctRedeemInfoList = new ArrayList<>();
        BigDecimal canRedeemVol = BigDecimal.ZERO;
        BigDecimal unConfirmRedeemVol = BigDecimal.ZERO;
        BigDecimal lockVol = BigDecimal.ZERO;
        for (Map.Entry<String, List<CustomerRedeemAppointInfo>> stringListEntry : customerRedeemAppointInfoMap.entrySet()) {
            CustomerCpAcctRedeemInfo customerCpAcctRedeemInfo = new CustomerCpAcctRedeemInfo();
            customerCpAcctRedeemInfo.setCpAcctNo(stringListEntry.getKey());
            customerCpAcctRedeemInfo.setUnConfirmRedeemVol(cptRedeemUnConfirmVolMap.get(customerCpAcctRedeemInfo.getCpAcctNo()) == null ? BigDecimal.ZERO : cptRedeemUnConfirmVolMap.get(customerCpAcctRedeemInfo.getCpAcctNo()));
            BigDecimal subCanRedeemVol = BigDecimal.ZERO;
            for (CustomerRedeemAppointInfo customerRedeemAppointInfo : stringListEntry.getValue()) {
                customerCpAcctRedeemInfo.addLockVol(customerRedeemAppointInfo.getLockVol());
                customerCpAcctRedeemInfo.addAvailVol(customerRedeemAppointInfo.getAvailVol());
                //产品状态
                String fundQueryDt = getFundQueryDt(customerRedeemAppointInfo.getOpenStartDt(), taTradeDt);
                HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(customerRedeemAppointInfo.getFundCode(), fundQueryDt);
                boolean canRedeem = validProductRedeemStatus(highProductStatInfoBean);
                if (!canRedeem) {
                    log.info("不可赎回,因为可赎回日期产品基金状态不对,fundCode={},redeemDt={},highProductStatInfoBean={}", customerRedeemAppointInfo.getFundCode(), fundQueryDt, JSON.toJSONString(highProductStatInfoBean));
                    customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
                    customerRedeemAppointInfo.setErrorMsg("不可赎回,因为可赎回日期产品基金状态不对");
                }
                if (customerRedeemAppointInfo.getCanRedeemVol() != null) {
                    subCanRedeemVol = subCanRedeemVol.add(customerRedeemAppointInfo.getCanRedeemVol());
                }
            }
            // 份额明细里面,可用份额就已经减去冻结的了,所以汇总可用不需要减去冻结的
            customerCpAcctRedeemInfo.setCanRedeemVol(subCanRedeemVol.subtract(customerCpAcctRedeemInfo.getUnConfirmRedeemVol()));
            customerCpAcctRedeemInfoList.add(customerCpAcctRedeemInfo);
            lockVol = lockVol.add(customerCpAcctRedeemInfo.getLockVol());
            canRedeemVol = canRedeemVol.add(customerCpAcctRedeemInfo.getCanRedeemVol());
            unConfirmRedeemVol = unConfirmRedeemVol.add(customerCpAcctRedeemInfo.getUnConfirmRedeemVol());
        }
        customerFundRedeemInfo.setCustomerCpAcctRedeemInfoList(customerCpAcctRedeemInfoList);
        customerFundRedeemInfo.setCanRedeemVol(canRedeemVol);
        customerFundRedeemInfo.setLockVol(lockVol);
        customerFundRedeemInfo.setUnConfirmRedeemVol(unConfirmRedeemVol);
        customerFundRedeemInfo.setTotalVol(customerRedeemAppointInfoList.get(0).getTotalVol());
        // 如果可赎回份额是0就是不可以赎回
        if (customerFundRedeemInfo.getCanRedeemVol().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-hasLockPeriodFundRedeemInfo,可用份额不足不支持赎回,不可以赎回,customerFundRedeemInfo={}", JSON.toJSONString(customerFundRedeemInfo));
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
            customerFundRedeemInfo.setErrorMsg("可用份额不足不支持赎回,不可以赎回");
        }
        log.info("QueryCustomerRedeemAppointInfoLogicService-hasLockPeriodFundRedeemInfo,有份额锁定期的,赎回信息返回,customerFundRedeemInfo={}", JSON.toJSONString(customerFundRedeemInfo));
    }

    /**
     * 没有份额锁定期的,可赎回信息构建
     */
    private void noLockPeriodFundRedeemInfo(HighProductBaseInfoBean highProductBaseInfoBean,
                                            CustomerFundRedeemInfo customerFundRedeemInfo,
                                            HighProductControlModel highProductControlInfo,Date currentDate) {
        String openStartDt = null;
        // 支持预约赎回
        if (isSupportAdvanceRedeem(highProductBaseInfoBean.getIsScheduledTrade())) {
            // 查询赎回预约日历
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(highProductBaseInfoBean.getFundCode(), "1",
                    highProductBaseInfoBean.getShareClass(), highProductControlInfo.getDisCode(), currentDate);
            // 支持提前赎回，没有预约开放日历
            if (productAppointmentInfoBean == null) {
                log.info("noLockPeriodFundRedeemInfo-支持提前赎回，没有预约开放日历,不可以赎回,highProductBaseInfoBean={}", JSON.toJSONString(highProductBaseInfoBean));
                customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
                customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                customerFundRedeemInfo.setErrorMsg("支持提前赎回，没有预约开放日历,不可以赎回");
                return;
            }
            openStartDt = productAppointmentInfoBean.getOpenStartDt();
            // 预约开始时间
            String appointStartDtm = "";
            // 预约结束时间
            String appointEndDtm = "";
            if (StringUtils.isNotBlank(productAppointmentInfoBean.getAppointStartDt())) {
                appointStartDtm = productAppointmentInfoBean.getAppointStartDt() + productAppointmentInfoBean.getAppointStartTm();
            }
            if (StringUtils.isNotBlank(productAppointmentInfoBean.getApponitEndDt())) {
                appointEndDtm = productAppointmentInfoBean.getApponitEndDt() + productAppointmentInfoBean.getApponitEndTm();
            }
            // 支持提前赎回，当前日期在可预约赎回期限内，可赎回
            String dateTimeStr = DateUtils.formatToString(currentDate, DateUtils.YYYYMMDDHHMMSS);
            if (productAppointmentInfoBean.getAppointStartDt() == null || productAppointmentInfoBean.getApponitEndDt() == null || appointStartDtm.compareTo(dateTimeStr) > 0 || appointEndDtm.compareTo(dateTimeStr) < 0) {
                log.info("noLockPeriodFundRedeemInfo-支持预约赎回，不在预约可赎回期内,不可以赎回,highProductBaseInfoBean={}", JSON.toJSONString(highProductBaseInfoBean));
                customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
                customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
                customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
                customerFundRedeemInfo.setErrorMsg("支持预约赎回，不在预约可赎回期内,不可以赎回");
                return;
            }
        }
        String workDay = queryTradeDayOuterService.getWorkDay(currentDate);
        String fundQueryDt = getFundQueryDt(openStartDt,workDay);
        HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductBaseInfoBean.getFundCode(), fundQueryDt);
        boolean canRedeem = validProductRedeemStatus(highProductStatInfoBean);
        if (!canRedeem) {
            log.info("产品基金状态不对,不可以赎回,highProductBaseInfoBean={}", JSON.toJSONString(highProductBaseInfoBean));
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.ERROR_CONFIG.getCode());
            customerFundRedeemInfo.setErrorMsg("产品基金状态不对,不可以赎回");
            return;
        }
        // 查询资金维度的份额明细
        QueryAcctBalanceBaseInfoParamVo queryAcctBalanceBaseInfoParamVo = new QueryAcctBalanceBaseInfoParamVo();
        queryAcctBalanceBaseInfoParamVo.setTxAcctNo(customerFundRedeemInfo.getTxAcctNo());
        queryAcctBalanceBaseInfoParamVo.setFundCodeList(Collections.singletonList(customerFundRedeemInfo.getFundCode()));
        List<BalanceVo> balanceVoList = custBooksRepository.queryConfirmBalanceBaseInfo(queryAcctBalanceBaseInfoParamVo);
        if (CollectionUtils.isEmpty(balanceVoList)) {
            log.info("noLockPeriodFundRedeemInfo-查不到可用确认份额,不支持赎回,queryAcctBalanceBaseInfoParamVo={}", JSON.toJSONString(queryAcctBalanceBaseInfoParamVo));
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
            customerFundRedeemInfo.setErrorMsg("查不到可用确认份额,不支持赎回");
            return;
        }
        // 设置可赎回份额信息
        setRedeemVolInfo(customerFundRedeemInfo, balanceVoList);
        // 如果可赎回份额是0就是不可以赎回
        if (customerFundRedeemInfo.getCanRedeemVol().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("可用份额不足不支持赎回,不可以赎回,customerFundRedeemInfo={}", JSON.toJSONString(customerFundRedeemInfo));
            customerFundRedeemInfo.setCanRedeemVol(BigDecimal.ZERO);
            customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.NOT_ALLOW.getCode());
            customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
            customerFundRedeemInfo.setErrorMsg("可用份额不足不支持赎回,不可以赎回");
            return;
        }
        customerFundRedeemInfo.setRedeemStatus(RedeemStatusEnum.ALLOW.getCode());
        customerFundRedeemInfo.setRedeemStatusType(RedeemStatusTypeEnum.NORMAL.getCode());
    }

    /**
     * 设置可赎回份额信息
     */
    private void setRedeemVolInfo(CustomerFundRedeemInfo customerFundRedeemInfo, List<BalanceVo> balanceVoList) {
        BigDecimal canRedeemVol = BigDecimal.ZERO;
        BigDecimal unConfirmRedeemVol = BigDecimal.ZERO;
        BigDecimal totalVol = BigDecimal.ZERO;
        for (BalanceVo balanceVo : balanceVoList) {
            CustomerCpAcctRedeemInfo customerCpAcctRedeemInfo = new CustomerCpAcctRedeemInfo();
            customerCpAcctRedeemInfo.setCpAcctNo(balanceVo.getCpAcctNo());
            BigDecimal balanceVol = balanceVo.getBalanceVol() == null ? BigDecimal.ZERO : balanceVo.getBalanceVol();
            BigDecimal justFrznVol = balanceVo.getJustFrznVol() == null ? BigDecimal.ZERO : balanceVo.getJustFrznVol();
            customerCpAcctRedeemInfo.addAvailVol(balanceVol.subtract(justFrznVol));
            customerCpAcctRedeemInfo.addLockVol(BigDecimal.ZERO);
            customerCpAcctRedeemInfo.setUnConfirmRedeemVol(balanceVo.getUnconfirmedVol() == null ? BigDecimal.ZERO : balanceVo.getUnconfirmedVol());
            customerCpAcctRedeemInfo.setCanRedeemVol(customerCpAcctRedeemInfo.getAvailVol() == null ? BigDecimal.ZERO : customerCpAcctRedeemInfo.getAvailVol().subtract(customerCpAcctRedeemInfo.getUnConfirmRedeemVol()));
            canRedeemVol = canRedeemVol.add(customerCpAcctRedeemInfo.getCanRedeemVol());
            unConfirmRedeemVol = unConfirmRedeemVol.add(customerCpAcctRedeemInfo.getUnConfirmRedeemVol());
            totalVol = totalVol.add(balanceVol);
        }
        customerFundRedeemInfo.setTotalVol(totalVol);
        customerFundRedeemInfo.setCanRedeemVol(canRedeemVol);
        customerFundRedeemInfo.setUnConfirmRedeemVol(unConfirmRedeemVol);
    }

    private boolean isSupportAdvanceRedeem(String supportAdvanceFlag) {
        if (StringUtils.isEmpty(supportAdvanceFlag)) {
            return false;
        }
        return SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag) || SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag);
    }

    /**
     * 是否支持赎回
     */
    private boolean validProductRedeemStatus(HighProductStatInfoBean highProductStatInfoBean) {
        if (highProductStatInfoBean == null || StringUtils.isEmpty(highProductStatInfoBean.getFundStat())) {
            return false;
        }

        return MDataDic.CAN_REDEEM_SET.contains(highProductStatInfoBean.getFundStat());
    }

    /**
     * 查询产品基金状态日期
     */
    private String getFundQueryDt(String openStartDt, String workDay) {
        String fundQueryDt = workDay;
        if (StringUtils.isNotEmpty(openStartDt) && openStartDt.compareTo(workDay) > 0) {
            fundQueryDt = openStartDt;
        }
        log.info("QueryCustomerRedeemAppointInfoLogicService-getFundQueryDt,查询产品基金状态日期,openStartDt={},workDay={}", openStartDt, workDay);
        return fundQueryDt;
    }

    /**
     * 查询用户赎回日历信息
     */
    public List<CustomerRedeemAppointInfo> getCustomerRedeemAppointInfo(HighProductBaseInfoBean highProductBaseInfoBean, String fundCode, String txAcctNo, String taTradeDt) {
        log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,查询用户赎回日历信息,fundCode={},txAcctNo={}", fundCode, txAcctNo);
        List<CustomerRedeemAppointInfo> customerRedeemAppointInfoList = new ArrayList<>();

        // 1.根据产品查询产品份额明细
        List<SubCustBooksPo> allSubCustomerBooksPoList = subCustBooksService.queryByFundCode(txAcctNo, Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(allSubCustomerBooksPoList)) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,根据产品查询不到份额明细,fundCode={}", fundCode);
            return customerRedeemAppointInfoList;
        }
        // 2.查询产品是否支持份额锁定
        if (YesOrNoEnum.NO.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
            log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,查询产品是不支持份额锁定,fundCode={}", fundCode);
            return customerRedeemAppointInfoList;
        }
        // 4.查询预约日历
        List<String> openRedeemDtList = allSubCustomerBooksPoList.stream().map(x -> {
            if (OPEN_REDEEM_DT.equals(x.getOpenRedeDt())) {
                return "20991231";
            }
            return x.getOpenRedeDt();
        }).distinct().collect(Collectors.toList());
        Map<String, ProductAppointmentInfoModel> productAppointmentInfoModelMap = productAppointmentInfoService.queryEffectOrWillEffectAppointByOpenDt(fundCode, "1", openRedeemDtList);
        // 5.返回用户赎回日历信息
        BigDecimal totalVol = BigDecimal.ZERO;
        for (SubCustBooksPo custBooksPo : allSubCustomerBooksPoList) {
            totalVol = totalVol.add(custBooksPo.getBalanceVol());
        }
        for (SubCustBooksPo subCustBooksPo : allSubCustomerBooksPoList) {
            ProductAppointmentInfoModel productAppointmentInfoModel = productAppointmentInfoModelMap.get(subCustBooksPo.getOpenRedeDt());
            CustomerRedeemAppointInfo customerRedeemAppointInfo = getCustomerRedeemAppointInfo(taTradeDt, fundCode, totalVol, highProductBaseInfoBean, productAppointmentInfoModel, subCustBooksPo);
            if (customerRedeemAppointInfo != null && customerRedeemAppointInfo.isError()) {
                OpsSysMonitor.businessWarn(customerRedeemAppointInfo.getErrorMsg(), OpsSysMonitor.INFO);
            }
            customerRedeemAppointInfoList.add(customerRedeemAppointInfo);
        }
        log.info("QueryCustomerRedeemAppointInfoLogicService-getCustomerRedeemAppointInfo,查询用户赎回日历信息结果,customerRedeemAppointInfoList={}", JSON.toJSONString(customerRedeemAppointInfoList));
        return customerRedeemAppointInfoList;
    }

    public CustomerRedeemAppointInfo getCustomerRedeemAppointInfoBySubCustBooks(SubCustBooksPo subCustBooksPo, String currentDt) {
        Map<String, ProductAppointmentInfoModel> productAppointmentInfoModelMap = productAppointmentInfoService.queryEffectOrWillEffectAppointByOpenDt(subCustBooksPo.getFundCode(), "1", Collections.singletonList(subCustBooksPo.getOpenRedeDt()));
        ProductAppointmentInfoModel productAppointmentInfoModel = null;
        if (productAppointmentInfoModelMap != null && productAppointmentInfoModelMap.containsKey(subCustBooksPo.getOpenRedeDt())) {
            productAppointmentInfoModel = productAppointmentInfoModelMap.get(subCustBooksPo.getOpenRedeDt());
        }
        HighProductBaseInfoBean highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(subCustBooksPo.getFundCode());
        CustomerRedeemAppointInfo customerRedeemAppointInfo = getCustomerRedeemAppointInfo(currentDt, subCustBooksPo.getFundCode(), subCustBooksPo.getAvailVol(), highProductBaseBean, productAppointmentInfoModel, subCustBooksPo);
        if (customerRedeemAppointInfo != null) {
            if (customerRedeemAppointInfo.isError()) {
                OpsSysMonitor.businessWarn(customerRedeemAppointInfo.getErrorMsg(), OpsSysMonitor.INFO);
            }
            //产品状态
            String fundQueryDt = getFundQueryDt(customerRedeemAppointInfo.getOpenStartDt(), currentDt);
            HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(subCustBooksPo.getFundCode(), fundQueryDt);
            boolean canRedeem = validProductRedeemStatus(highProductStatInfoBean);
            if (!canRedeem) {
                log.info("不可赎回,因为可赎回日期产品基金状态不对,fundCode={},redeemDt={},highProductStatInfoBean={}", subCustBooksPo.getFundCode(), fundQueryDt, JSON.toJSONString(highProductStatInfoBean));
                customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
                customerRedeemAppointInfo.setErrorMsg("不可赎回,因为可赎回日期产品基金状态不对");
            }
        }
        return customerRedeemAppointInfo;
    }


    /**
     * 设置用户赎回日历信息
     */
    public CustomerRedeemAppointInfo getCustomerRedeemAppointInfo(String nowDt, String fundCode, BigDecimal totalVol,
                                                                  HighProductBaseInfoBean highProductBaseInfoBean,
                                                                  ProductAppointmentInfoModel productAppointmentInfoModel,
                                                                  SubCustBooksPo subCustBooksPo) {
        CustomerRedeemAppointInfo customerRedeemAppointInfo = new CustomerRedeemAppointInfo();
        // 客户号
        customerRedeemAppointInfo.setTxAcctNo(subCustBooksPo.getTxAcctNo());
        customerRedeemAppointInfo.setSubCustBooksId(subCustBooksPo.getId());
        // 产品代码
        customerRedeemAppointInfo.setFundCode(highProductBaseInfoBean.getFundCode());
        // 产品名称
        customerRedeemAppointInfo.setFundName(highProductBaseInfoBean.getFundName());
        // 总份额
        customerRedeemAppointInfo.setTotalVol(totalVol);
        // 可用余额
        customerRedeemAppointInfo.setAvailVol(subCustBooksPo.getAvailVol());
        // 司法冻结份额
        customerRedeemAppointInfo.setJustFrznVol(subCustBooksPo.getJustFrznVol());
        // 冻结份额
        customerRedeemAppointInfo.setFrznVol(subCustBooksPo.getFrznVol());
        customerRedeemAppointInfo.setError(false);
        // 份额可赎回日期
        String openRedeDt = subCustBooksPo.getOpenRedeDt();
        customerRedeemAppointInfo.setOpenRedeemDt(openRedeDt);
        customerRedeemAppointInfo.setCpAcctNo(subCustBooksPo.getCpAcctNo());
        boolean isCyclicLock = StringUtils.isNotBlank(highProductBaseInfoBean.getIsCyclicLock()) && YesOrNoEnum.YES.getCode().equals(highProductBaseInfoBean.getIsCyclicLock());
        // 是否支持预约
        if (QueryRedeemFundListFacadeService.isSupportAdvanceRedeem(highProductBaseInfoBean.getIsScheduledTrade())) {
            // 查不到生效或者待生效的开放日历
            if (productAppointmentInfoModel == null) {
                log.info("查不到生效或者待生效的开放日历,fundCode={}", fundCode);
                customerRedeemAppointInfo.setLockVol(subCustBooksPo.getAvailVol());
                customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
                customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.LOCKING.getStatus());
                return customerRedeemAppointInfo;
            }
            String appointStartDt = productAppointmentInfoModel.getAppointStartDt();
            String appointEndDt = productAppointmentInfoModel.getApponitEndDt();
            String openStartDt = productAppointmentInfoModel.getOpenStartDt();
            String openEndDt = productAppointmentInfoModel.getOpenEndDt();
            customerRedeemAppointInfo.setOpenStartDt(openStartDt);
            customerRedeemAppointInfo.setOpenEndDt(openEndDt);
            if (appointEndDt.compareTo(openStartDt) < 0) {
                // 如果预约结束时间小于开放开始时间,那么锁定期不能再开放区间内
                if (openStartDt.compareTo(openRedeDt) < 0 && openEndDt.compareTo(openRedeDt) >= 0) {
                    log.info("支持预约,如果预约结束时间小于开放开始时间,那么锁定期不能再开放区间内,fundCode={},openStartDt={},openEndDt={},openRedeDt={},subCustBookId={}", fundCode, openStartDt, openEndDt, openRedeDt, subCustBooksPo.getId());
                    customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.CAN_NOT_REDEEM.getStatus());
                    customerRedeemAppointInfo.setLockVol(subCustBooksPo.getAvailVol());
                    customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
                    customerRedeemAppointInfo.setErrorMsg("支持预约,如果预约结束时间小于开放开始时间,那么锁定期不能再开放区间内");
                    return customerRedeemAppointInfo;
                } else {
                    log.info("支持预约,预约结束时间小于开放开始时间,可赎回日期就是预约开始时间到预约截止时间,productAppointmentInfoModel={},subCustBookId={}", JSON.toJSONString(productAppointmentInfoModel), subCustBooksPo.getId());
                    customerRedeemAppointInfo.setRedeemStartDt(appointStartDt);
                    customerRedeemAppointInfo.setRedeemEndDt(appointEndDt);
                    customerRedeemAppointInfo.setOpenEndDt(openEndDt);
                    customerRedeemAppointInfo.setOpenStartDt(openStartDt);
                }
            } else {
                if (openRedeDt.compareTo(openStartDt) <= 0) {
                    log.info("支持预约,预约结束时间大于开放开始时间,赎回锁定日期小于等于开放开始日,可赎回日期就是预约开始-预约结束,fundCode={},openRedeDt={},subCustBookId={},productAppointmentInfoModel={}", fundCode, openRedeDt, subCustBooksPo.getId(), JSON.toJSONString(productAppointmentInfoModel));
                    customerRedeemAppointInfo.setRedeemStartDt(appointStartDt);
                    customerRedeemAppointInfo.setRedeemEndDt(appointEndDt);
                    customerRedeemAppointInfo.setOpenEndDt(openEndDt);
                    customerRedeemAppointInfo.setOpenStartDt(openStartDt);
                } else {
                    if (appointEndDt.compareTo(openRedeDt) < 0) {
                        log.info("支持预约,预约结束时间大于开放开始时间,预约结束时间小于锁定截止日,意味着该日历下永远不可赎回,fundCode={},appointEndDt={},openRedeDt={},subCustBookId={},productAppointmentInfoModel={}", fundCode, appointEndDt, openRedeDt, subCustBooksPo.getId(), JSON.toJSONString(productAppointmentInfoModel));
                        String msg = "支持预约,预约结束时间大于开放开始时间,预约结束时间小于锁定截止日,意味着该日历下永远不可赎回,用户:" + customerRedeemAppointInfo.getTxAcctNo() + ",产品:" + customerRedeemAppointInfo.getFundCode() + ",日历id:" + productAppointmentInfoModel.getAppointId() + ",锁定期:" + openRedeDt + ",子账本明细id:" + subCustBooksPo.getId();
                        customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.CAN_NOT_REDEEM.getStatus());
                        customerRedeemAppointInfo.setLockVol(subCustBooksPo.getAvailVol());
                        customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
                        customerRedeemAppointInfo.setError(true);
                        customerRedeemAppointInfo.setErrorMsg(msg);
                        customerRedeemAppointInfo.setRedeemStartDt(null);
                        customerRedeemAppointInfo.setRedeemEndDt(null);
                        customerRedeemAppointInfo.setOpenEndDt(null);
                        customerRedeemAppointInfo.setOpenStartDt(null);
                        return customerRedeemAppointInfo;
                    } else {
                        if (isCyclicLock) {
                            log.info("支持预约,预约结束时间大于开放开始时间,循环锁定的,可赎回日期就是赎回锁定期,fundCode={},openRedeDt={},subCustBookId={},productAppointmentInfoModel={}", fundCode, openRedeDt, subCustBooksPo.getId(), JSON.toJSONString(productAppointmentInfoModel));
                            customerRedeemAppointInfo.setRedeemStartDt(openRedeDt);
                            customerRedeemAppointInfo.setRedeemEndDt(openRedeDt);
                            customerRedeemAppointInfo.setOpenEndDt(openEndDt);
                            customerRedeemAppointInfo.setOpenStartDt(openStartDt);
                        } else {
                            String currRedeemDay = getMaxDt(openStartDt, openRedeDt);
                            log.info("支持预约,预约结束时间大于开放开始时间,可赎回日期就是max(赎回锁定期,开放开始日期)-预约截止时间,fundCode={},currRedeemDay={},subCustBookId={},productAppointmentInfoModel={}", fundCode, currRedeemDay, subCustBooksPo.getId(), JSON.toJSONString(productAppointmentInfoModel));
                            customerRedeemAppointInfo.setRedeemStartDt(currRedeemDay);
                            customerRedeemAppointInfo.setRedeemEndDt(appointEndDt);
                            customerRedeemAppointInfo.setOpenEndDt(openEndDt);
                            customerRedeemAppointInfo.setOpenStartDt(openStartDt);
                        }
                    }
                }
            }
        } else {
            // 不支持预约的情况下,跟预约没关系,就看当前时间跟赎回锁定期
            if (isCyclicLock) {
                log.info("不支持预约赎回,循环锁定的产品,可赎回只能是赎回锁定期,fundCode={},openRedeDt={},subCustBookId={}", fundCode, openRedeDt, subCustBooksPo.getId());
                customerRedeemAppointInfo.setRedeemStartDt(openRedeDt);
                customerRedeemAppointInfo.setRedeemEndDt(openRedeDt);
            } else {
                log.info("不支持预约赎回,非循环锁定的产品,超过锁定期就可以赎回,fundCode={},openRedeemDt={},subCustBookId={}", fundCode, openRedeDt, subCustBooksPo.getId());
                customerRedeemAppointInfo.setRedeemStartDt(openRedeDt);
                // 非循环锁定,非预约的只要超过锁定结束日就可以赎回
                customerRedeemAppointInfo.setRedeemEndDt(null);
            }
        }
        setLockStatus(nowDt, subCustBooksPo, customerRedeemAppointInfo, isCyclicLock);
        log.info("设置用户赎回日历信息结果,subCustBookId={},nowDt={},customerRedeemAppointInfo={}", subCustBooksPo.getId(), nowDt, JSON.toJSONString(customerRedeemAppointInfo));
        return customerRedeemAppointInfo;
    }

    private void setLockStatus(String nowDt, SubCustBooksPo subCustBooksPo, CustomerRedeemAppointInfo customerRedeemAppointInfo, boolean isCyclicLock) {
        // 设置锁定状态
        setLockStatus(customerRedeemAppointInfo, nowDt);
        // 如果是循环锁定的产品,出现锁定期已过,说明锁定期没有及时更新,将状态改为异常
        if (isCyclicLock) {
            if (VolLockStatusEnum.LOCK_OVER.getStatus().equals(customerRedeemAppointInfo.getLockStatus())) {
                log.info("循环锁定的,锁定状态是锁定已过期,说明锁定期没有及时更新,将状态改为异常,customerRedeemAppointInfo={}", JSON.toJSON(customerRedeemAppointInfo));
                String msg = "循环锁定的,锁定状态是锁定已过期,说明锁定期没有及时更新,用户:" + customerRedeemAppointInfo.getTxAcctNo() + ",产品:" + customerRedeemAppointInfo.getFundCode() + ",锁定日历信息:" + JSON.toJSONString(customerRedeemAppointInfo) + ",子账本明细id:" + subCustBooksPo.getId();
                customerRedeemAppointInfo.setError(true);
                customerRedeemAppointInfo.setErrorMsg(msg);
                customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.CAN_NOT_REDEEM.getStatus());
                customerRedeemAppointInfo.setRedeemStartDt(null);
                customerRedeemAppointInfo.setRedeemEndDt(null);
                customerRedeemAppointInfo.setOpenEndDt(null);
                customerRedeemAppointInfo.setOpenStartDt(null);
            }
        }
        if (!VolLockStatusEnum.LOCK_REDEEMABLE.getStatus().equals(customerRedeemAppointInfo.getLockStatus())) {
            customerRedeemAppointInfo.setLockVol(subCustBooksPo.getAvailVol());
            customerRedeemAppointInfo.setCanRedeemVol(BigDecimal.ZERO);
        } else {
            customerRedeemAppointInfo.setCanRedeemVol(subCustBooksPo.getAvailVol());
            customerRedeemAppointInfo.setLockVol(BigDecimal.ZERO);
        }
    }

    /**
     * 设置锁定状态
     *
     * @param customerRedeemAppointInfo 预约信息
     * @param nowDt                     当前时间
     */
    private void setLockStatus(CustomerRedeemAppointInfo customerRedeemAppointInfo, String nowDt) {
        if (customerRedeemAppointInfo.getRedeemEndDt() != null && customerRedeemAppointInfo.getRedeemStartDt() != null) {
            if (customerRedeemAppointInfo.getRedeemEndDt().compareTo(customerRedeemAppointInfo.getRedeemStartDt()) < 0) {
                log.info("可赎回结束日期小于可赎回开始日,不可以赎回,fundCode={},redeemStartDt={},redeemEndDt={}", customerRedeemAppointInfo.getFundCode(), customerRedeemAppointInfo.getRedeemStartDt(), customerRedeemAppointInfo.getRedeemEndDt());
                customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.CAN_NOT_REDEEM.getStatus());
                customerRedeemAppointInfo.setLockVol(customerRedeemAppointInfo.getAvailVol());
                customerRedeemAppointInfo.setErrorMsg("可赎回结束日期小于可赎回开始日,不可以赎回");
                customerRedeemAppointInfo.setRedeemStartDt(null);
                customerRedeemAppointInfo.setRedeemEndDt(null);
                customerRedeemAppointInfo.setOpenEndDt(null);
                customerRedeemAppointInfo.setOpenStartDt(null);
                return;
            }
        }
        if (customerRedeemAppointInfo.getRedeemStartDt() != null && nowDt.compareTo(customerRedeemAppointInfo.getRedeemStartDt()) < 0) {
            customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.LOCKING.getStatus());
            customerRedeemAppointInfo.setLockVol(customerRedeemAppointInfo.getAvailVol());
            customerRedeemAppointInfo.setErrorMsg("锁定中");
        } else if (StringUtils.isBlank(customerRedeemAppointInfo.getRedeemEndDt()) || nowDt.compareTo(customerRedeemAppointInfo.getRedeemEndDt()) > 0) {
            customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.LOCK_OVER.getStatus());
            customerRedeemAppointInfo.setErrorMsg("锁定已过,不可以赎回");
            customerRedeemAppointInfo.setLockVol(customerRedeemAppointInfo.getAvailVol());
            customerRedeemAppointInfo.setRedeemStartDt(null);
            customerRedeemAppointInfo.setRedeemEndDt(null);
            customerRedeemAppointInfo.setOpenEndDt(null);
            customerRedeemAppointInfo.setOpenStartDt(null);
        } else {
            customerRedeemAppointInfo.setErrorMsg("锁定可赎回");
            customerRedeemAppointInfo.setLockStatus(VolLockStatusEnum.LOCK_REDEEMABLE.getStatus());
        }
    }

    /**
     * 获取两个日期的大值
     *
     * @param tradeDt     交易日期
     * @param openStartDt 赎回锁定期
     */
    private String getMaxDt(String tradeDt, String openStartDt) {
        if (StringUtils.isNotEmpty(openStartDt)) {
            if (openStartDt.compareTo(tradeDt) > 0) {
                return openStartDt;
            }

        }
        return tradeDt;
    }
}
