package com.howbuy.tms.high.orders.dao.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 非香港待确认买入订单查询结果VO
 * @Author: AI Assistant
 * @Date: 2025/09/01 00:00:00
 */
@Getter
@Setter
public class UnConfirmUnHkBuyOrderVo {

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 是否合并上报,1:是,0:不是
     */
    private String mergeSubmitFlag;

    /**
     * 主订单号
     */
    private String mainDealOrderNo;

    /**
     * 订单交易类型,1:直销,2:代销
     */
    private String scaleType;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;

    /**
     * 净申请金额(当前币种)
     */
    private BigDecimal currencyNetAppAmt;

    /**
     * 业务类型
     */
    private String mBusiCode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 储蓄罐冻结金额(当前币种)
     */
    private BigDecimal currencyCxgFrznAmt;

    /**
     * 储蓄罐冻结金额
     */
    private BigDecimal cxgFrznAmt;
}
