package com.howbuy.tms.common.sequence;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.tms.common.utils.StringUtils;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 优化后的KeyGenerator，解决并发重复问题
 */
public class KeyGeneratorOptimized {
    private static CacheService cacheService = CacheServiceImpl.getInstance();
    private static String REMOTE_COUNTER_KEY = "TMS_COUNTER_KEY";
    private static long BATCH_COUNT = 1000L;
    private static long LOCAL_COUNT_CEIL = 0L;
    private static int DEFAULT_TIMEOUT = 600;
    private static AtomicInteger LOCAL_COUNTER = new AtomicInteger(0);
    
    // 新增：纳秒级计数器，用于同一毫秒内的区分
    private static AtomicLong NANO_COUNTER = new AtomicLong(0);
    private static volatile long lastTimestamp = 0L;

    /**
     * 优化方案1：增加纳秒级时间戳和线程标识
     */
    public static String generateOptimized1(TableBusiCode tableBusiCode, String acctNo) {
        if (acctNo == null || acctNo.length() < 3) {
            throw new IllegalArgumentException("acctNo : " + acctNo);
        }
        if (tableBusiCode == null) {
            throw new IllegalArgumentException("tableBusiCode.");
        }

        // 使用纳秒级时间戳
        String timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));
        String acctStamp = acctNo.substring(acctNo.length() - 3);
        String sequence = getSequence();
        
        // 添加线程ID的后3位作为额外区分
        String threadId = String.format("%03d", Thread.currentThread().getId() % 1000);
        
        return tableBusiCode.getValue() + timeStamp + sequence + threadId + acctStamp;
    }

    /**
     * 优化方案2：使用分布式锁确保原子性
     */
    public static String generateOptimized2(TableBusiCode tableBusiCode, String acctNo) {
        if (acctNo == null || acctNo.length() < 3) {
            throw new IllegalArgumentException("acctNo : " + acctNo);
        }
        if (tableBusiCode == null) {
            throw new IllegalArgumentException("tableBusiCode.");
        }

        // 使用账号和业务代码作为锁的key，确保相同参数的调用串行执行
        String lockKey = "LOCK_" + tableBusiCode.getValue() + "_" + acctNo.substring(acctNo.length() - 3);
        
        try {
            // 尝试获取分布式锁
            if (cacheService.setNx(lockKey, "1", 1)) { // 1秒超时
                String timeStamp = DateUtil.formatNowDate(DateUtil.STR_PATTERN).substring(2);
                String acctStamp = acctNo.substring(acctNo.length() - 3);
                String sequence = getSequence();
                return tableBusiCode.getValue() + timeStamp + sequence + acctStamp;
            } else {
                // 获取锁失败，等待后重试
                Thread.sleep(1);
                return generateOptimized2(tableBusiCode, acctNo);
            }
        } catch (Exception e) {
            // 降级到方案1
            return generateOptimized1(tableBusiCode, acctNo);
        } finally {
            cacheService.del(lockKey);
        }
    }

    /**
     * 优化方案3：基于时间戳的单调递增序列
     */
    public static String generateOptimized3(TableBusiCode tableBusiCode, String acctNo) {
        if (acctNo == null || acctNo.length() < 3) {
            throw new IllegalArgumentException("acctNo : " + acctNo);
        }
        if (tableBusiCode == null) {
            throw new IllegalArgumentException("tableBusiCode.");
        }

        long currentTimestamp = System.currentTimeMillis();
        long nanoIncrement = getNanoIncrement(currentTimestamp);
        
        String timeStamp = DateUtil.formatNowDate("yyMMddHHmmssSSS");
        String acctStamp = acctNo.substring(acctNo.length() - 3);
        String sequence = getSequence();
        String nanoSeq = String.format("%03d", nanoIncrement % 1000);
        
        return tableBusiCode.getValue() + timeStamp + sequence + nanoSeq + acctStamp;
    }

    /**
     * 获取纳秒级递增序列
     */
    private static synchronized long getNanoIncrement(long currentTimestamp) {
        if (currentTimestamp != lastTimestamp) {
            lastTimestamp = currentTimestamp;
            NANO_COUNTER.set(0);
        }
        return NANO_COUNTER.incrementAndGet();
    }

    /**
     * 推荐方案：综合优化方案
     */
    public static String generateRecommended(TableBusiCode tableBusiCode, String acctNo) {
        if (acctNo == null || acctNo.length() < 3) {
            throw new IllegalArgumentException("acctNo : " + acctNo);
        }
        if (tableBusiCode == null) {
            throw new IllegalArgumentException("tableBusiCode.");
        }

        // 1. 使用更精确的时间戳（包含毫秒）
        String timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmssSSS"));
        
        // 2. 获取全局唯一序列号
        String sequence = getSequence();
        
        // 3. 添加机器标识（可以从配置中获取，这里简化为进程ID的后2位）
        String machineId = String.format("%02d", getProcessId() % 100);
        
        // 4. 账号后缀
        String acctStamp = acctNo.substring(acctNo.length() - 3);
        
        // 5. 组装最终ID：业务代码 + 时间戳(15位) + 序列号(8位) + 机器ID(2位) + 账号后缀(3位)
        return tableBusiCode.getValue() + timeStamp + sequence + machineId + acctStamp;
    }

    /**
     * 获取进程ID
     */
    private static int getProcessId() {
        try {
            String processName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
            return Integer.parseInt(processName.split("@")[0]);
        } catch (Exception e) {
            return (int) (System.currentTimeMillis() % 10000);
        }
    }

    // 保持原有方法不变，确保向后兼容
    public static String getSequence() {
        int currCount = getSequenceOrigin();
        return StringUtils.autoFill("" + currCount, 8, '0', true);
    }

    public static int getSequenceOrigin() {
        int currCount = LOCAL_COUNTER.incrementAndGet();
        if ((long)currCount >= LOCAL_COUNT_CEIL) {
            synchronized(KeyGeneratorOptimized.class) {
                if ((long)currCount >= LOCAL_COUNT_CEIL) {
                    String key = REMOTE_COUNTER_KEY;
                    LOCAL_COUNT_CEIL = cacheService.incrBy(key, BATCH_COUNT);
                    if (LOCAL_COUNT_CEIL == BATCH_COUNT) {
                        cacheService.expires(key, DEFAULT_TIMEOUT);
                    }
                    LOCAL_COUNTER.set((int)(LOCAL_COUNT_CEIL - BATCH_COUNT));
                }
                currCount = LOCAL_COUNTER.incrementAndGet();
            }
        }
        return currCount;
    }
}
