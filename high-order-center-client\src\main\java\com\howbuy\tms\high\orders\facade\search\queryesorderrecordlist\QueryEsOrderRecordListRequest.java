package com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;

import java.util.List;

/**
 * @Description:查询es交易记录入参
 * @Author: yun.lu
 * Date: 2025/6/23 19:59
 */
public class QueryEsOrderRecordListRequest extends OrderSearchBaseRequest {
    /**
     * 分销渠道
     */
    private List<String> disCodeList;
    /**
     * 不过滤香港产品,1:不过滤,0:过滤
     */
    private String notFilterHkFund;
    /**
     * 不过滤好臻产品,1:不过滤,0:过滤
     */
    private String notFilterHzFund;

    /**
     * 是否授权,1:授权,0:未授权
     */
    private String isAuth;

    /**
     * 业务类型
     */
    private List<String> mBusiCodeList;

    /**
     * 订单状态
     */
    private List<String> orderStatusList;
    /**
     * 申请开始日期
     */
    private String appDateStart;
    /**
     * 申请结束日期
     */
    private String appDateEnd;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 产品简称
     */
    private String fundName;


    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist.QueryEsOrderRecordListFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryEsOrderRecordListService
     * @apiName execute
     * @apiDescription es查询交易记录
     * @apiParam (请求参数) {Array} disCodeList 分销渠道
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:不过滤,0:过滤
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:不过滤,0:过滤
     * @apiParam (请求参数) {Array} mBusiCodeList 业务类型
     * @apiParam (请求参数) {Array} orderStatusList 订单状态
     * @apiParam (请求参数) {String} appDateStart 申请开始日期
     * @apiParam (请求参数) {String} appDateEnd 申请结束日期
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {String} fundName 产品简称
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode
     * @apiParam (请求参数) {String} outletCode
     * @apiParam (请求参数) {String} appDt
     * @apiParam (请求参数) {String} appTm
     * @apiParam (请求参数) {Number} pageNo
     * @apiParam (请求参数) {Number} pageSize
     * @apiParam (请求参数) {String} operIp
     * @apiParam (请求参数) {String} txCode
     * @apiParam (请求参数) {String} txChannel
     * @apiParam (请求参数) {String} dataTrack
     * @apiParam (请求参数) {String} subOutletCode
     * @apiParamExample 请求参数示例
     * notFilterHzFund=E&appDateStart=Nl&hbOneNo=g&mBusiCodeList=cUhNN6J3&orderStatusList=KaoIU&pageSize=3541&appDateEnd=79Jvtc&disCode=k44&txChannel=m&appTm=POHrvNAj&disCodeList=zkLpRtD&subOutletCode=9JGv&pageNo=9012&operIp=LKMgW&txAcctNo=5l&cpAcctNo=BbI0t&appDt=Jf22&dataTrack=kVc&notFilterHkFund=FZ&fundName=1p36wNzq2s&txCode=D3&outletCode=mLFKh1svmS
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {Array} dealOrderList 高端订单列表
     * @apiSuccess (响应结果) {String} dealOrderList.dealNo 客户订单号
     * @apiSuccess (响应结果) {String} dealOrderList.disCode 分销代码
     * @apiSuccess (响应结果) {String} dealOrderList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} dealOrderList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} dealOrderList.bankAcct 银行账号
     * @apiSuccess (响应结果) {String} dealOrderList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} dealOrderList.productName 产品名称
     * @apiSuccess (响应结果) {String} dealOrderList.productAttr 产品简称
     * @apiSuccess (响应结果) {String} dealOrderList.productCode 产品代码
     * @apiSuccess (响应结果) {Number} dealOrderList.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} dealOrderList.appVol 申请份额
     * @apiSuccess (响应结果) {Number} dealOrderList.appDtm 申请日期时间
     * @apiSuccess (响应结果) {String} dealOrderList.payStatus 付款状态
     * @apiSuccess (响应结果) {String} dealOrderList.orderStatus 订单状态
     * @apiSuccess (响应结果) {String} dealOrderList.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {Number} dealOrderList.fee 手续费
     * @apiSuccess (响应结果) {String} dealOrderList.mBusiCode 中台业务码
     * @apiSuccess (响应结果) {String} dealOrderList.scaleType 销售类型: 1-直销; 2-代销
     * @apiSuccess (响应结果) {String} dealOrderList.tradeStatus 交易状态
     * @apiSuccess (响应结果) {String} dealOrderList.divMode 分红方式
     * @apiSuccess (响应结果) {Number} dealOrderList.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} dealOrderList.ackAmt 确认金额
     * @apiSuccess (响应结果) {String} dealOrderList.ackDt 确认日期
     * @apiSuccess (响应结果) {Number} dealOrderList.nav 净值
     * @apiSuccess (响应结果) {String} dealOrderList.productType 产品类型
     * @apiSuccess (响应结果) {String} dealOrderList.submitTaDt 上报TA日期
     * @apiSuccess (响应结果) {String} dealOrderList.productSubType 产品子类型
     * @apiSuccess (响应结果) {String} dealOrderList.currency 币种
     * @apiSuccess (响应结果) {String} dealOrderList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
     * @apiSuccess (响应结果) {String} dealOrderList.txAckFlag 确认标识
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {Number} totalCount
     * @apiSuccess (响应结果) {Number} totalPage
     * @apiSuccess (响应结果) {Number} pageNo
     * @apiSuccess (响应结果) {String} txId
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"gc","hasHZProduct":"ZfYTBkELn3","totalPage":3067,"pageNo":3908,"description":"h1yl","txId":"O","hasHKProduct":"rviHRa","totalCount":5295,"dealOrderList":[{"appAmt":4819.************,"fee":304.**************,"bankAcct":"MejWFv0","txAckFlag":"n5uom8Y5","orderStatus":"s7irlS","taTradeDt":"FQhFkgV","disCode":"WY66PG","mBusiCode":"bfl8w","productSubType":"Sh9NbDf6","productName":"2DCi2IAM62","productAttr":"JBYiMtzEbE","ackAmt":9158.***********,"appVol":5298.*************,"txAcctNo":"DyA","appDtm":************,"cpAcctNo":"88rkJ","currency":"Llp2CB","hkSaleFlag":"04VoY","productType":"MfwaUiKXwX","bankCode":"ra6lyI","ackVol":9869.***********,"nav":3433.************,"submitTaDt":"c5UEagZoE","divMode":"TNsQ000YG","dealNo":"JtB7niIv","productCode":"vfZ4Ihho","scaleType":"4583","tradeStatus":"y","ackDt":"SGKBshNR3","payStatus":"r"}]}
     */
    public QueryEsOrderRecordListRequest() {
    }

    public String getNotFilterHkFund() {
        return notFilterHkFund;
    }

    public void setNotFilterHkFund(String notFilterHkFund) {
        this.notFilterHkFund = notFilterHkFund;
    }

    public String getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(String isAuth) {
        this.isAuth = isAuth;
    }

    public String getNotFilterHzFund() {
        return notFilterHzFund;
    }

    public void setNotFilterHzFund(String notFilterHzFund) {
        this.notFilterHzFund = notFilterHzFund;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public List<String> getOrderStatusList() {
        return orderStatusList;
    }

    public void setOrderStatusList(List<String> orderStatusList) {
        this.orderStatusList = orderStatusList;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }


    public List<String> getmBusiCodeList() {
        return mBusiCodeList;
    }

    public void setmBusiCodeList(List<String> mBusiCodeList) {
        this.mBusiCodeList = mBusiCodeList;
    }

    public String getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(String appDateStart) {
        this.appDateStart = appDateStart;
    }

    public String getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(String appDateEnd) {
        this.appDateEnd = appDateEnd;
    }
}
