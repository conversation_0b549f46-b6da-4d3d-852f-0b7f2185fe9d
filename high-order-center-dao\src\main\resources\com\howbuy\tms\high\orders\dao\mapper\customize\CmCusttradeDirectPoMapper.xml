<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.CmCusttradeDirectPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.BaseResultMap">
    </resultMap>

    <select id="getFirstAckInfoForDirect" parameterType="map"
            resultMap="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.BaseResultMap">
        select * from (
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.Base_Column_List"/>
        from CM_CUSTTRADE_DIRECT
        where HBONENO = #{hbOneNo,jdbcType=VARCHAR}
          and FUNDCODE = #{productCode,jdbcType=VARCHAR}
          and BUSICODE in ('120', '122')
          and RECSTAT = '0'
          and ORDERSTATE in ('2', '3')
        order by TRADEDT limit 1) tt
        where 1 = 1
    </select>

    <select id="getLastAckInfoForDirect" parameterType="map"
            resultMap="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.BaseResultMap">
        select * from (
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.Base_Column_List"/>
        from CM_CUSTTRADE_DIRECT
        where HBONENO = #{hbOneNo,jdbcType=VARCHAR}
          and FUNDCODE = #{productCode,jdbcType=VARCHAR}
          and BUSICODE in ('120', '122')
          and RECSTAT = '0'
          and ORDERSTATE in ('2', '3')
        order by TRADEDT desc limit 1) tt
        where 1 = 1
    </select>

    <resultMap id="BalanceOrderMap" type="com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo">
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
        <result column="fundCode" jdbcType="VARCHAR" property="fundCode"/>
        <result column="mBusinessCode" jdbcType="VARCHAR" property="mBusinessCode"/>
        <result column="businessCode" jdbcType="VARCHAR" property="businessCode"/>
        <result column="ackVol" jdbcType="DECIMAL" property="ackVol"/>
        <result column="ackAmt" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="transferPrice" jdbcType="DECIMAL" property="transferPrice"/>
        <result column="isNoTradeTransfer" jdbcType="VARCHAR" property="isNoTradeTransfer"/>
        <result column="tradeDt" jdbcType="VARCHAR" property="tradeDt"/>
        <result column="ackDt" jdbcType="VARCHAR" property="ackDt"/>
    </resultMap>

    <select id="getOnWayDirectBalance" parameterType="com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo"
            resultMap="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.CmCusttradeDirectPoAutoMapper.Base_Column_List"/>
        from CM_CUSTTRADE_DIRECT t
        where t.busicode in ('120', '122')
          and t.prebookstate = '2'
          and t.paystate = '4'
          and t.orderstate = '1'
          and t.recstat = '0'
          and t.TRADEDT <![CDATA[ >= ]]> '20221101'
        <if test="paramVo.disCodeList != null and paramVo.disCodeList.size() > 0">
            and t.discode in
            <foreach collection="paramVo.disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="paramVo.fundCodeList != null and paramVo.fundCodeList.size() > 0">
            and(t.FUNDCODE in
            <foreach collection="paramVo.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
            or t.MJJDM in
            <foreach collection="paramVo.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
            )
        </if>
        <if test="paramVo.txAcctNo != null and paramVo.txAcctNo != ''">
            and t.txacctno = #{paramVo.txAcctNo,jdbcType=VARCHAR}
        </if>
        <if test="paramVo.hbOneNo != null and paramVo.hbOneNo != ''">
            and t.hboneno = #{paramVo.hbOneNo,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectBalanceDirectOrderVo" parameterType="map" resultMap="BalanceOrderMap">
        select t.APPSERIALNO           as orderNo,
               t.FUNDCODE              as fundCode,
               t.BUSICODE              as businessCode,
               CONCAT(t.BUSICODE, '1') as mBusinessCode,
               t.ACKAMT                as ackAmt,
               t.ACKVOL                as ackVol,
               t.TRADEDT               as ackDt,
               t.FEE                   as fee,
               t.TRANSFER_PRICE        as transferPrice,
               t.IS_NO_TRADE_TRANSFER  as isNoTradeTransfer,
               t.TRADEDT               as tradeDt
        from cm_custtrade_direct t
        where t.hboneno = #{hbOneNo,jdbcType=VARCHAR}
          and t.RECSTAT = '0'
          and t.ORDERSTATE in ('2', '3')
        <if test="fundCodeList != null and fundCodeList.size() > 0">
            and t.fundcode in
            <foreach collection="fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0">
            and t.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
    </select>
    <resultMap id="ackDealOrderMap" type="com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo">
        <result column="dealNo" jdbcType="VARCHAR" property="dealNo"/>
        <result column="fundCode" jdbcType="VARCHAR" property="fundCode"/>
        <result column="mBusinessCode" jdbcType="VARCHAR" property="mBusinessCode"/>
        <result column="ackVol" jdbcType="DECIMAL" property="ackVol"/>
        <result column="ackAmt" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="submitDt" jdbcType="VARCHAR" property="submitDt"/>
        <result column="ackDt" jdbcType="VARCHAR" property="ackDt"/>
        <result column="isHkProduct" jdbcType="VARCHAR" property="isHkProduct"/>
        <result column="balanceFactor" jdbcType="DECIMAL" property="balanceFactor"/>
        <result column="ackNavDt" jdbcType="VARCHAR" property="ackNavDt"/>
    </resultMap>
    <select id="selectAckDealDtl" resultMap="ackDealOrderMap">
        select a.APPSERIALNO           as dealNo,
               a.FUNDCODE              as fundCode,
               CONCAT(a.BUSICODE, '1') as mBusinessCode,
               a.ACKVOL                as ackVol,
               a.ACKAMT                as ackAmt,
               a.fee                   as fee,
               a.TRADEDT               as ackDt,
               a.NEW_TRADE_DT          as submitDt,
               a.is_hk_product         as isHkProduct,
               a.balance_factor        as balanceFactor,
               a.ack_nav_dt        as ackNavDt
        from cm_custtrade_direct a
        where a.ORDERSTATE in ('2', '3')
          and a.BUSICODE in ('120', '122', '12B', '13C')
          and a.HBONENO = #{hbOneNo,jdbcType = VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0">
            and a.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and a.FUNDCODE in
        <foreach collection="fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
            #{fundCode}
        </foreach>
        union all
        select a.APPSERIALNO           as dealNo,
               a.transfer_in_fund_code as fundCode,
               '1136'                  as mBusinessCode,
               a.transfer_in_ack_vol   as ackVol,
               a.transfer_in_ack_amt   as ackAmt,
               a.fee                   as fee,
               a.ack_dt                as ackDt,
               a.NEW_TRADE_DT          as submitDt,
               a.is_hk_product         as isHkProduct,
               a.transfer_in_balance_factor        as balanceFactor,
               a.transfer_in_ack_nav_dt  as ackNavDt
        from cm_custtrade_direct a
        where a.ORDERSTATE in ('2', '3')
          and a.BUSICODE = '136'
          and a.HBONENO = #{hbOneNo,jdbcType = VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0">
            and a.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and a.transfer_in_fund_code in
        <foreach collection="fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
            #{fundCode}
        </foreach>
    </select>
</mapper>