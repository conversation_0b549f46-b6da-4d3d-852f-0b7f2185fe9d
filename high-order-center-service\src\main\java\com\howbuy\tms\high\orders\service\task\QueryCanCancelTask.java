/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.common.date.DateUtil;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.CanCancelFlagEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Date;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询可撤单订单
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class QueryCanCancelTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryCanCancelTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private CountDownLatch latch;
    
    private DealOrderVo dealOrderVo;
    
    private Date currDate;
    
    public QueryCanCancelTask(QueryHighProductOuterService queryHighProductOuterService, DealOrderVo dealOrderVo,
            Date currDate, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.dealOrderVo = dealOrderVo;
        this.currDate = currDate;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            if(BusinessCodeEnum.REDEEM.getMCode().equals(dealOrderVo.getmBusiCode())){
                String cancelEndDt = dealOrderVo.getSubmitTaDt();
                if(IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(dealOrderVo.getSupportAdvanceFlag()) 
                                || IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(dealOrderVo.getSupportAdvanceFlag())){
                    ProductAppointmentInfoBean productAppointmentInfoBean = 
                            queryHighProductOuterService.queryAppointmentInfoByAppointDate(dealOrderVo.getProductCode(), "1", dealOrderVo.getFundShareClass(), dealOrderVo.getDisCode(), dealOrderVo.getAppDtm());
                    if(productAppointmentInfoBean == null){
                        dealOrderVo.setCanCancelFlag(CanCancelFlagEnum.CAN_NOT.getCode());
                        logger.error("queryHighProductOuterService is null productCode:{},busiType:{},appDtm:{}",dealOrderVo.getProductCode(),"1",dealOrderVo.getAppDtm());
                        return null;
                    }
                    
                    String apponitEndDt=  productAppointmentInfoBean.getApponitEndDt();
                    if(!StringUtils.isEmpty(apponitEndDt)){
                        cancelEndDt = apponitEndDt;
                    }
                    
                }
                //撤单截止时间
                String cancelEndDtm = new StringBuilder(cancelEndDt).append("150000").toString();
                if(currDate.compareTo(DateUtil.formatToDate(cancelEndDtm, DateUtil.YYYYMMDDHHMMSS)) < 0){
                    dealOrderVo.setCanCancelFlag(CanCancelFlagEnum.CAN.getCode()); 
                }else{
                    dealOrderVo.setCanCancelFlag(CanCancelFlagEnum.CAN_NOT.getCode());
                }
            }else{
                dealOrderVo.setCanCancelFlag(CanCancelFlagEnum.CAN.getCode());
            }
        
        }catch(RuntimeException ex){
            dealOrderVo.setCanCancelFlag(CanCancelFlagEnum.CAN_NOT.getCode());
            logger.error("queryHighProductOuterService|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

