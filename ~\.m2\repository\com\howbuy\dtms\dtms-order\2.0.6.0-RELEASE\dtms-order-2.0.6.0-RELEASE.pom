<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.dtms</groupId>
    <artifactId>dtms-order</artifactId>
    <packaging>pom</packaging>
    <version>2.0.6.0-RELEASE</version>

    <properties>
        <java.version>1.8</java.version>
        <dubbo.version>3.2.12</dubbo.version>
        <druid.version>1.2.8</druid.version>
        <log4j.version>2.15.0</log4j.version>
        <mybatis.version>2.2.2</mybatis.version>
        <zkclient.version>0.4</zkclient.version>
        <fastjson.version>1.2.80</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <commons-beanutils.version>1.8.3</commons-beanutils.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <redis.clients.version>2.9.3</redis.clients.version>
        <powermock.version>2.0.2</powermock.version>
        
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-rocket.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-message-amq.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>
        <com.howbuy.param-server-facade.version>20250811-RELEASE</com.howbuy.param-server-facade.version>
        <com.howbuy.howbuy_dfile.version>1.18.1-RELEASE</com.howbuy.howbuy_dfile.version>
        <com.howbuy.util.version>1.0.0-SNAPSHOT</com.howbuy.util.version>
        <com.howbuy.easyexcel.version>3.0.5</com.howbuy.easyexcel.version>
        <com.howbuy.dtms-order.version>2.0.6.0-RELEASE</com.howbuy.dtms-order.version>
        <com.howbuy.dtms-settle.version>1.9.4.0-RELEASE</com.howbuy.dtms-settle.version>
        <com.howbuy.howbuy-trace.version>1.0.5-RELEASE</com.howbuy.howbuy-trace.version>
        <com.howbuy.dtms-common.version>bugfix-20250724-RELEASE</com.howbuy.dtms-common.version>
        <com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
        <com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.crm-trade-api.version>1.9.2.4-RELEASE</com.howbuy.crm-trade-api.version>
        <com.howbuy.crm-nt-client.version>2.0.5.7-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.dtms-common-service.version>1.7.5.0-RELEASE</com.howbuy.dtms-common-service.version>
        <com.howbuy.dtms-common-datasource.version>bugfix-20250724-RELEASE</com.howbuy.dtms-common-datasource.version>
        <com.howbuy-boot-actuator>2.1.0-RELEASE</com.howbuy-boot-actuator>
        <com.howbuy.howbuy-boot-actuator-dubbo3.version>2.3.0-RELEASE</com.howbuy.howbuy-boot-actuator-dubbo3.version>
        <com.howbuy.tms.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms.tms-common-log-pattern.version>
        <com.howbuy.hk-acc-online-facade.version>20250808-RELEASE</com.howbuy.hk-acc-online-facade.version>
        <com.howbuy.howbuy-simu-client.version>release-20250811-jy-2.8-zcxqy-RELEASE</com.howbuy.howbuy-simu-client.version>
        <com.howbuy.crm-core-client.version>2.0.0.7-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.message-public-client.version>5.1.17-RELEASE</com.howbuy.message-public-client.version>
        <com.howbuy.acc-center-facade.version>3.6.6-RELEASE</com.howbuy.acc-center-facade.version>
        <com.howbuy.dtms-product.version>1.9.4.0-RELEASE</com.howbuy.dtms-product.version>
        <com.howbuy.hk-fin-online-facade.version>1.0.0-RELEASE</com.howbuy.hk-fin-online-facade.version>
        <com.howbuy.product-center-client.version>20250821-001-RELEASE</com.howbuy.product-center-client.version>
        <com.howbuy.dtms-common-enums.version>bugfix-20250724-RELEASE</com.howbuy.dtms-common-enums.version>
        <com.howbuy.hk-fin-facade.version>20250815-RELEASE</com.howbuy.hk-fin-facade.version>
        <com.howbuy.dtms-product-client.version>1.9.4.0-RELEASE</com.howbuy.dtms-product-client.version>
        <com.howbuy.dtms-settle-client.version>20250718-RELEASE</com.howbuy.dtms-settle-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>hk-fin-facade</artifactId>
                <version>${com.howbuy.hk-fin-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-core-client</artifactId>
                <version>${com.howbuy.crm-core-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <artifactId>dtms-product-client</artifactId>
                <groupId>com.howbuy.dtms</groupId>
                <version>${com.howbuy.dtms-product-client.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-simu-client</artifactId>
                <version>${com.howbuy.howbuy-simu-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.acccenter</groupId>
                <artifactId>acc-center-facade</artifactId>
                <version>${com.howbuy.acc-center-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.cc.message</groupId>
                <artifactId>message-public-client</artifactId>
                <version>${com.howbuy.message-public-client.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-auth-facade</artifactId>
                <version>${com.howbuy.howbuy-auth-facade.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.interlayer</groupId>
                <artifactId>product-center-client</artifactId>
                <version>${com.howbuy.product-center-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-webdav</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-service</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-local</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-nfs</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-trade-api</artifactId>
                <version>${com.howbuy.crm-trade-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>pagehelper-spring-boot-starter</artifactId>
                        <groupId>com.github.pagehelper</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>pagehelper-spring-boot-autoconfigure</artifactId>
                        <groupId>com.github.pagehelper</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring-boot-starter</artifactId>
                        <groupId>org.mybatis.spring.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-nt-client</artifactId>
                <version>${com.howbuy.crm-nt-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>httpclient</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>joda-time</artifactId>
                        <groupId>joda-time</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>disruptor</artifactId>
                        <groupId>com.lmax</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.mail</groupId>
                        <artifactId>mail</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-common-datasource</artifactId>
                <version>${com.howbuy.dtms-common-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${com.howbuy.easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy-boot-actuator}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator-dubbo3</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator-dubbo3.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-support-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>${com.howbuy.howbuy-message-amq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.3.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-order-dao</artifactId>
                <version>${com.howbuy.dtms-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-order-service</artifactId>
                <version>${com.howbuy.dtms-order.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-common-service</artifactId>
                <version>${com.howbuy.dtms-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-common-enums</artifactId>
                <version>${com.howbuy.dtms-common-enums.version}</version>
            </dependency>
            <dependency>
                <artifactId>howbuy-commons-validator</artifactId>
                <groupId>com.howbuy.commons.validator</groupId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-order-client</artifactId>
                <version>${com.howbuy.dtms-order.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.oval</groupId>
                <artifactId>oval</artifactId>
                <version>1.84</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-td-client</artifactId>
                <version>${com.howbuy.crm-td-client.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms.tms-common-log-pattern.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.hkacconline</groupId>
                <artifactId>hk-acc-online-facade</artifactId>
                <version>${com.howbuy.hk-acc-online-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.sharding</groupId>
                <artifactId>howbuy-sharding-id</artifactId>
                <version>${com.howbuy.howbuy-sharding-id.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>param-server-facade</artifactId>
                <version>${com.howbuy.param-server-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-settle-client</artifactId>
                <version>${com.howbuy.dtms-settle-client.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-trace</artifactId>
                <version>${com.howbuy.howbuy-trace.version}</version>
            </dependency>

            
            
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-testng</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4-rule-agent</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>