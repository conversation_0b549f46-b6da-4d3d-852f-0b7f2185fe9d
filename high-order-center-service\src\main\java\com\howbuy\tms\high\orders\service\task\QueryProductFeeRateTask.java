/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:(查询高端产品费率)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月5日 下午2:29:31
 * @since JDK 1.6
 */
public class QueryProductFeeRateTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryProductFeeRateTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private HighProductFeeRateBean highProductFeeRateBean;
    
    private BigDecimal appAmt;

    private CountDownLatch latch;

    public QueryProductFeeRateTask(QueryHighProductOuterService queryHighProductOuterService, HighProductFeeRateBean highProductFeeRateBean, 
            BigDecimal appAmt, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.highProductFeeRateBean = highProductFeeRateBean;
        this.appAmt = appAmt;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            if(highProductFeeRateBean == null){
                logger.error("QueryProductInfoTask|highProductFeeRateBean|is null");
                return null;
            }
            HighProductFeeRateBean bean = queryHighProductOuterService.getFundFeeRateByAmt(highProductFeeRateBean.getFundCode(), highProductFeeRateBean.getBusiCode(), highProductFeeRateBean.getInvstType(), highProductFeeRateBean.getShareClass(), appAmt);
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, highProductFeeRateBean);
        }catch(RuntimeException ex){
            logger.error("QueryProductInfoTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

