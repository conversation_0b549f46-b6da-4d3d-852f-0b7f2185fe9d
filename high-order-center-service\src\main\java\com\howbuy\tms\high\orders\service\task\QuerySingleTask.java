/**
 *Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryacchboneInfo.QueryAccHboneInfoResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
/**
 * 
 * @description:(根据一帐通号一帐通信息)
 * @reason:
 * <AUTHOR>
 * @date 2018年1月9日 下午1:49:53
 * @since JDK 1.6
 */
public class QuerySingleTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QuerySingleTask.class);

    private QueryAccHboneInfoOuterService queryAccHboneInfoOuterService;
    
    private QueryAccHboneInfoResult queryAccHboneInfoResult;

    private CountDownLatch latch;

    public QuerySingleTask(QueryAccHboneInfoOuterService queryAccHboneInfoService, QueryAccHboneInfoResult queryAccHboneResult, CountDownLatch latch) {
        this.queryAccHboneInfoOuterService = queryAccHboneInfoService;
        this.queryAccHboneInfoResult = queryAccHboneResult;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            QueryAccHboneInfoResult bean = queryAccHboneInfoOuterService.queryAccHboneInfo(queryAccHboneInfoResult.getHboneNo());
            if (bean == null) {
                return null;
            }
            
            BeanUtils.copyProperties(bean, queryAccHboneInfoResult);
            
        }catch(RuntimeException ex){
            logger.error("QuerySingleTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

