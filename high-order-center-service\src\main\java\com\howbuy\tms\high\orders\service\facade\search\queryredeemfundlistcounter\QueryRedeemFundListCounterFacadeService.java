/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.queryredeemfundlistcounter;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querybankacctsensitiveinfo.QueryBankAcctSensitiveInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerRedeemAppointInfo.CustomerRedeemAppointInfo;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterResponse.RedeemListCounterBean;
import com.howbuy.tms.high.orders.service.task.QueryCustBankCardSensitiveTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(查询子账本明细)
 * @reason:
 * @date 2018年4月12日 下午3:40:47
 * @since JDK 1.6
 */
@DubboService
@Service("queryRedeemFundListCounterFacade")
public class QueryRedeemFundListCounterFacadeService implements QueryRedeemFundListCounterFacade {

    private static final Logger logger = LogManager.getLogger(QueryRedeemFundListCounterFacadeService.class);

    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private QueryBankAcctSensitiveInfoOuterService queryBankAcctSensitiveInfoOuterService;
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;
    @Autowired
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;

    private static final String UN_LINE = "_";

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryredeemfundlistcounter.QueryRedeemFundListCounterFacade.execute(QueryRedeemFundListCounterRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryRedeemFundListCounterFacadeService
     * @apiName execute
     * @apiDescription 查询子账本明细
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} fundShareClass 产品份额类型，A-前收费；B-后收费
     * @apiParam (请求参数) {String} protocolNo 协议号
     * @apiParam (请求参数) {String} protocolType 4-高端协议类型
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * fundShareClass=kGqjh0V2mM&hbOneNo=Ok&pageSize=8392&protocolType=YGzIM&disCode=ilHCKucS2&txChannel=2NoTDjXmk&appTm=pZjbFzj1&productCode=7k&disCodeList=M4LAB&subOutletCode=mGXvIM0B70&pageNo=5063&operIp=THCs3d5B&protocolNo=KFPNUx&txAcctNo=I8mZUDQ&cpAcctNo=WY0OdzVWpe&appDt=PHZ47QBqF&dataTrack=AsY3Q7jc&txCode=RgVs8ZB0&outletCode=FEH3Uo
     * @apiSuccess (响应结果) {Array} balanceList
     * @apiSuccess (响应结果) {String} balanceList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} balanceList.disCode 分销机构号
     * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceList.fundShareClass 份额类型
     * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
     * @apiSuccess (响应结果) {String} balanceList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} balanceList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} balanceList.bankName 银行名称
     * @apiSuccess (响应结果) {String} balanceList.bankAcctNo 银行卡号
     * @apiSuccess (响应结果) {Number} balanceList.balanceVol 总份额
     * @apiSuccess (响应结果) {Number} balanceList.availVol 可用份额
     * @apiSuccess (响应结果) {Number} balanceList.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} balanceList.LockVol 锁定份额
     * @apiSuccess (响应结果) {String} balanceList.productChannel 产品通道  3-群济私募 5-好买公募 6-高端公募
     * @apiSuccess (响应结果) {Number} balanceList.subscribeAmt 认缴金额
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"yAaB","totalPage":3031,"pageNo":3927,"description":"NoggCwBD","balanceList":[{"LockVol":3154.*************,"bankCode":"B5P2I7","fundShareClass":"JEuSlXc","bankAcctNo":"V","productChannel":"K84D6ta9","balanceVol":9591.************,"bankName":"gKteRlYlO","disCode":"B2","productName":"l","productCode":"g9k0E0Wt","unconfirmedVol":628.*************,"availVol":6088.************,"subscribeAmt":6501.************,"txAcctNo":"f5K","cpAcctNo":"merVe"}],"totalCount":6646}
     */
    @Override
    public QueryRedeemFundListCounterResponse execute(QueryRedeemFundListCounterRequest request) {
        String txAcctNo = request.getTxAcctNo();
        List<String> disCodeList = getDisCodeList(request);
        String productCode = request.getProductCode();
        String cpAcctNo = request.getCpAcctNo();
        String outletCode = request.getOutletCode();
        QueryRedeemFundListCounterResponse response = new QueryRedeemFundListCounterResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));

        // 查询子账本表(开放赎回日期维度)
        List<SubCustBooksPo> booklist = subCustBooksRepository.selectSubCustBooksByOpenRedeDt(disCodeList, txAcctNo, productCode, cpAcctNo, null);
        logger.info("SubCustBooksPo booklist:{}", JSON.toJSONString(booklist));

        //查询专户持仓
        List<SubCustBooksPo> zhBalanceList = subCustBooksRepository.selectZhBalanceDtlWithOutSubBook(disCodeList, txAcctNo, productCode, cpAcctNo);
        logger.info("BalanceVo zhBalanceList{}", JSON.toJSONString(zhBalanceList));

        // 私募（tp,qj）查询账本明细冻结
        List<CustBooksPo> unConfirmVolList = custBooksRepository.selectUnconfirmedVolByCpAcctNo(disCodeList, txAcctNo, productCode, cpAcctNo);
        logger.info("QueryRedeemFundListCounterFacadeService|SubCustBooksPo unConfirmVolList:{}", JSON.toJSONString(unConfirmVolList));

        if (CollectionUtils.isEmpty(booklist)) {
            booklist = new ArrayList<>();
        }
        // 排除有子账本的专户持仓
        if (!CollectionUtils.isEmpty(zhBalanceList)) {
            booklist.addAll(zhBalanceList);
        }
        if (CollectionUtils.isEmpty(booklist)) {
            return response;
        }
        Map<String, BigDecimal> unConfirmVolMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(unConfirmVolList)) {
            for (CustBooksPo po : unConfirmVolList) {
                unConfirmVolMap.put(po.getProductCode() + "_" + po.getCpAcctNo(), po.getUnconfirmedVol());
            }
        }
        // 构造银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = buildBankInfo(booklist, txAcctNo, disCodeList.get(0), outletCode);

        // TA交易日
        final String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());

        Map<String, HighProductBaseInfoBean> productMap = new HashMap<>();
        Map<String, RedeemListCounterBean> map = new HashMap<>();
        String appDt = request.getAppDt();
        String appTm = request.getAppTm();
        List<String> fundCodeList = booklist.stream().map(SubCustBooksPo::getFundCode).distinct().collect(Collectors.toList());
        List<SubscribeAmtDetailPo> subscribeAmtDetailList = subscribeAmtDetailRepository.getSubscribeAmtDetail(txAcctNo, fundCodeList);
        Map<String, SubscribeAmtDetailPo> subscribeAmtDetailPoMap = subscribeAmtDetailList.stream().collect(Collectors.toMap(x -> Joiner.on(UN_LINE).join(x.getFundCode(), x.getTxAcctNo()), x -> x));
        // 设置赎回持仓信息
        setRedeemBalanceVolInfo(request, txAcctNo, disCodeList, booklist, unConfirmVolMap, bankCardMap, taTradeDt, productMap, map, appDt, appTm, subscribeAmtDetailPoMap);
        List<RedeemListCounterBean> balanceDtlList = new ArrayList<>(map.values());
        response.setBalanceList(balanceDtlList);
        return response;

    }

    /**
     * 设置赎回持仓信息
     */
    private void setRedeemBalanceVolInfo(QueryRedeemFundListCounterRequest request, String txAcctNo, List<String> disCodeList, List<SubCustBooksPo> booklist, Map<String, BigDecimal> unConfirmVolMap, Map<String, QueryCustBankCardResult> bankCardMap, String taTradeDt, Map<String, HighProductBaseInfoBean> productMap, Map<String, RedeemListCounterBean> map, String appDt, String appTm, Map<String, SubscribeAmtDetailPo> subscribeAmtDetailPoMap) {
        for (SubCustBooksPo book : booklist) {
            HighProductBaseInfoBean highProductBaseBean = productMap.get(book.getFundCode());
            if (highProductBaseBean == null) {
                highProductBaseBean = queryHighProductOuterService.getHighProductBaseInfo(book.getFundCode());
                if (highProductBaseBean == null) {
                    logger.error("SellController|highProductBaseBean:{} not exit", book.getFundCode());
                    break;
                }
                productMap.put(book.getFundCode(), highProductBaseBean);
            }
            String key = book.getFundCode() + "_" + book.getCpAcctNo();
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            BigDecimal unConfirmVol = unConfirmVolMap.get(key);
            if (unConfirmVol == null) {
                unConfirmVol = book.getFrznVol();
            }
            // 如果是循环锁定的,用新逻辑
            BigDecimal lockVol = BigDecimal.ZERO;
            if (YesOrNoEnum.YES.getCode().equals(highProductBaseBean.getIsCyclicLock())) {
                CustomerRedeemAppointInfo appointInfoBySubCustBooks = queryCustomerRedeemAppointInfoLogicService.getCustomerRedeemAppointInfoBySubCustBooks(book, taTradeDt);
                lockVol = appointInfoBySubCustBooks.getLockVol();
            } else {
                String currDay = getCurrDay(book.getFundCode(), appDt, appTm, disCodeList.get(0), taTradeDt);
                lockVol = getLockVol(book.getFundCode(), highProductBaseBean.getHasLockPeriod(), currDay, book.getOpenRedeDt(), book.getBalanceVol());
            }
            book.setProductName(highProductBaseBean.getFundAttr());
            SubscribeAmtDetailPo subscribeAmtDetailPo = subscribeAmtDetailPoMap.get(Joiner.on(UN_LINE).join(book.getFundCode(), txAcctNo));
            // 数据组织
            buildBalance(book, map, disCodeList.get(0), unConfirmVol, lockVol, txAcctNo, result, key, highProductBaseBean, subscribeAmtDetailPo);
        }
    }

    private List<String> getDisCodeList(QueryRedeemFundListCounterRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

    private void buildBalance(SubCustBooksPo book, Map<String, RedeemListCounterBean> map, String disCode, BigDecimal unConfirmVol, BigDecimal lockVol, String txAcctNo,
                              QueryCustBankCardResult result, String key, HighProductBaseInfoBean highProductBaseBean, SubscribeAmtDetailPo subscribeAmtDetailPo) {
        RedeemListCounterBean bean = map.get(key);
        if (bean == null) {
            if (unConfirmVol == null) {
                unConfirmVol = new BigDecimal(0);
            }
            bean = new RedeemListCounterBean();
            bean.setTxAcctNo(txAcctNo);
            bean.setDisCode(disCode);
            bean.setProductCode(book.getFundCode());
            bean.setProductName(book.getProductName());
            bean.setCpAcctNo(book.getCpAcctNo());
            bean.setBalanceVol(book.getBalanceVol());
            logger.info("getBalanceVol:{},getFrznVol:{},getJustFrznVol:{},lockVol:{},unConfirmVol:{}", book.getBalanceVol(), book.getFrznVol(), book.getJustFrznVol(), lockVol, unConfirmVol);
            bean.setAvailVol(book.getBalanceVol().subtract(book.getJustFrznVol()).subtract(lockVol).subtract(unConfirmVol));
            bean.setUnconfirmedVol(unConfirmVol);
            bean.setLockVol(lockVol);
            bean.setBankCode(result.getBankCode());
            bean.setBankName(result.getBankRegionName());
            bean.setBankAcctNo(result.getBankAcct());
            bean.setProductChannel(highProductBaseBean.getProductChannel());
            map.put(key, bean);
        } else {
            bean.setBalanceVol(bean.getBalanceVol().add(book.getBalanceVol()));
            bean.setAvailVol(bean.getAvailVol().add(book.getBalanceVol().subtract(book.getFrznVol()).subtract(book.getJustFrznVol()).subtract(lockVol)));
            bean.setLockVol(bean.getLockVol().add(lockVol));
        }
        if (subscribeAmtDetailPo != null) {
            bean.setSubscribeAmt(subscribeAmtDetailPo.getSubscribeAmt());
        }
    }

    private Map<String, QueryCustBankCardResult> buildBankInfo(List<SubCustBooksPo> booklist, String txAcctNo, String disCode, String outletCode) {
        // 资金账号set
        Set<String> cpAcctNoSet = new HashSet<>();
        // 产品代码set
        Set<String> fundCodeSet = new HashSet<>();
        for (SubCustBooksPo book : booklist) {
            cpAcctNoSet.add(book.getCpAcctNo());
            fundCodeSet.add(book.getFundCode());
        }

        // 获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardNoMap = new HashMap<String, QueryCustBankCardResult>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardNoMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardSensitiveTask(queryCustBankCardOuterService, queryBankAcctSensitiveInfoOuterService,
                    latch, txAcctNo, disCode, cpAcct, bankCardInfo, outletCode));
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryRedeemFundListCounterFacadeService|latch|", e);
            Thread.currentThread().interrupt();
        }
        return bankCardNoMap;
    }

    /**
     * getCurrDay:(获取当前赎回日期)
     *
     * @param productCode
     * @param appDt
     * @param appTm
     * @param disCode
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2017年11月6日 下午9:07:05
     */
    private String getCurrDay(String productCode, String appDt, String appTm, String disCode, String workDay) {
        logger.info("getCurrDay|productCode:{}, appDt:{}, appTm:{} , disCode:{}", productCode, appDt, appTm, disCode, workDay);

        if (StringUtils.isEmpty(appDt)) {
            appDt = workDay;
        }

        if (StringUtils.isEmpty(appTm)) {
            appTm = "145959";
        }
        logger.info("actual|getCurrDay|productCode:{}, appDt:{}, appTm:{} , disCode:{}", productCode, appDt, appTm, disCode);
        HighProductBaseInfoBean highProductBaseModel = queryHighProductOuterService.getHighProductBaseInfo(productCode);
        if (highProductBaseModel != null && supportAdvanceRedeem(highProductBaseModel.getIsScheduledTrade())) {
            if (StringUtils.isEmpty(appDt)) {
                appDt = workDay;
            }
            if (StringUtils.isEmpty(appTm)) {
                appTm = "143000";
            }
            logger.info("getCurrDay|actual productCode:{},  appDt:{}, appTm:{}", productCode, appDt, appTm);
            String appDtmStr = new StringBuilder().append(appDt).append(appTm).toString();
            Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
            ProductAppointmentInfoBean productAppointmentInfoModel = queryHighProductOuterService.queryAppointmentInfoByAppointDate(productCode, "1", highProductBaseModel.getShareClass(), disCode, appDtm);
            if (productAppointmentInfoModel != null && workDay.compareTo(productAppointmentInfoModel.getOpenStartDt()) < 0) {
                return productAppointmentInfoModel.getOpenStartDt();
            }
        }
        return workDay;
    }

    private boolean supportAdvanceRedeem(String supportAdvanceFlag) {
        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag) || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
            return true;
        }
        return false;
    }

    private BigDecimal getLockVol(String productCode, String hasLockPeriod, String currDay, String openRedDt, BigDecimal availVol) {
        logger.info("SellController|getLockVol|productCode:{}, currDay:{}, openRedDt:{}, availVol:{}", productCode, currDay, openRedDt, availVol);
        if (StringUtils.isEmpty(hasLockPeriod)) {
            logger.error("SellController|getLockVol|hasLockPeriod:{} not exit", productCode);
            return BigDecimal.ZERO;
        }
        if ((RedeemTypeEnum.YES.getCode().equals(hasLockPeriod)) && (openRedDt != null && currDay.compareTo(openRedDt) < 0)) {
            return availVol;
        }

        return BigDecimal.ZERO;
    }

}

