//package com.howbuy.tms.high.orders.service.service.dubboforward;
//
//import com.howbuy.ftxcommon.model.constants.enums.YesOrNoEnum;
//import com.howbuy.tms.common.client.BaseRequest;
//import com.howbuy.tms.common.utils.DateUtils;
//import com.howbuy.tms.common.utils.StringUtils;
//import com.howbuy.tms.high.orders.service.config.ThreadPoolConfig;
//import com.howbuy.trace.thread.ThreadTraceHelper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.ReferenceConfig;
//import org.apache.dubbo.config.bootstrap.DubboBootstrap;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.core.type.classreading.MetadataReader;
//import org.springframework.core.type.classreading.SimpleMetadataReaderFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.io.IOException;
//import java.lang.reflect.Method;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Date;
//import java.util.List;
//import java.util.Random;
//import java.util.concurrent.ExecutorService;
//
///**
// * com.howbuy.tms.high.orders.facade.search这个包下的dubbo请求，处理完成后，会转发给相同duboo接口，只是版本号为2.0
// */
//@Component
//@Slf4j
//@RefreshScope
//public class DubboForwardService {
//
//    // 是否转发 1：转发
//    @Value("${dubboForward.flag}")
//    private String flag;
//
//    @Value("${dubboForward.probability}")
//    private int probability;
//
//    @Value("${dubboForward.timeInterval}")
//    private String timeInterval;
//
//    @Value("${dubboForward.version}")
//    private String version;
//
//    private ExecutorService executor = ThreadPoolConfig.createThreadPool("DubboForwardAspect",10,50);
//
//    private static List<Class<?>> interfaces = new ArrayList<>();
//
//    @PostConstruct
//    public void init() throws Exception {
//        if(YesOrNoEnum.YES.getCode().equals(flag)) {
//            // 接口加载
//            loadInterfaces();
//            // dubbo 消费者配置
//            dubboReferenceCfg(version);
//        }
//    }
//
//    public void afterAdvice(JoinPoint joinPoint)  {
//        if(YesOrNoEnum.YES.getCode().equals(flag)) {
//            executor.execute(ThreadTraceHelper.decorate(() -> {
//                try {
//                    Object target = joinPoint.getTarget();
//                    String interfaceName = inferInterfaceName(target);
//                    if(interfaceName.startsWith("com.howbuy.tms.high.orders.facade.search")){
//                        // 校验概率
//                        if(!hitByProbability(probability)){
//                            return;
//                        }
//                        // 校验时间
//                        if(!checkTime()){
//                            return;
//                        }
//                        // 获取方法签名
//                        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
//                        String methodName = signature.getName();
//                        // 获取参数类型
////                    Class<?>[] parameterTypes = signature.getMethod().getParameterTypes();
//                        // 获取方法名和参数
//                        Object[] args = joinPoint.getArgs();
//                        if(interfaceName.endsWith("QueryBuyFundStatusFacade")){
//                            return;
//                        }
//                        log.info("interfaceName：{}转发dubbo2.0",interfaceName);
//                        Object serviceProxy = DubboBootstrap.getInstance().getCache().get(interfaceName + ":" + version);
//
//                        Class<?> serviceClass = Class.forName(interfaceName);
//                        // 获取Method对象
//                        Method method = serviceClass.getMethod(methodName, BaseRequest.class);
//                        // 调用方法
//                        Object result = method.invoke(serviceProxy, args);
//                    }else {
//                        log.info("interfaceName：{}不转发dubbo2.0",interfaceName);
//                    }
//                } catch (Exception e) {
//                    log.error("系统异常", e);
//                }
//            }));
//        }
//    }
//
//    public boolean checkTime(){
//        try {
//            if (StringUtils.isNotEmpty(timeInterval)) {
//                String currentTime = DateUtils.formatToString(new Date(), DateUtils.HHMMSS);
//                List<String> timeIntervals = Arrays.asList(timeInterval.split(","));
//                for (String timeInterval : timeIntervals) {
//                    String startTime = timeInterval.split("-")[0];
//                    String endTime = timeInterval.split("-")[1];
//                    if (isInTimeRange(currentTime, startTime, endTime)) {
//                        log.error("时间不满足currentTime：{}，startTime：{}，endTime：{}，不转发", currentTime, startTime, endTime);
//                        return false;
//                    }
//                }
//            }
//        }catch (Exception e){
//            log.error("checkTime系统异常",e);
//        }
//        return true;
//    }
//
//    public void dubboReferenceCfg(String version) {
//        interfaces.stream().forEach(item -> {
//            ReferenceConfig mysqlRef = new ReferenceConfig<>();
//            mysqlRef.setId("mysql" + item.getTypeName());
//            mysqlRef.setInterface(item);
//            mysqlRef.setVersion(version);
//            mysqlRef.setTimeout(20000);
//            mysqlRef.setRetries(0);
//            mysqlRef.setCheck(false);
//
//            DubboBootstrap.getInstance().reference(mysqlRef);
//        });
//    }
//
//    void loadInterfaces() throws Exception {
//        interfaces.addAll(scanInterfaces("com.howbuy.tms.high.orders.facade.search"));
//    }
//
//    public List<Class<?>> scanInterfaces(String basePackage) throws IOException, ClassNotFoundException {
//        List<Class<?>> interfaceClasses = new ArrayList<>();
//
//        // 创建一个用于读取类信息的MetadataReaderFactory
//        SimpleMetadataReaderFactory metadataReaderFactory = new SimpleMetadataReaderFactory();
//
//        // 创建一个用于加载类资源的PathMatchingResourcePatternResolver
//        PathMatchingResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
//
//        // 根据指定的包名，获取匹配的类资源
//        String packageSearchPath = "classpath*:" + basePackage.replace(".", "/") + "/**/*.class";
//        org.springframework.core.io.Resource[] resources = resourcePatternResolver.getResources(packageSearchPath);
//
//        // 遍历每个类资源
//        for (org.springframework.core.io.Resource resource : resources) {
//            // 获取类的元数据信息
//            MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(resource);
//
//            // 判断该类是否是接口类型
//            if (metadataReader.getClassMetadata().isInterface()) {
//                // 获取接口的Class对象，并添加到结果列表中
//                String className = metadataReader.getClassMetadata().getClassName();
//                Class<?> interfaceClass = Class.forName(className);
//                interfaceClasses.add(interfaceClass);
//            }
//        }
//
//        return interfaceClasses;
//    }
//
//    private String inferInterfaceName(Object target) {
//        // 这里需要根据实际情况来实现接口名的推断逻辑
//        // 例如，可以通过反射检查目标对象实现的接口
//        for (Class<?> iface : target.getClass().getInterfaces()) {
//            if (iface.getSimpleName().endsWith("Facade")) { // 假设接口名以"I"开头
//                return iface.getName();
//            }
//        }
//        return "Unknown Interface";
//    }
//
//
//
//    public static boolean hitByProbability(int probability) {
//        boolean flag = false;
//        try {
//            if (probability < 0 || probability > 100) {
//                throw new IllegalArgumentException("概率值必须在0到100之间");
//            }
//            Random random = new Random();
//            int randomNumberInRange = random.nextInt(100) + 1;
//            flag = randomNumberInRange <= probability;
//            if(!flag){
//                log.error("概率不转发，random:{}，probability:{}", randomNumberInRange, probability);
//            }
//        }catch (Exception e){
//            log.error("hitByProbability系统异常",e);
//            flag = false;
//        }
//        return flag;
//    }
//
//    public static boolean isInTimeRange(String currentTime, String startTime, String endTime) {
//        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
//        try {
//            Date now = sdf.parse(currentTime);
//            Date start = sdf.parse(startTime);
//            Date end = sdf.parse(endTime);
//
//            return now.after(start) && now.before(end);
//        } catch (ParseException e) {
//            log.error("系统异常",e);
//            return false;
//        }
//    }
//}
