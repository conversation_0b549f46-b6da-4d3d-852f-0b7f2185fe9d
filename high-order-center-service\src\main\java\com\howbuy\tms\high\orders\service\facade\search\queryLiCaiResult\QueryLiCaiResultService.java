package com.howbuy.tms.high.orders.service.facade.search.queryLiCaiResult;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.UserShowResultInfoPo;
import com.howbuy.tms.high.orders.facade.search.queryLiCaiResult.QueryLiCaiResultFacade;
import com.howbuy.tms.high.orders.facade.search.queryLiCaiResult.QueryLiCaiResultRequest;
import com.howbuy.tms.high.orders.facade.search.queryLiCaiResult.QueryLiCaiResultResponse;
import com.howbuy.tms.high.orders.service.business.query.QueryNeedShowLiCaiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @Description:查询是否展示理财分析结果
 * @Author: yun.lu
 * Date: 2024/12/9 14:48
 */
@DubboService
@Service("queryLiCaiResultFacade")
@Slf4j
@RefreshScope
public class QueryLiCaiResultService implements QueryLiCaiResultFacade {
    @Autowired
    private QueryNeedShowLiCaiServiceImpl queryNeedShowLiCaiService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryLiCaiResult.QueryLiCaiResultFacade.execute(QueryLiCaiResultRequest queryLiCaiResultRequest)
     * @apiVersion 1.0.0
     * @apiGroup QueryLiCaiResultService
     * @apiName execute
     * @apiDescription 查询是否展示理财分析结果
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=Sv2zz&pageSize=6492&disCode=lIxwmT8i&txChannel=m&appTm=N&subOutletCode=QcptXW1Qaf&pageNo=356&operIp=1&txAcctNo=jVffc&appDt=ioi1SMPjr&dataTrack=2&txCode=rYefsbNVDv&outletCode=A9uLx7j51y
     * @apiSuccess (响应结果) {String} showLiCai 是否展示理财分析,1:展示,0:不展示
     * @apiSuccess (响应结果) {String} showLiCaiReason 是否阐释理财分析原因
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccess (响应结果) {String} txId txId
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"teqEui7","showLiCaiReason":"WzE","totalPage":6253,"pageNo":5821,"description":"IQ","txId":"4nUCtD","totalCount":9130,"showLiCai":"9"}
     */
    @Override
    public QueryLiCaiResultResponse execute(QueryLiCaiResultRequest queryLiCaiResultRequest) {
        log.info("查询是否展示理财分析结果,txAcctNo={},hbOneNo={}", queryLiCaiResultRequest.getTxAcctNo(), queryLiCaiResultRequest.getHbOneNo());
        QueryLiCaiResultResponse resp = new QueryLiCaiResultResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        if (StringUtils.isBlank(queryLiCaiResultRequest.getHbOneNo()) && StringUtils.isBlank(queryLiCaiResultRequest.getTxAcctNo())) {
            resp.setDescription("交易账号与一账通都为空");
            return resp;
        }
        // 查询是否需要理财分析
        UserShowResultInfoPo needShowLiCai = queryNeedShowLiCaiService.getNeedShowLiCai(queryLiCaiResultRequest.getHbOneNo(), queryLiCaiResultRequest.getTxAcctNo());
        resp.setShowLiCai(needShowLiCai.getShowLiCai());
        resp.setShowLiCaiReason(needShowLiCai.getShowLiCaiReason());
        return resp;
    }


}
