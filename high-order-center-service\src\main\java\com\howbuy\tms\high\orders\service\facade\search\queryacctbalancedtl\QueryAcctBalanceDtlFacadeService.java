/**
 * Copyright (c) 2017, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancedtl;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.interlayer.product.enums.SupportAdvanceFlagEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.threadpool.CommonThreadPool;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlResponse.BalanceDtlBean;
import com.howbuy.tms.high.orders.service.task.QueryCustBankCardTask;
import com.howbuy.tms.high.orders.service.cacheservice.querytatradedt.QueryTaTradeDtCacheService;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询产品持仓明细接口实现
 *
 * <AUTHOR>
 * @reason:T
 * @date 2017年4月12日 下午7:26:11
 * @since JDK 1.7
 */
@DubboService
@Service("queryAcctBalanceDtlFacade")
public class QueryAcctBalanceDtlFacadeService implements QueryAcctBalanceDtlFacade {

    private static final Logger logger = LogManager.getLogger(QueryAcctBalanceDtlFacadeService.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTaTradeDtCacheService queryTaTradeDtCacheService;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;


    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctBalanceDtlFacadeService
     * @apiName execute
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} fundShareClass 产品份额类型，A-前收费；B-后收费
     * @apiParam (请求参数) {String} protocolNo 协议号
     * @apiParam (请求参数) {String} protocolType 4-高端协议类型
     * @apiParam (请求参数) {String} cpAcctNo 资金账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * fundShareClass=qqHqBZd9&hbOneNo=76&pageSize=7615&protocolType=g4EV&disCode=4YcyFTgn77&txChannel=pkfUR&appTm=ucRx&productCode=lT&disCodeList=saq&subOutletCode=LiZE1DkpNW&pageNo=8021&operIp=Ai1&protocolNo=9n&txAcctNo=RUpnqw&cpAcctNo=8RAjGhG&appDt=K&dataTrack=72u&txCode=idwJd&outletCode=3Sy3Ax
     * @apiSuccess (响应结果) {Array} balanceDtlList
     * @apiSuccess (响应结果) {String} balanceDtlList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} balanceDtlList.disCode 分销机构号
     * @apiSuccess (响应结果) {String} balanceDtlList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceDtlList.fundShareClass 份额类型
     * @apiSuccess (响应结果) {String} balanceDtlList.productName 产品名称
     * @apiSuccess (响应结果) {String} balanceDtlList.productType 产品类型
     * @apiSuccess (响应结果) {Number} balanceDtlList.nav 净值
     * @apiSuccess (响应结果) {String} balanceDtlList.productStatus 产品状态：0-交易； 1-发行； 2-发行成功； 3-发行失败； 4-停止交易； 5-停止申购； 6-停止赎回； 7-权益登记； 8-红利发放；          9-基金封闭； a-基金终止
     * @apiSuccess (响应结果) {String} balanceDtlList.navDt 净值日期
     * @apiSuccess (响应结果) {String} balanceDtlList.buyStatus 购买状态：1-认购，2-申购，3-不可购买
     * @apiSuccess (响应结果) {String} balanceDtlList.redeemStatus 赎回状态 1-可赎回，2-不可赎回
     * @apiSuccess (响应结果) {String} balanceDtlList.protocolNo 协议号
     * @apiSuccess (响应结果) {String} balanceDtlList.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} balanceDtlList.bankCode 银行代码
     * @apiSuccess (响应结果) {String} balanceDtlList.bankName 银行名称
     * @apiSuccess (响应结果) {String} balanceDtlList.bankAcctNo 银行卡号
     * @apiSuccess (响应结果) {String} balanceDtlList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {Number} balanceDtlList.balanceVol 总份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.availVol 可用份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.unconfirmedVol 待确认份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.marketValue 市值
     * @apiSuccess (响应结果) {String} balanceDtlList.openRedeDt 开放赎回日期
     * @apiSuccess (响应结果) {String} balanceDtlList.productChannel 产品通道  3-群济私募 5-好买公募 6-高端公募
     * @apiSuccess (响应结果) {Number} balanceDtlList.totalVol 总持有份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.totalUnConfirmVol 总在途份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.totalAvailVol 总可用份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.redeemAllSurplusVol 全赎剩余份额
     * @apiSuccess (响应结果) {Number} balanceDtlList.totalRedeemAllSurplusVol 总全赎剩余份额
     * @apiSuccess (响应结果) {Number} redeemUnconfirmedNum 赎回待确认笔数
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"dF4J","totalPage":3852,"pageNo":209,"description":"PeimxZQltJ","balanceDtlList":[{"redeemAllSurplusVol":7055.************,"bankAcctNo":"FV6T5tleu5","bankAcctMask":"us","productChannel":"HR","balanceVol":7086.************,"bankName":"SnADfK","disCode":"bPw","totalAvailVol":6708.************,"productName":"hsnVQ","availVol":2711.*************,"txAcctNo":"ehU6dPR8G5","protocolNo":"s0cVhxkhEe","cpAcctNo":"q7i","productType":"nCWziUjzb","bankCode":"Fvx","fundShareClass":"bm5Se5","nav":8159.************,"totalRedeemAllSurplusVol":5673.************,"navDt":"t5mTKUl","productStatus":"CmB1L24","marketValue":4832.*************,"totalUnConfirmVol":7266.***********,"productCode":"c","unconfirmedVol":9189.************,"openRedeDt":"G3JFp","buyStatus":"dTYJcqB","redeemStatus":"cOAVZ","totalVol":904.*************}],"totalCount":2455,"redeemUnconfirmedNum":5399}
     */
    @Override
    public QueryAcctBalanceDtlResponse execute(QueryAcctBalanceDtlRequest request) {
        String txAcctNo = request.getTxAcctNo();
        List<String> disCodeList = getDisCodeList(request);
        String productCode = request.getProductCode();
        String cpAcctNo = request.getCpAcctNo();
        String outletCode = request.getOutletCode();
        String appDt = request.getAppDt();
        String appTm = request.getAppTm();
        QueryAcctBalanceDtlResponse response = new QueryAcctBalanceDtlResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // TA交易日
        final String taTradeDt = queryTaTradeDtCacheService.getTaTradeDt(request.getAppDt(), request.getAppTm());
        // 产品基本信息
        HighProductBaseInfoBean highProductBaseInfoBean = queryHighProductOuterService.getHighProductBaseInfo(productCode);
        if (highProductBaseInfoBean == null) {
            logger.info("QueryAcctBalanceDtlFacadeService|productCode:{} product not exist ", productCode);
            return response;
        }
        String submitDt = getSubmitDt(highProductBaseInfoBean, disCodeList.get(0), taTradeDt, appDt, appTm);
        logger.info("QueryAcctBalanceDtlFacadeService|getSubmitDt|submitDt:{},disCode:{}, taTradeDt:{}, appDt:{}, appTm:{} ", submitDt, disCodeList.get(0), taTradeDt, appDt, appTm);
        // 查询用户持仓
        List<BalanceVo> booklist = custBooksRepository.selectBalanceDtlByDisCodeList(disCodeList, txAcctNo, null, productCode, cpAcctNo, submitDt);
        if (CollectionUtils.isEmpty(booklist)) {
            return response;
        }
        // 资金账号set
        Set<String> cpAcctNoSet = new HashSet<>();
        Iterator<BalanceVo> bookListIterator = booklist.iterator();
        while (bookListIterator.hasNext()) {
            BalanceVo book = bookListIterator.next();
            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            if (availVol.compareTo(BigDecimal.ZERO) <= 0) {
                bookListIterator.remove();
            } else {
                cpAcctNoSet.add(book.getCpAcctNo());
            }
        }
        // 获取银行卡信息
        Map<String, QueryCustBankCardResult> bankCardMap = new HashMap<>();
        CountDownLatch latch = new CountDownLatch(cpAcctNoSet.size());
        for (Object value : cpAcctNoSet.toArray()) {
            String cpAcct = (String) value;
            QueryCustBankCardResult bankCardInfo = new QueryCustBankCardResult();
            bankCardMap.put(cpAcct, bankCardInfo);
            CommonThreadPool.submit(new QueryCustBankCardTask(queryCustBankCardOuterService, latch, txAcctNo, disCodeList.get(0), cpAcct, bankCardInfo, outletCode));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("QueryAcctBalanceDtlFacadeService|latch|", e);
            Thread.currentThread().interrupt();
        }
        // 设置持仓信息
        setBalanceList(txAcctNo, disCodeList, response, highProductBaseInfoBean, booklist, bankCardMap);
        // 赎回在途笔数
        response.setRedeemUnconfirmedNum(dealOrderRepository.selectRedeemOnWayTradeNum(disCodeList, txAcctNo, productCode));
        return response;

    }

    /**
     * 设置持仓信息
     */
    private void setBalanceList(String txAcctNo, List<String> disCodeList, QueryAcctBalanceDtlResponse response, HighProductBaseInfoBean highProductBaseInfoBean, List<BalanceVo> booklist, Map<String, QueryCustBankCardResult> bankCardMap) {
        List<BalanceDtlBean> balanceDtlList = new ArrayList<>();
        BigDecimal totalVol = BigDecimal.ZERO;
        BigDecimal totalUnConfirmVol = BigDecimal.ZERO;
        BigDecimal totalAvailVol = BigDecimal.ZERO;
        BigDecimal totalRedeemAllSurplusVol = BigDecimal.ZERO;
        for (BalanceVo book : booklist) {
            BalanceDtlBean bean = new BalanceDtlBean();
            bean.setTxAcctNo(txAcctNo);
            bean.setDisCode(disCodeList.get(0));
            bean.setProductCode(book.getProductCode());
            bean.setCpAcctNo(book.getCpAcctNo());
            bean.setProtocolNo(book.getProtocolNo());

            totalVol = MathUtils.add(book.getBalanceVol(), totalVol);
            totalUnConfirmVol = MathUtils.add(book.getUnconfirmedVol(), totalUnConfirmVol);
            totalUnConfirmVol = MathUtils.add(totalUnConfirmVol, book.getJustFrznVol());

            // 可用份额
            BigDecimal availVol = book.getBalanceVol().subtract(book.getUnconfirmedVol()).subtract(book.getJustFrznVol());
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                availVol = availVol.subtract(book.getLockingPeriodVol());
            }
            bean.setAvailVol(MoneyUtil.formatMoney(availVol, 2));
            totalAvailVol = MathUtils.add(totalAvailVol, availVol);

            // 全赎剩余份额 = 未到锁定期 + 司法冻结
            if (RedeemTypeEnum.YES.getCode().equals(highProductBaseInfoBean.getHasLockPeriod())) {
                bean.setRedeemAllSurplusVol(MathUtils.add(book.getLockingPeriodVol(), book.getJustFrznVol()));
            } else {
                bean.setRedeemAllSurplusVol(book.getJustFrznVol());
            }
            totalRedeemAllSurplusVol = MathUtils.add(totalRedeemAllSurplusVol, bean.getRedeemAllSurplusVol());

            // 基金信息
            bean.setProductName(highProductBaseInfoBean.getFundAttr());
            bean.setProductType(highProductBaseInfoBean.getFundType());
            bean.setFundShareClass(highProductBaseInfoBean.getShareClass());
            bean.setProductChannel(highProductBaseInfoBean.getProductChannel());

            bean.setBalanceVol(MoneyUtil.formatMoney(book.getBalanceVol(), 2));

            // 银行卡信息
            QueryCustBankCardResult result = bankCardMap.get(book.getCpAcctNo());
            if (result != null) {
                bean.setBankCode(result.getBankCode());
                bean.setBankName(result.getBankRegionName());
                bean.setBankAcctNo(result.getBankAcct());
                bean.setBankAcctMask(result.getBankAcctMask());
            }

            balanceDtlList.add(bean);
        }

        for (BalanceDtlBean balanceDtlBean : balanceDtlList) {
            balanceDtlBean.setTotalVol(totalVol);
            balanceDtlBean.setTotalUnConfirmVol(totalUnConfirmVol);
            balanceDtlBean.setTotalAvailVol(totalAvailVol);
            balanceDtlBean.setTotalRedeemAllSurplusVol(totalRedeemAllSurplusVol);
        }

        response.setBalanceDtlList(balanceDtlList);
    }

    /**
     * @param request
     * @return java.util.List<java.lang.String>
     * @description:(获取分销机构代码)
     * @author: haiguang.chen
     * @date: 2022/1/4 11:04
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryAcctBalanceDtlRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }


    /**
     * getSubmitDt:(获取上报日期)
     *
     * @param highProductBaseInfoBean 产品基本信息
     * @param disCode                 分销机构代码
     * @param taTradeDt               当前日期所属交易日
     * @param appDt                   申请日期
     * @param appTm                   申请时间
     * @return
     * <AUTHOR>
     * @date 2018年6月15日 下午2:19:11
     */
    private String getSubmitDt(HighProductBaseInfoBean highProductBaseInfoBean, String disCode, String taTradeDt, String appDt, String appTm) {
        logger.info("QueryAcctBalanceDtlFacadeService|getTaTradeDt|highProductBaseInfoBean:{}, disCode:{},  taTradeDt:{}, appDt:{}, appTm:{}", JSON.toJSONString(highProductBaseInfoBean), disCode, taTradeDt, appDt, appTm);

        String appDtmStr = new StringBuilder(appDt).append(appTm).toString();
        Date appDtm = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);

        if (SupportAdvanceFlagEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())
                || SupportAdvanceFlagEnum.SupportRedeemAdvance.getCode().equals(highProductBaseInfoBean.getIsScheduledTrade())) {

            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(highProductBaseInfoBean.getFundCode(), "1", highProductBaseInfoBean.getShareClass(), disCode, appDtm);
            if (productAppointmentInfoBean != null) {
                if (taTradeDt.compareTo(productAppointmentInfoBean.getOpenStartDt()) < 0) {
                    return productAppointmentInfoBean.getOpenStartDt();
                }
            }
        }

        return taTradeDt;

    }

}

