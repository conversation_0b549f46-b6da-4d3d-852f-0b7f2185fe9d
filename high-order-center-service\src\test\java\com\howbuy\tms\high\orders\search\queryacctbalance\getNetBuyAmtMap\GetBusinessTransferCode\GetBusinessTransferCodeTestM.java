package com.howbuy.tms.high.orders.search.queryacctbalance.getNetBuyAmtMap.GetBusinessTransferCode;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.EffectiveType;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.BusinessCodeTransferConfigInfoBean;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程图:https://nextcloud.howbuy.com/apps/drawio/602741
 *
 * @Description:交易类型转义单元测试
 * @Author: yun.lu
 * Date: 2023/7/24 16:26
 */
@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({AcctBalanceBaseInfoServiceImpl.class})
@PowerMockIgnore({"javax.security.*", "javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class GetBusinessTransferCodeTestM {

    @InjectMocks
    private AcctBalanceBaseInfoServiceImpl acctBalanceBaseInfoService;

    /**
     * 转义配置
     */
    private List<BusinessCodeTransferConfigInfoBean> businessCodeConfigList;
    /**
     * 转义配置制定订单号
     */
    private String transferDealNo = "testDeal23456543";
    /**
     * 转义配置指定产品编码
     */
    private String transferFundCode = "testFundCode23456543";
    /**
     * 转义配置指定业务类型
     */
    private String currMBusinessCode1 = BusinessCodeEnum.FORCE_SUBTRACT.getMCode();
    /**
     * 转义配置指定业务类型
     */
    private String currMBusinessCode2 = BusinessCodeEnum.FORCE_ADD.getMCode();

    /**
     * 转义后的业务类型
     */
    private String transferMBusinessCode = BusinessCodeEnum.FUND_SHARE_DIVERT.getMCode();

    /**
     * 转义后的业务类型
     */
    private String transferMBusinessCode22 = BusinessCodeEnum.TRANSFER_PAYMENT.getMCode();

    /**
     * 转义后的业务类型
     */
    private String transferMBusinessCode33 = BusinessCodeEnum.WITHDRAW_TO_PIGGY.getMCode();

    @Before
    public void buildData() {
        MockitoAnnotations.initMocks(this);
        acctBalanceBaseInfoService = PowerMockito.spy(acctBalanceBaseInfoService);
        businessCodeConfigList = new ArrayList<>();
        // 1.转义配置,首单生效(强减)
        BusinessCodeTransferConfigInfoBean businessCodeTransferConfigInfoBean1 = new BusinessCodeTransferConfigInfoBean();
        businessCodeTransferConfigInfoBean1.setEffectiveType(EffectiveType.FIRST_ORDER.getType());
        businessCodeTransferConfigInfoBean1.setCurrMBusinessCode(currMBusinessCode1);
        businessCodeTransferConfigInfoBean1.setTransferMBusinessCode(transferMBusinessCode);
        businessCodeTransferConfigInfoBean1.setFundCode(transferFundCode);
        businessCodeConfigList.add(businessCodeTransferConfigInfoBean1);
        // 2.每次都调整(强增)
        BusinessCodeTransferConfigInfoBean businessCodeTransferConfigInfoBean2 = new BusinessCodeTransferConfigInfoBean();
        businessCodeTransferConfigInfoBean2.setEffectiveType(EffectiveType.EVERY_ORDER.getType());
        businessCodeTransferConfigInfoBean2.setFundCode(transferFundCode);
        businessCodeTransferConfigInfoBean2.setCurrMBusinessCode(currMBusinessCode2);
        businessCodeTransferConfigInfoBean2.setTransferMBusinessCode(transferMBusinessCode22);

        businessCodeConfigList.add(businessCodeTransferConfigInfoBean2);

        // 3.指定订单号调整
        BusinessCodeTransferConfigInfoBean businessCodeTransferConfigInfoBean3 = new BusinessCodeTransferConfigInfoBean();
        businessCodeTransferConfigInfoBean3.setEffectiveType(EffectiveType.FIRST_ORDER.getType());
        businessCodeTransferConfigInfoBean3.setFundCode(transferFundCode);
        businessCodeTransferConfigInfoBean3.setDealNo(transferDealNo);
        businessCodeTransferConfigInfoBean3.setTransferMBusinessCode(transferMBusinessCode33);
        businessCodeConfigList.add(businessCodeTransferConfigInfoBean3);

    }


    /**
     * 转义配置,首单生效
     * 三个元素:产品code,业务类型,是否首单;每个元素可以按照是否符合配置来写->8种情况
     */
    @Test
    public void getBusinessTransferCodeTest1() {
        acctBalanceBaseInfoService = PowerMockito.spy(acctBalanceBaseInfoService);
        // 1.指定产品+业务类型+首单->转义
        String transferMBusinessCode1 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode1, true, businessCodeConfigList);
        Assert.assertTrue(transferMBusinessCode.equals(transferMBusinessCode1));

        // 2.指定产品+业务类型+非首单->不转义
        String transferMBusinessCode2 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode1, false, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode2));

        // 3.指定产品+非指定业务类型+首单->不转义
        String transferMBusinessCode3 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode1 + "2345", true, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode3));

        // 4.非指定产品+业务类型+首单->不转义
        String transferMBusinessCode4 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode + "1234", currMBusinessCode1, true, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode4));

        // 5.非指定产品+非指定业务类型+首单->不转义
        String transferMBusinessCode5 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode + "1234", currMBusinessCode1 + "2345", true, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode5));

        // 6.非指定产品+指定业务类型+非首单->不转义
        String transferMBusinessCode6 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode + "2345", currMBusinessCode1, false, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode6));

        // 7.指定产品+非指定业务类型+非首单->不转义
        String transferMBusinessCode7 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode1 + "2345", false, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode7));

        // 8.非指定产品+非指定业务类型+非首单->不转义
        String transferMBusinessCode8 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode + "3456", currMBusinessCode1 + "2345", false, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode.equals(transferMBusinessCode8));
    }


    /**
     * 每次都调整
     */
    @Test
    public void getBusinessTransferCodeTest2() {
        acctBalanceBaseInfoService = PowerMockito.spy(acctBalanceBaseInfoService);
        // 1.指定业务类型->转义
        String transferMBusinessCode1 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode2, true, businessCodeConfigList);
        Assert.assertTrue(transferMBusinessCode22.equals(transferMBusinessCode1));

        // 2.非业务类型>不转义
        String transferMBusinessCode2 = acctBalanceBaseInfoService.getBusinessTransferCode("test123456", transferFundCode, currMBusinessCode2 + "2345", true, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode22.equals(transferMBusinessCode2));

    }


    /**
     * 指定订单号调整
     * 1个元素:订单号
     */
    @Test
    public void getBusinessTransferCodeTest3() {
        acctBalanceBaseInfoService = PowerMockito.spy(acctBalanceBaseInfoService);
        // 1.指定订单号->转义
        String transferMBusinessCode1 = acctBalanceBaseInfoService.getBusinessTransferCode(transferDealNo, transferFundCode, transferMBusinessCode33, true, businessCodeConfigList);
        Assert.assertTrue(transferMBusinessCode33.equals(transferMBusinessCode1));

        // 2.非制定订单号>不转义
        String transferMBusinessCode2 = acctBalanceBaseInfoService.getBusinessTransferCode(transferDealNo + "2345", transferFundCode, currMBusinessCode2 + "2345", true, businessCodeConfigList);
        Assert.assertTrue(!transferMBusinessCode33.equals(transferMBusinessCode2));

    }

}
