/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询客户持仓接口返回结果)
 * @reason:
 * @date 2018年6月21日 下午4:55:20
 * @since JDK 1.7
 */
public class QueryAcctBalanceResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = -6989971807357099927L;

    /**
     * 分销机构号
     */
    @Deprecated
    private String disCode;
    /**
     * 分销机构号列表-股权直销改造
     */
    private List<String> disCodeList;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 总市值
     */
    private BigDecimal totalMarketValue;
    /**
     * 在途总金额
     */
    private BigDecimal totalUnconfirmedAmt;
    /**
     * 待确认笔数
     */
    private Integer totalUnconfirmedNum;

    /**
     * 赎回待确认笔数
     */
    private Integer redeemUnconfirmedNum;

    /**
     * 当前总收益
     */
    private BigDecimal totalCurrentAsset;
    /**
     * 总收益计算状态: 0-计算中;1-计算成功
     */
    private String totalIncomCalStat;
    /**
     * 总回款
     */
    private BigDecimal totalCashCollection;

    /**
     * 换汇后总现金余额
     */
    private BigDecimal totalExchangeCashBalance;

    /**
     * 换汇后总冻结金额
     */
    private BigDecimal totalExchangeFrozenAmt;

    /**
     * 换汇后总可用余额
     */
    private BigDecimal totalExchangeAvailableBalance;


    /**
     * 香港展示总资产,balanceNew查询接口才有值
     */
    private BigDecimal disPlayHkTotalMarketValue;
    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct = YesOrNoEnum.NO.getCode();
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct = YesOrNoEnum.NO.getCode();
    /**
     * 持仓明细列表
     */
    private List<BalanceBean> balanceList = new ArrayList<BalanceBean>();

    private List<UnconfirmeProduct> unconfirmeProducts = new ArrayList<UnconfirmeProduct>();

    public List<UnconfirmeProduct> getUnconfirmeProducts() {
        return unconfirmeProducts;
    }

    public void setUnconfirmeProducts(List<UnconfirmeProduct> unconfirmeProducts) {
        this.unconfirmeProducts = unconfirmeProducts;
    }

    public BigDecimal getDisPlayHkTotalMarketValue() {
        return disPlayHkTotalMarketValue;
    }

    public void setDisPlayHkTotalMarketValue(BigDecimal disPlayHkTotalMarketValue) {
        this.disPlayHkTotalMarketValue = disPlayHkTotalMarketValue;
    }

    public String getHasHZProduct() {
        return hasHZProduct;
    }

    public void setHasHZProduct(String hasHZProduct) {
        this.hasHZProduct = hasHZProduct;
    }

    public String getHasHKProduct() {
        return hasHKProduct;
    }

    public void setHasHKProduct(String hasHKProduct) {
        this.hasHKProduct = hasHKProduct;
    }

    public String getTotalIncomCalStat() {
        return totalIncomCalStat;
    }

    public void setTotalIncomCalStat(String totalIncomCalStat) {
        this.totalIncomCalStat = totalIncomCalStat;
    }

    public BigDecimal getTotalCurrentAsset() {
        return totalCurrentAsset;
    }

    public void setTotalCurrentAsset(BigDecimal totalCurrentAsset) {
        this.totalCurrentAsset = totalCurrentAsset;
    }

    public BigDecimal getTotalMarketValue() {
        return totalMarketValue;
    }

    public void setTotalMarketValue(BigDecimal totalMarketValue) {
        this.totalMarketValue = totalMarketValue;
    }

    public BigDecimal getTotalUnconfirmedAmt() {
        return totalUnconfirmedAmt;
    }

    public void setTotalUnconfirmedAmt(BigDecimal totalUnconfirmedAmt) {
        this.totalUnconfirmedAmt = totalUnconfirmedAmt;
    }

    public BigDecimal getTotalExchangeCashBalance() {
        return totalExchangeCashBalance;
    }

    public void setTotalExchangeCashBalance(BigDecimal totalExchangeCashBalance) {
        this.totalExchangeCashBalance = totalExchangeCashBalance;
    }

    public BigDecimal getTotalExchangeFrozenAmt() {
        return totalExchangeFrozenAmt;
    }

    public void setTotalExchangeFrozenAmt(BigDecimal totalExchangeFrozenAmt) {
        this.totalExchangeFrozenAmt = totalExchangeFrozenAmt;
    }

    public BigDecimal getTotalExchangeAvailableBalance() {
        return totalExchangeAvailableBalance;
    }

    public void setTotalExchangeAvailableBalance(BigDecimal totalExchangeAvailableBalance) {
        this.totalExchangeAvailableBalance = totalExchangeAvailableBalance;
    }

    public List<BalanceBean> getBalanceList() {
        return balanceList;
    }

    public void setBalanceList(List<BalanceBean> balanceList) {
        this.balanceList = balanceList;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    @Deprecated
    public String getDisCode() {
        return disCode;
    }

    @Deprecated
    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public Integer getTotalUnconfirmedNum() {
        return totalUnconfirmedNum;
    }

    public void setTotalUnconfirmedNum(Integer totalUnconfirmedNum) {
        this.totalUnconfirmedNum = totalUnconfirmedNum;
    }

    public Integer getRedeemUnconfirmedNum() {
        return redeemUnconfirmedNum;
    }

    public void setRedeemUnconfirmedNum(Integer redeemUnconfirmedNum) {
        this.redeemUnconfirmedNum = redeemUnconfirmedNum;
    }

    public BigDecimal getTotalCashCollection() {
        return totalCashCollection;
    }

    public void setTotalCashCollection(BigDecimal totalCashCollection) {
        this.totalCashCollection = totalCashCollection;
    }

    public static class BalanceBean implements Serializable, Comparable<BalanceBean> {
        private static final long serialVersionUID = -6776763701201056826L;

        /**
         * 分销代码
         */
        private String disCode;
        /**
         * 分销代码列表
         */
        private List<String> disCodeList;
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 子产品代码
         */
        private String subProductCode;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 产品类型
         */
        private String productType;
        /**
         * 产品子类型(好买产品线)
         */
        private String productSubType;
        /**
         * 总份额
         */
        private BigDecimal balanceVol;
        /**
         * 待确认份额
         */
        private BigDecimal unconfirmedVol;
        /**
         * 待确认金额
         */
        private BigDecimal unconfirmedAmt;
        /**
         * 币种
         */
        private String currency;
        /**
         * 净值
         */
        private BigDecimal nav;
        /**
         * 净值日期
         */
        private String navDt;
        /**
         * 净值分红标识 0-否，1-是
         */
        private String navDivFlag = "0";
        /**
         * 实缴金额
         */
        private BigDecimal paidTotalAmt;

        /**
         * 实缴百分比
         */
        private String paidSubTotalRatio;
        /**
         * 市值
         */
        private BigDecimal marketValue;
        /**
         * 当前币种的市值
         */
        private BigDecimal currencyMarketValue;
        /**
         * 销售类型: 1-直销;2-代销
         */
        private String scaleType;
        /**
         * 好买香港代销标识: 0-否; 1-是
         */
        private String hkSaleFlag;
        /**
         * 分期成立标识(证券类有此标识:0-否,1-是)
         */
        private String StageEstablishFlag;
        /**
         * 分次call标识(股权类有此标识:0-否,1-是)
         */
        private String fractionateCallFlag;
        /**
         * 产品存续期限(类似于5+3+2这种说明)
         */
        private String fundCXQXStr;
        /**
         * 净购买金额(投资成本)
         */
        private BigDecimal netBuyAmount;
        /**
         * 净购买金额(投资成本)(当前币种)
         */
        private BigDecimal currencyNetBuyAmount;
        /**
         * 认缴金额
         */
        private BigDecimal paidInAmt;
        /**
         * 收益日期
         */
        private String incomeDt;
        /**
         * 0-计算中；1-计算完成
         */
        private String incomeCalStat;
        /**
         * 当前收益(人民币）
         */
        private BigDecimal currentAsset;
        /**
         * 当前收益（当前币种）
         */
        private BigDecimal currentAssetCurrency;
        /**
         * 累计收益
         */
        private BigDecimal accumIncome;
        /**
         * 累计收益(人民币)
         */
        private BigDecimal accumIncomeRmb;
        /**
         * 累计已实现收益
         */
        private BigDecimal accumRealizedIncome;
        /**
         * 累计已实现收益人民币
         */
        private BigDecimal accumRealizedIncomeRmb;

        /**
         * 是否复构 0-否 1-是
         **/
        private String rePurchaseFlag;

        /**
         * 业绩比较基准
         **/
        private String benchmark;
        /**
         * 业绩比较基准类型：0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
         **/
        private String benchmarkType;

        /**
         * 起息日
         **/
        private String valueDate;

        /**
         * 到期日
         **/
        private String dueDate;

        /**
         * 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
         */
        private String standardFixedIncomeFlag;

        /**
         * 投资期限
         */
        private String investmentHorizon;

        /**
         * 合作商户
         */
        private String cooperation;

        /**
         * 清盘中标识 0-否 1-是
         */
        private String crisisFlag;
        /**
         * 七日年化收益
         */
        private BigDecimal yieldIncome;
        /**
         * 七日年化日期/万份收益日期
         */
        private String yieldIncomeDt;
        /**
         * 万份收益
         */
        private BigDecimal copiesIncome;

        /**
         * 是否海外产品 0-否 1-是
         */
        private String hwSaleFlag;
        /**
         * 登记日期
         */
        private String regDt;
        /**
         * 一级监管分类
         */
        private String oneStepType;
        /**
         * 二级监管分类
         */
        private String twoStepType;
        /**
         * 三级监管分类
         */
        private String secondStepType;
        /**
         * 产品销售类型 0-好买 1-海外 2-其他
         */
        private String productSaleType;
        /**
         * NA产品收费类型 10201-好买收费 0-管理人收费
         */
        private String naProductFeeType;
        /**
         * 累计应收管理费
         */
        private BigDecimal receivManageFee;
        /**
         * 累计应收业绩报酬
         */
        private BigDecimal receivPreformFee;
        /**
         * NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
         */
        private BigDecimal currencyMarketValueExFee;
        /**
         * NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
         */
        private BigDecimal marketValueExFee;
        /**
         * 当前收益（股权新算法）不含费
         */
        private BigDecimal balanceIncomeNew;
        /**
         * 当前收益（股权新算法）不含费-人民币
         */
        private BigDecimal balanceIncomeNewRmb;
        /**
         * 平衡因子
         */
        private BigDecimal balanceFactor;
        /**
         * 平衡因子转换完成 1-是 0-否
         */
        private String convertFinish;
        /**
         * 平衡因子日期
         */
        private String balanceFactorDate;

        /**
         * 收益率
         */
        private BigDecimal yieldRate;


        /**************持仓2.0 crm页面新增字段start****************/
        /**
         * 累计收益（股权固收新算法）不含费
         */
        private BigDecimal accumIncomeNew;
        /**
         * 累计收益（股权固收新算法）不含费-人民币
         */
        private BigDecimal accumIncomeNewRmb;
        /**
         * 持仓总成本(人民币）
         */
        private BigDecimal balanceCost;
        /**
         * 持仓总成本（当前币种）
         */
        private BigDecimal balanceCostCurrency;
        /**
         * 日收益(人民币）
         */
        private BigDecimal dailyAsset;
        /**
         * 日收益（当前币种）
         */
        private BigDecimal dailyAssetCurrency;
        /**
         * 私募股权回款
         */
        private BigDecimal cashCollection;
        /**
         * 私募股权回款(当前币种)
         */
        private BigDecimal currencyCashCollection;

        /**
         * 累计收益率
         */
        private BigDecimal accumYieldRate;
        /**
         * 累计成本
         */
        private BigDecimal accumCost;
        /**
         * 累计成本(人民币)
         */
        private BigDecimal accumCostRmb;
        /**
         * 持仓浮盈亏
         */
        private BigDecimal balanceFloatIncome;
        /**
         * 持仓浮盈亏(人民币)
         */
        private BigDecimal balanceFloatIncomeRmb;
        /**
         * 持仓浮盈亏比率
         */
        private BigDecimal balanceFloatIncomeRate;
        /**
         * 最新收益率
         */
        private BigDecimal dayAssetRate;
        /**
         * 最新资产增长率
         */
        private BigDecimal dayIncomeGrowthRate;

        /**
         * 投资总成本——新
         */
        private BigDecimal accumCostNew;
        /**
         * 投资总成本——新
         */
        private BigDecimal accumCostRmbNew;
        /**
         * 参考市值
         */
        private BigDecimal balanceAmt;
        /**
         * 参考市值(人民币)
         */
        private BigDecimal balanceAmtRmb;
        /**
         * 累计总回款
         */
        private BigDecimal accumCollection;
        /**
         * 累计总回款(人民币)
         */
        private BigDecimal accumCollectionRmb;
        /**
         * NA费后参考市值
         */
        private BigDecimal balanceAmtExFee;
        /**
         * NA费后参考市值(人民币)
         */
        private BigDecimal balanceAmtExFeeRmb;

        /**
         * 非交易接口(获取NA标识)：SXZ -- NA产品收费类型 （10201：好买收费 0：管理人收费）
         */
        private String sxz;

        /**
         * 当前收益(当前币种）
         */
        private BigDecimal currentIncome;

        /**
         * 当前收益(人民币）
         */
        private BigDecimal currentIncomeRmb;

        /**
         * 当前累计收益(当前币种）
         */
        private BigDecimal currentAccumIncome;

        /**
         * 当前累计收益(人民币）
         */
        private BigDecimal currentAccumIncomeRmb;
        /**
         * 是否拆单产品 1-是
         */
        private String stageFlag;
        /**
         * 产品成立日期
         */
        private String establishDt;
        /****************持仓2.2**********************/
        /**
         * 收益计算日期
         */
        private String assetUpdateDate;
        /**
         * 单位持仓成本去费
         */
        private BigDecimal unitBalanceCostExFee;
        /**
         * 单位持仓成本去费(人民币)
         */
        private BigDecimal unitBalanceCostExFeeRmb;

        /**
         * 股权转让标识
         *
         * @see OwnershipTransferIdentityEnum
         */
        private String ownershipTransferIdentity;
        /**
         * 股权回款进度,保留2小数
         */
        private String accumBackRatio;

        /**
         * 资产策略一级分类 0.多策略 1.股票型 2.股权型 3.CTA 4.另类 5.固收与中性
         */
        private String assetStrategyFirstType;

        public String getAssetStrategyFirstType() {
            return assetStrategyFirstType;
        }

        public void setAssetStrategyFirstType(String assetStrategyFirstType) {
            this.assetStrategyFirstType = assetStrategyFirstType;
        }

        public String getOwnershipTransferIdentity() {
            return ownershipTransferIdentity;
        }

        public void setOwnershipTransferIdentity(String ownershipTransferIdentity) {
            this.ownershipTransferIdentity = ownershipTransferIdentity;
        }

        /****************持仓2.2**********************/


        public String getAssetUpdateDate() {
            return assetUpdateDate;
        }

        public void setAssetUpdateDate(String assetUpdateDate) {
            this.assetUpdateDate = assetUpdateDate;
        }

        public BigDecimal getUnitBalanceCostExFee() {
            return unitBalanceCostExFee;
        }

        public void setUnitBalanceCostExFee(BigDecimal unitBalanceCostExFee) {
            this.unitBalanceCostExFee = unitBalanceCostExFee;
        }

        public BigDecimal getUnitBalanceCostExFeeRmb() {
            return unitBalanceCostExFeeRmb;
        }

        public void setUnitBalanceCostExFeeRmb(BigDecimal unitBalanceCostExFeeRmb) {
            this.unitBalanceCostExFeeRmb = unitBalanceCostExFeeRmb;
        }

        public String getAccumBackRatio() {
            return accumBackRatio;
        }

        public void setAccumBackRatio(String accumBackRatio) {
            this.accumBackRatio = accumBackRatio;
        }

        public BigDecimal getPaidTotalAmt() {
            return paidTotalAmt;
        }

        public void setPaidTotalAmt(BigDecimal paidTotalAmt) {
            this.paidTotalAmt = paidTotalAmt;
        }

        public String getPaidSubTotalRatio() {
            return paidSubTotalRatio;
        }

        public void setPaidSubTotalRatio(String paidSubTotalRatio) {
            this.paidSubTotalRatio = paidSubTotalRatio;
        }

        public String getEstablishDt() {
            return establishDt;
        }

        public void setEstablishDt(String establishDt) {
            this.establishDt = establishDt;
        }

        public String getStageFlag() {
            return stageFlag;
        }

        public void setStageFlag(String stageFlag) {
            this.stageFlag = stageFlag;
        }

        /**
         * 是否海外储蓄罐(1:是;0:否)
         */
        private String sfhwcxg;

        public String getSfhwcxg() {
            return sfhwcxg;
        }

        public void setSfhwcxg(String sfhwcxg) {
            this.sfhwcxg = sfhwcxg;
        }

        /**************持仓2.0 crm页面新增字段end****************/

        public BigDecimal getCurrentAccumIncome() {
            return currentAccumIncome;
        }

        public void setCurrentAccumIncome(BigDecimal currentAccumIncome) {
            this.currentAccumIncome = currentAccumIncome;
        }

        public BigDecimal getCurrentAccumIncomeRmb() {
            return currentAccumIncomeRmb;
        }

        public void setCurrentAccumIncomeRmb(BigDecimal currentAccumIncomeRmb) {
            this.currentAccumIncomeRmb = currentAccumIncomeRmb;
        }

        public BigDecimal getCurrentIncome() {
            return currentIncome;
        }

        public void setCurrentIncome(BigDecimal currentIncome) {
            this.currentIncome = currentIncome;
        }

        public BigDecimal getCurrentIncomeRmb() {
            return currentIncomeRmb;
        }

        public void setCurrentIncomeRmb(BigDecimal currentIncomeRmb) {
            this.currentIncomeRmb = currentIncomeRmb;
        }

        public BigDecimal getBalanceAmtExFee() {
            return balanceAmtExFee;
        }

        public void setBalanceAmtExFee(BigDecimal balanceAmtExFee) {
            this.balanceAmtExFee = balanceAmtExFee;
        }


        public BigDecimal getBalanceAmtExFeeRmb() {
            return balanceAmtExFeeRmb;
        }

        public void setBalanceAmtExFeeRmb(BigDecimal balanceAmtExFeeRmb) {
            this.balanceAmtExFeeRmb = balanceAmtExFeeRmb;
        }

        public BigDecimal getAccumCollection() {
            return accumCollection;
        }

        public void setAccumCollection(BigDecimal accumCollection) {
            this.accumCollection = accumCollection;
        }

        public BigDecimal getAccumCollectionRmb() {
            return accumCollectionRmb;
        }

        public void setAccumCollectionRmb(BigDecimal accumCollectionRmb) {
            this.accumCollectionRmb = accumCollectionRmb;
        }

        public BigDecimal getBalanceAmt() {
            return balanceAmt;
        }

        public void setBalanceAmt(BigDecimal balanceAmt) {
            this.balanceAmt = balanceAmt;
        }

        public BigDecimal getBalanceAmtRmb() {
            return balanceAmtRmb;
        }

        public void setBalanceAmtRmb(BigDecimal balanceAmtRmb) {
            this.balanceAmtRmb = balanceAmtRmb;
        }

        public BigDecimal getAccumCostNew() {
            return accumCostNew;
        }

        public void setAccumCostNew(BigDecimal accumCostNew) {
            this.accumCostNew = accumCostNew;
        }

        public BigDecimal getAccumCostRmbNew() {
            return accumCostRmbNew;
        }

        public void setAccumCostRmbNew(BigDecimal accumCostRmbNew) {
            this.accumCostRmbNew = accumCostRmbNew;
        }

        public BigDecimal getAccumYieldRate() {
            return accumYieldRate;
        }

        public void setAccumYieldRate(BigDecimal accumYieldRate) {
            this.accumYieldRate = accumYieldRate;
        }

        public BigDecimal getAccumCost() {
            return accumCost;
        }

        public void setAccumCost(BigDecimal accumCost) {
            this.accumCost = accumCost;
        }

        public BigDecimal getAccumCostRmb() {
            return accumCostRmb;
        }

        public void setAccumCostRmb(BigDecimal accumCostRmb) {
            this.accumCostRmb = accumCostRmb;
        }

        public BigDecimal getBalanceFloatIncome() {
            return balanceFloatIncome;
        }

        public void setBalanceFloatIncome(BigDecimal balanceFloatIncome) {
            this.balanceFloatIncome = balanceFloatIncome;
        }

        public BigDecimal getBalanceFloatIncomeRmb() {
            return balanceFloatIncomeRmb;
        }

        public void setBalanceFloatIncomeRmb(BigDecimal balanceFloatIncomeRmb) {
            this.balanceFloatIncomeRmb = balanceFloatIncomeRmb;
        }

        public BigDecimal getBalanceFloatIncomeRate() {
            return balanceFloatIncomeRate;
        }

        public void setBalanceFloatIncomeRate(BigDecimal balanceFloatIncomeRate) {
            this.balanceFloatIncomeRate = balanceFloatIncomeRate;
        }

        public BigDecimal getDayAssetRate() {
            return dayAssetRate;
        }

        public void setDayAssetRate(BigDecimal dayAssetRate) {
            this.dayAssetRate = dayAssetRate;
        }

        public BigDecimal getDayIncomeGrowthRate() {
            return dayIncomeGrowthRate;
        }

        public void setDayIncomeGrowthRate(BigDecimal dayIncomeGrowthRate) {
            this.dayIncomeGrowthRate = dayIncomeGrowthRate;
        }

        /**
         * 股权产品期限说明
         */
        private String cpqxsm;

        /**
         * 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
         */
        private String navDisclosureType;

        /**
         * 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上
         * 异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
         */
        private String abnormalFlag = "0";

        /**
         * 人民币市值-是否控制表人为置空(0-否 1-是)
         */
        private String marketValueCtl = "0";
        /**
         * 当前币种市值-是否控制表人为置空
         */
        private String currencyMarketValueCtl = "0";
        /**
         * NA产品费后市值（当前币种）-是否控制表人为置空
         */
        private String currencyMarketValueExFeeCtl = "0";
        /**
         * NA产品费后市值（人民币）-是否控制表人为置空
         */
        private String marketValueExFeeCtl = "0";
        /**
         * 人民币收益-是否控制表人为置空
         */
        private String currentAssetCtl = "0";
        /**
         * 当前币种收益-是否控制表人为置空
         */
        private String currentAssetCurrencyCtl = "0";
        /**
         * 人民币日收益-是否控制表人为置空
         */
        private String dailyAssetCtl = "0";
        /**
         * 当前币种日收益-是否控制表人为置空
         */
        private String dailyAssetCurrencyCtl = "0";
        /**
         * 累计收益-是否控制表人为置空
         */
        private String accumIncomeCtl = "0";
        /**
         * 累计收益人民币-是否控制表人为置空
         */
        private String accumIncomeRmbCtl = "0";
        /**
         * 累计已实现收益-是否控制表人为置空
         */
        private String accumRealizedIncomeCtl = "0";
        /**
         * 累计已实现收益人民币-是否控制表人为置空
         */
        private String accumRealizedIncomeRmbCtl = "0";
        /**
         * 当前收益（股权新算法）不含费-是否控制表人为置空
         */
        private String balanceIncomeNewCtl = "0";
        /**
         * 当前收益（股权新算法）不含费人民币-是否控制表人为置空
         */
        private String balanceIncomeNewRmbCtl = "0";
        /**
         * 累计收益（股权固收新算法）不含费-是否控制表人为置空
         */
        private String accumIncomeNewCtl = "0";
        /**
         * 累计收益（股权固收新算法）不含费人民币-是否控制表人为置空
         */
        private String accumIncomeNewRmbCtl = "0";
        /**
         * 收益率-是否控制表人为置空
         */
        private String yieldRateCtl = "0";
        /**
         * 份额-是否控制表人为置空
         */
        private String balanceVolCtl = "0";
        /**
         * 待确认份额-是否控制表人为置空
         */
        private String unconfirmedVolCtl = "0";
        /**
         * 净值-是否控制表人为置空
         */
        private String navCtl = "0";

        // 千禧年持仓功能适配需求 20230216
        /**
         * 是否为千禧年产品 0-否、1-是
         */
        private String qianXiFlag = "0";

        /**
         * 待投金额（人民币）
         */
        private BigDecimal unPaidInAmt;

        /**
         * 待投金额（当前币种）
         */
        private BigDecimal currencyUnPaidInAmt;

        /**
         * 是否持仓
         *
         * @return true:持仓,false:不持仓
         */
        public boolean isBalance() {
            if (ScaleTypeEnum.DIRECT.getCode().equals(scaleType)) {
                if (YesOrNoEnum.YES.getCode().equals(hkSaleFlag)) {
                    // 海外,份额大于0,认为持仓
                    return balanceVol.compareTo(BigDecimal.ZERO) > 0;
                } else {
                    // 直销,份额大于1,认为持仓
                    return balanceVol.compareTo(BigDecimal.ONE) > 0;
                }

            } else {
                // 代销,份额大于0,认为持仓
                return balanceVol.compareTo(BigDecimal.ZERO) > 0;
            }
        }

        public String getQianXiFlag() {
            return qianXiFlag;
        }

        public void setQianXiFlag(String qianXiFlag) {
            this.qianXiFlag = qianXiFlag;
        }

        public BigDecimal getUnPaidInAmt() {
            return unPaidInAmt;
        }

        public void setUnPaidInAmt(BigDecimal unPaidInAmt) {
            this.unPaidInAmt = unPaidInAmt;
        }

        public BigDecimal getCurrencyUnPaidInAmt() {
            return currencyUnPaidInAmt;
        }

        public void setCurrencyUnPaidInAmt(BigDecimal currencyUnPaidInAmt) {
            this.currencyUnPaidInAmt = currencyUnPaidInAmt;
        }

        public String getMarketValueCtl() {
            return marketValueCtl;
        }

        public void setMarketValueCtl(String marketValueCtl) {
            this.marketValueCtl = marketValueCtl;
        }

        public String getCurrencyMarketValueCtl() {
            return currencyMarketValueCtl;
        }

        public void setCurrencyMarketValueCtl(String currencyMarketValueCtl) {
            this.currencyMarketValueCtl = currencyMarketValueCtl;
        }

        public String getCurrencyMarketValueExFeeCtl() {
            return currencyMarketValueExFeeCtl;
        }

        public void setCurrencyMarketValueExFeeCtl(String currencyMarketValueExFeeCtl) {
            this.currencyMarketValueExFeeCtl = currencyMarketValueExFeeCtl;
        }

        public String getMarketValueExFeeCtl() {
            return marketValueExFeeCtl;
        }

        public void setMarketValueExFeeCtl(String marketValueExFeeCtl) {
            this.marketValueExFeeCtl = marketValueExFeeCtl;
        }

        public String getCurrentAssetCtl() {
            return currentAssetCtl;
        }

        public void setCurrentAssetCtl(String currentAssetCtl) {
            this.currentAssetCtl = currentAssetCtl;
        }

        public String getCurrentAssetCurrencyCtl() {
            return currentAssetCurrencyCtl;
        }

        public void setCurrentAssetCurrencyCtl(String currentAssetCurrencyCtl) {
            this.currentAssetCurrencyCtl = currentAssetCurrencyCtl;
        }

        public String getDailyAssetCtl() {
            return dailyAssetCtl;
        }

        public void setDailyAssetCtl(String dailyAssetCtl) {
            this.dailyAssetCtl = dailyAssetCtl;
        }

        public String getDailyAssetCurrencyCtl() {
            return dailyAssetCurrencyCtl;
        }

        public void setDailyAssetCurrencyCtl(String dailyAssetCurrencyCtl) {
            this.dailyAssetCurrencyCtl = dailyAssetCurrencyCtl;
        }

        public String getAccumIncomeCtl() {
            return accumIncomeCtl;
        }

        public void setAccumIncomeCtl(String accumIncomeCtl) {
            this.accumIncomeCtl = accumIncomeCtl;
        }

        public String getAccumIncomeRmbCtl() {
            return accumIncomeRmbCtl;
        }

        public void setAccumIncomeRmbCtl(String accumIncomeRmbCtl) {
            this.accumIncomeRmbCtl = accumIncomeRmbCtl;
        }

        public String getAccumRealizedIncomeCtl() {
            return accumRealizedIncomeCtl;
        }

        public void setAccumRealizedIncomeCtl(String accumRealizedIncomeCtl) {
            this.accumRealizedIncomeCtl = accumRealizedIncomeCtl;
        }

        public String getAccumRealizedIncomeRmbCtl() {
            return accumRealizedIncomeRmbCtl;
        }

        public void setAccumRealizedIncomeRmbCtl(String accumRealizedIncomeRmbCtl) {
            this.accumRealizedIncomeRmbCtl = accumRealizedIncomeRmbCtl;
        }

        public String getBalanceIncomeNewCtl() {
            return balanceIncomeNewCtl;
        }

        public void setBalanceIncomeNewCtl(String balanceIncomeNewCtl) {
            this.balanceIncomeNewCtl = balanceIncomeNewCtl;
        }

        public String getBalanceIncomeNewRmbCtl() {
            return balanceIncomeNewRmbCtl;
        }

        public void setBalanceIncomeNewRmbCtl(String balanceIncomeNewRmbCtl) {
            this.balanceIncomeNewRmbCtl = balanceIncomeNewRmbCtl;
        }

        public String getAccumIncomeNewCtl() {
            return accumIncomeNewCtl;
        }

        public void setAccumIncomeNewCtl(String accumIncomeNewCtl) {
            this.accumIncomeNewCtl = accumIncomeNewCtl;
        }

        public String getAccumIncomeNewRmbCtl() {
            return accumIncomeNewRmbCtl;
        }

        public void setAccumIncomeNewRmbCtl(String accumIncomeNewRmbCtl) {
            this.accumIncomeNewRmbCtl = accumIncomeNewRmbCtl;
        }

        public String getYieldRateCtl() {
            return yieldRateCtl;
        }

        public void setYieldRateCtl(String yieldRateCtl) {
            this.yieldRateCtl = yieldRateCtl;
        }

        public String getBalanceVolCtl() {
            return balanceVolCtl;
        }

        public void setBalanceVolCtl(String balanceVolCtl) {
            this.balanceVolCtl = balanceVolCtl;
        }

        public String getUnconfirmedVolCtl() {
            return unconfirmedVolCtl;
        }

        public void setUnconfirmedVolCtl(String unconfirmedVolCtl) {
            this.unconfirmedVolCtl = unconfirmedVolCtl;
        }

        public String getNavCtl() {
            return navCtl;
        }

        public void setNavCtl(String navCtl) {
            this.navCtl = navCtl;
        }

        public String getAbnormalFlag() {
            return abnormalFlag;
        }

        public void setAbnormalFlag(String abnormalFlag) {
            this.abnormalFlag = abnormalFlag;
        }

        public String getNavDisclosureType() {
            return navDisclosureType;
        }

        public void setNavDisclosureType(String navDisclosureType) {
            this.navDisclosureType = navDisclosureType;
        }

        public String getCpqxsm() {
            return cpqxsm;
        }

        public void setCpqxsm(String cpqxsm) {
            this.cpqxsm = cpqxsm;
        }

        public BigDecimal getCurrencyCashCollection() {
            return currencyCashCollection;
        }

        public void setCurrencyCashCollection(BigDecimal currencyCashCollection) {
            this.currencyCashCollection = currencyCashCollection;
        }

        public BigDecimal getCurrencyNetBuyAmount() {
            return currencyNetBuyAmount;
        }

        public void setCurrencyNetBuyAmount(BigDecimal currencyNetBuyAmount) {
            this.currencyNetBuyAmount = currencyNetBuyAmount;
        }

        public String getStageEstablishFlag() {
            return StageEstablishFlag;
        }

        public void setStageEstablishFlag(String stageEstablishFlag) {
            StageEstablishFlag = stageEstablishFlag;
        }

        public String getFractionateCallFlag() {
            return fractionateCallFlag;
        }

        public void setFractionateCallFlag(String fractionateCallFlag) {
            this.fractionateCallFlag = fractionateCallFlag;
        }

        public String getHkSaleFlag() {
            return hkSaleFlag;
        }

        public void setHkSaleFlag(String hkSaleFlag) {
            this.hkSaleFlag = hkSaleFlag;
        }

        public BigDecimal getPaidInAmt() {
            return paidInAmt;
        }

        public void setPaidInAmt(BigDecimal paidInAmt) {
            this.paidInAmt = paidInAmt;
        }

        public String getFundCXQXStr() {
            return fundCXQXStr;
        }

        public void setFundCXQXStr(String fundCXQXStr) {
            this.fundCXQXStr = fundCXQXStr;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getDisCode() {
            return disCode;
        }

        public void setDisCode(String disCode) {
            this.disCode = disCode;
        }

        public BigDecimal getCurrentAsset() {
            return currentAsset;
        }

        public void setCurrentAsset(BigDecimal currentAsset) {
            this.currentAsset = currentAsset;
        }

        public String getSubProductCode() {
            return subProductCode;
        }

        public void setSubProductCode(String subProductCode) {
            this.subProductCode = subProductCode;
        }

        public String getScaleType() {
            return scaleType;
        }

        public void setScaleType(String scaleType) {
            this.scaleType = scaleType;
        }

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public BigDecimal getNav() {
            return nav;
        }

        public void setNav(BigDecimal nav) {
            this.nav = nav;
        }

        public BigDecimal getMarketValue() {
            return marketValue;
        }

        public void setMarketValue(BigDecimal marketValue) {
            this.marketValue = marketValue;
        }

        public String getNavDt() {
            return navDt;
        }

        public void setNavDt(String navDt) {
            this.navDt = navDt;
        }

        public String getNavDivFlag() {
            return navDivFlag;
        }

        public void setNavDivFlag(String navDivFlag) {
            this.navDivFlag = navDivFlag;
        }

        public BigDecimal getUnconfirmedVol() {
            return unconfirmedVol;
        }

        public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
            this.unconfirmedVol = unconfirmedVol;
        }

        public BigDecimal getUnconfirmedAmt() {
            return unconfirmedAmt;
        }

        public void setUnconfirmedAmt(BigDecimal unconfirmedAmt) {
            this.unconfirmedAmt = unconfirmedAmt;
        }

        public BigDecimal getDailyAsset() {
            return dailyAsset;
        }

        public void setDailyAsset(BigDecimal dailyAsset) {
            this.dailyAsset = dailyAsset;
        }

        public BigDecimal getBalanceCost() {
            return balanceCost;
        }

        public void setBalanceCost(BigDecimal balanceCost) {
            this.balanceCost = balanceCost;
        }

        public BigDecimal getCurrencyMarketValue() {
            return currencyMarketValue;
        }

        public void setCurrencyMarketValue(BigDecimal currencyMarketValue) {
            this.currencyMarketValue = currencyMarketValue;
        }

        public BigDecimal getCurrentAssetCurrency() {
            return currentAssetCurrency;
        }

        public void setCurrentAssetCurrency(BigDecimal currentAssetCurrency) {
            this.currentAssetCurrency = currentAssetCurrency;
        }

        public BigDecimal getBalanceCostCurrency() {
            return balanceCostCurrency;
        }

        public void setBalanceCostCurrency(BigDecimal balanceCostCurrency) {
            this.balanceCostCurrency = balanceCostCurrency;
        }

        public BigDecimal getDailyAssetCurrency() {
            return dailyAssetCurrency;
        }

        public void setDailyAssetCurrency(BigDecimal dailyAssetCurrency) {
            this.dailyAssetCurrency = dailyAssetCurrency;
        }

        public String getIncomeDt() {
            return incomeDt;
        }

        public void setIncomeDt(String incomeDt) {
            this.incomeDt = incomeDt;
        }

        public String getIncomeCalStat() {
            return incomeCalStat;
        }

        public void setIncomeCalStat(String incomeCalStat) {
            this.incomeCalStat = incomeCalStat;
        }

        public BigDecimal getAccumRealizedIncome() {
            return accumRealizedIncome;
        }

        public void setAccumRealizedIncome(BigDecimal accumRealizedIncome) {
            this.accumRealizedIncome = accumRealizedIncome;
        }

        public BigDecimal getNetBuyAmount() {
            return netBuyAmount;
        }

        public void setNetBuyAmount(BigDecimal netBuyAmount) {
            this.netBuyAmount = netBuyAmount;
        }

        public BigDecimal getAccumRealizedIncomeRmb() {
            return accumRealizedIncomeRmb;
        }

        public void setAccumRealizedIncomeRmb(BigDecimal accumRealizedIncomeRmb) {
            this.accumRealizedIncomeRmb = accumRealizedIncomeRmb;
        }

        public BigDecimal getCashCollection() {
            return cashCollection;
        }

        public void setCashCollection(BigDecimal cashCollection) {
            this.cashCollection = cashCollection;
        }

        public BigDecimal getAccumIncome() {
            return accumIncome;
        }

        public void setAccumIncome(BigDecimal accumIncome) {
            this.accumIncome = accumIncome;
        }

        public BigDecimal getAccumIncomeRmb() {
            return accumIncomeRmb;
        }

        public void setAccumIncomeRmb(BigDecimal accumIncomeRmb) {
            this.accumIncomeRmb = accumIncomeRmb;
        }

        public String getProductSubType() {
            return productSubType;
        }

        public void setProductSubType(String productSubType) {
            this.productSubType = productSubType;
        }

        public String getRePurchaseFlag() {
            return rePurchaseFlag;
        }

        public void setRePurchaseFlag(String rePurchaseFlag) {
            this.rePurchaseFlag = rePurchaseFlag;
        }

        public String getBenchmark() {
            return benchmark;
        }

        public void setBenchmark(String benchmark) {
            this.benchmark = benchmark;
        }

        public String getValueDate() {
            return valueDate;
        }

        public void setValueDate(String valueDate) {
            this.valueDate = valueDate;
        }

        public String getDueDate() {
            return dueDate;
        }

        public void setDueDate(String dueDate) {
            this.dueDate = dueDate;
        }

        public String getStandardFixedIncomeFlag() {
            return standardFixedIncomeFlag;
        }

        public void setStandardFixedIncomeFlag(String standardFixedIncomeFlag) {
            this.standardFixedIncomeFlag = standardFixedIncomeFlag;
        }

        public String getInvestmentHorizon() {
            return investmentHorizon;
        }

        public void setInvestmentHorizon(String investmentHorizon) {
            this.investmentHorizon = investmentHorizon;
        }

        public String getCooperation() {
            return cooperation;
        }

        public void setCooperation(String cooperation) {
            this.cooperation = cooperation;
        }

        public String getCrisisFlag() {
            return crisisFlag;
        }

        public void setCrisisFlag(String crisisFlag) {
            this.crisisFlag = crisisFlag;
        }

        public BigDecimal getYieldIncome() {
            return yieldIncome;
        }

        public void setYieldIncome(BigDecimal yieldIncome) {
            this.yieldIncome = yieldIncome;
        }

        public String getYieldIncomeDt() {
            return yieldIncomeDt;
        }

        public void setYieldIncomeDt(String yieldIncomeDt) {
            this.yieldIncomeDt = yieldIncomeDt;
        }

        public BigDecimal getCopiesIncome() {
            return copiesIncome;
        }

        public void setCopiesIncome(BigDecimal copiesIncome) {
            this.copiesIncome = copiesIncome;
        }

        public String getBenchmarkType() {
            return benchmarkType;
        }

        public void setBenchmarkType(String benchmarkType) {
            this.benchmarkType = benchmarkType;
        }

        public String getHwSaleFlag() {
            return hwSaleFlag;
        }

        public void setHwSaleFlag(String hwSaleFlag) {
            this.hwSaleFlag = hwSaleFlag;
        }

        public String getRegDt() {
            return regDt;
        }

        public void setRegDt(String regDt) {
            this.regDt = regDt;
        }

        public String getOneStepType() {
            return oneStepType;
        }

        public void setOneStepType(String oneStepType) {
            this.oneStepType = oneStepType;
        }

        public String getTwoStepType() {
            return twoStepType;
        }

        public void setTwoStepType(String twoStepType) {
            this.twoStepType = twoStepType;
        }

        public String getSecondStepType() {
            return secondStepType;
        }

        public void setSecondStepType(String secondStepType) {
            this.secondStepType = secondStepType;
        }

        public String getProductSaleType() {
            return productSaleType;
        }

        public void setProductSaleType(String productSaleType) {
            this.productSaleType = productSaleType;
        }

        public BigDecimal getReceivManageFee() {
            return receivManageFee;
        }

        public void setReceivManageFee(BigDecimal receivManageFee) {
            this.receivManageFee = receivManageFee;
        }

        public String getNaProductFeeType() {
            return naProductFeeType;
        }

        public void setNaProductFeeType(String naProductFeeType) {
            this.naProductFeeType = naProductFeeType;
        }

        public BigDecimal getReceivPreformFee() {
            return receivPreformFee;
        }

        public void setReceivPreformFee(BigDecimal receivPreformFee) {
            this.receivPreformFee = receivPreformFee;
        }

        public BigDecimal getCurrencyMarketValueExFee() {
            return currencyMarketValueExFee;
        }

        public void setCurrencyMarketValueExFee(BigDecimal currencyMarketValueExFee) {
            this.currencyMarketValueExFee = currencyMarketValueExFee;
        }

        public BigDecimal getMarketValueExFee() {
            return marketValueExFee;
        }

        public void setMarketValueExFee(BigDecimal marketValueExFee) {
            this.marketValueExFee = marketValueExFee;
        }

        public BigDecimal getBalanceIncomeNew() {
            return balanceIncomeNew;
        }

        public void setBalanceIncomeNew(BigDecimal balanceIncomeNew) {
            this.balanceIncomeNew = balanceIncomeNew;
        }

        public BigDecimal getBalanceIncomeNewRmb() {
            return balanceIncomeNewRmb;
        }

        public void setBalanceIncomeNewRmb(BigDecimal balanceIncomeNewRmb) {
            this.balanceIncomeNewRmb = balanceIncomeNewRmb;
        }

        public BigDecimal getAccumIncomeNew() {
            return accumIncomeNew;
        }

        public void setAccumIncomeNew(BigDecimal accumIncomeNew) {
            this.accumIncomeNew = accumIncomeNew;
        }

        public BigDecimal getAccumIncomeNewRmb() {
            return accumIncomeNewRmb;
        }

        public void setAccumIncomeNewRmb(BigDecimal accumIncomeNewRmb) {
            this.accumIncomeNewRmb = accumIncomeNewRmb;
        }

        public int compareTo(BalanceBean bean) {
            return this.productCode.compareTo(bean.productCode);
        }

        public List<String> getDisCodeList() {
            return disCodeList;
        }

        public void setDisCodeList(List<String> disCodeList) {
            this.disCodeList = disCodeList;
        }

        public BigDecimal getBalanceFactor() {
            return balanceFactor;
        }

        public void setBalanceFactor(BigDecimal balanceFactor) {
            this.balanceFactor = balanceFactor;
        }

        public String getConvertFinish() {
            return convertFinish;
        }

        public void setConvertFinish(String convertFinish) {
            this.convertFinish = convertFinish;
        }

        public String getBalanceFactorDate() {
            return balanceFactorDate;
        }

        public void setBalanceFactorDate(String balanceFactorDate) {
            this.balanceFactorDate = balanceFactorDate;
        }

        public BigDecimal getYieldRate() {
            return yieldRate;
        }

        public void setYieldRate(BigDecimal yieldRate) {
            this.yieldRate = yieldRate;
        }

        public String getSxz() {
            return sxz;
        }

        public void setSxz(String sxz) {
            this.sxz = sxz;
        }
    }

    public List<String> getDisCodeList() {
        return disCodeList;
    }

    public void setDisCodeList(List<String> disCodeList) {
        this.disCodeList = disCodeList;
    }


    /**
     * 交易订单
     */
    public static class DealOrderBean implements Serializable {

        private static final long serialVersionUID = -6029033253173281380L;
        /**
         * 订单号
         */
        private String dealNo;

        /**
         * 订单类型：0-直销、1-代销
         */
        private String dealType;

        public String getDealNo() {
            return dealNo;
        }

        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }

        public String getDealType() {
            return dealType;
        }

        public void setDealType(String dealType) {
            this.dealType = dealType;
        }
    }
}
