package com.howbuy.tms.high.orders.service.facade.search.queryCustomerFundSubsAmt;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryacccustinfo.bean.AccCustInfoBean;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.CustomerFundSubsAmtInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtFacade;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtRequest;
import com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtResponse;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:查询用户产品认缴金额信息
 * @Author: yun.lu
 * Date: 2024/7/15 19:02
 */
@DubboService
@Service("queryCustomerFundSubsAmtFacade")
@Slf4j
public class QueryCustomerFundSubsAmtService implements QueryCustomerFundSubsAmtFacade {
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryCustomerFundSubsAmtInfo.QueryCustomerFundSubsAmtFacade.execute(QueryCustomerFundSubsAmtRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryCustomerFundSubsAmtService
     * @apiName execute
     * @apiDescription 查询用户产品认缴金额信息
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=U3frFM&pageSize=9169&disCode=5s33YzynS&txChannel=30r3&appTm=eI6FZic&fundCode=Nwrq&subOutletCode=2yeuNOa&pageNo=5742&operIp=KIh&txAcctNo=B6&appDt=PD&dataTrack=MiUp&txCode=yLwHTr8aW3&outletCode=Tl
     * @apiSuccess (响应结果) {Array} customerFundSubsAmtInfoDtoList 用户产品认缴金额信息
     * @apiSuccess (响应结果) {String} customerFundSubsAmtInfoDtoList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} customerFundSubsAmtInfoDtoList.custName 客户姓名
     * @apiSuccess (响应结果) {String} customerFundSubsAmtInfoDtoList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} customerFundSubsAmtInfoDtoList.fundName 基金名
     * @apiSuccess (响应结果) {Number} customerFundSubsAmtInfoDtoList.subsAmt 认缴金额
     * @apiSuccess (响应结果) {Number} customerFundSubsAmtInfoDtoList.balanceVol 持仓份额
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"eKt","totalPage":2951,"pageNo":1001,"description":"qjUtB","totalCount":8689,"customerFundSubsAmtInfoDtoList":[{"fundCode":"f8Vk1AIX","txAcctNo":"Qn","subsAmt":4521.812561353323,"balanceVol":2838.741476095912,"custName":"IS","fundName":"yJzcqK"}]}
     */
    @Override
    public QueryCustomerFundSubsAmtResponse execute(QueryCustomerFundSubsAmtRequest request) {
        QueryCustomerFundSubsAmtResponse response = new QueryCustomerFundSubsAmtResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 1.参数校验
        checkRequest(request, response);
        if (!ExceptionCodes.SUCCESS.equals(response.getReturnCode())) {
            log.info("查询用户产品认缴金额信息,入参校验不通过,request={}", JSON.toJSONString(request));
            return response;
        }
        // 2.查询出该客户已持有的好臻分销且分次call产品且未实缴完成（实缴金额小于认缴金额），包括：客户号、客户姓名、基金代码、基金简称、认缴金额（千分位展示）、持仓份额（千分位展示）
        // 2.1.查询客户认缴的信息
        List<SubscribeAmtDetailPo> subscribeAmtDetailPoList = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getFundCode())) {
            SubscribeAmtDetailPo subscribeAmtDetail = subscribeAmtDetailRepository.getSubscribeAmtDetail(request.getTxAcctNo(), request.getFundCode());
            subscribeAmtDetailPoList.add(subscribeAmtDetail);
        } else {
            subscribeAmtDetailPoList = subscribeAmtDetailRepository.queryByTxAcctNo(request.getTxAcctNo());
        }
        if (CollectionUtils.isEmpty(subscribeAmtDetailPoList)) {
            log.info("该用户没有认缴信息,txAcctNo={}", request.getTxAcctNo());
            return response;
        }
        // 2.2.过滤出未完成的认缴
        List<SubscribeAmtDetailPo> unFinishList = subscribeAmtDetailPoList.stream().filter(x -> x.getPaidAmt() != null && x.getSubscribeAmt() != null && x.getSubscribeAmt().compareTo(x.getPaidAmt()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unFinishList)) {
            log.info("该用户没有未结束的认缴信息,txAcctNo={}", request.getTxAcctNo());
            return response;
        }
        // 2.3.过滤出对应分销的分次call产品
        boolean needCheckDisCode = !StringUtils.isNotBlank(request.getFundCode());
        List<String> fundCodeList = unFinishList.stream().map(SubscribeAmtDetailPo::getFundCode).distinct().collect(Collectors.toList());
        Map<String, HighProductBaseInfoModel> highProductBaseInfoMap = queryHighProductOuterService.getHighProductBaseInfoMap(fundCodeList);
        List<SubscribeAmtDetailPo> validList = unFinishList.stream().filter(x -> checkProduct(x.getFundCode(), highProductBaseInfoMap, request.getDisCode(), needCheckDisCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validList)) {
            log.info("过滤掉非指定分销,非分次cll后,没有符合要求的数据,txAcctNo={}", request.getTxAcctNo());
            return response;
        }
        // 3.查询持仓信息
        List<String> validFundCodeList = validList.stream().map(SubscribeAmtDetailPo::getFundCode).distinct().collect(Collectors.toList());
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setFundCodeList(validFundCodeList);
        queryAcctBalanceBaseParam.setIncludeDirect(false);
        queryAcctBalanceBaseParam.setTxAcctNo(request.getTxAcctNo());
        List<AcctBalanceBaseInfo> balanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(queryAcctBalanceBaseParam);
        // 4.构建返回实体
        List<CustomerFundSubsAmtInfoDto> customerFundSubsAmtInfoDtoList = buildCustomerFundSubsAmtInfoDtoList(balanceBaseInfoList, validList, highProductBaseInfoMap);
        response.setCustomerFundSubsAmtInfoDtoList(customerFundSubsAmtInfoDtoList);
        return response;
    }

    /**
     * 构建用户产品认缴金额信息实体
     *
     * @param balanceBaseInfoList 持仓信息
     * @param validList           认缴金额信息
     * @return 构建用户产品认缴金额信息实体
     */
    private List<CustomerFundSubsAmtInfoDto> buildCustomerFundSubsAmtInfoDtoList(List<AcctBalanceBaseInfo> balanceBaseInfoList, List<SubscribeAmtDetailPo> validList, Map<String, HighProductBaseInfoModel> highProductBaseInfoMap) {
        AccCustInfoBean queryTxAcctByHboneResult = queryHbOneNoOuterService.queryCustBaseInfoByTxAcctNo(validList.get(0).getTxAcctNo());
        HashMap<String, BigDecimal> balanceVolMap = new HashMap<>();
        for (AcctBalanceBaseInfo acctBalanceBaseInfo : balanceBaseInfoList) {
            balanceVolMap.put(acctBalanceBaseInfo.getFundCode(), acctBalanceBaseInfo.getBalanceVol());
        }
        List<CustomerFundSubsAmtInfoDto> customerFundSubsAmtInfoDtoList = new ArrayList<>();
        for (SubscribeAmtDetailPo subscribeAmtDetailPo : validList) {
            HighProductBaseInfoModel highProductBaseInfoModel = highProductBaseInfoMap.get(subscribeAmtDetailPo.getFundCode());
            CustomerFundSubsAmtInfoDto customerFundSubsAmtInfoDto = new CustomerFundSubsAmtInfoDto();
            customerFundSubsAmtInfoDto.setSubsAmt(subscribeAmtDetailPo.getSubscribeAmt());
            customerFundSubsAmtInfoDto.setFundCode(subscribeAmtDetailPo.getFundCode());
            customerFundSubsAmtInfoDto.setFundName(highProductBaseInfoModel.getFundName());
            customerFundSubsAmtInfoDto.setTxAcctNo(subscribeAmtDetailPo.getTxAcctNo());
            customerFundSubsAmtInfoDto.setCustName(queryTxAcctByHboneResult.getCustName());
            customerFundSubsAmtInfoDto.setBalanceVol(balanceVolMap.get(subscribeAmtDetailPo.getFundCode()));
            customerFundSubsAmtInfoDtoList.add(customerFundSubsAmtInfoDto);
        }
        return customerFundSubsAmtInfoDtoList;
    }

    /**
     * 产品是否符合查询需求,分销是否对,是否是否是分次call产品
     *
     * @param fundCode
     * @param highProductBaseInfoMap
     * @param disCode
     * @return
     */
    private boolean checkProduct(String fundCode, Map<String, HighProductBaseInfoModel> highProductBaseInfoMap, String disCode, boolean needCheckDisCode) {
        if (highProductBaseInfoMap == null) {
            return false;
        }
        HighProductBaseInfoModel highProductBaseInfoModel = highProductBaseInfoMap.get(fundCode);
        if (highProductBaseInfoModel == null) {
            return false;
        }
        if (StringUtils.isBlank(highProductBaseInfoModel.getDisCode()) || StringUtils.isBlank(highProductBaseInfoModel.getPeDivideCallFlag())) {
            return false;
        }
        if (needCheckDisCode && !disCode.equals(highProductBaseInfoModel.getDisCode())) {
            return false;
        }
        if (!YesOrNoEnum.YES.getCode().equals(highProductBaseInfoModel.getPeDivideCallFlag())) {
            return false;
        }
        return true;
    }

    /**
     * 校验入参
     *
     * @param request  请求入参
     * @param response 响应结果
     */
    private void checkRequest(QueryCustomerFundSubsAmtRequest request, QueryCustomerFundSubsAmtResponse response) {
        if (StringUtils.isBlank(request.getTxAcctNo())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("交易账号不能为空");
            return;
        }
        if (StringUtils.isBlank(request.getDisCode())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("分销编码与产品编码不能同时为空");
        }
    }
}
