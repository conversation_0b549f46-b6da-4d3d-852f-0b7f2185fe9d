package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:代销用户交易记录
 * @Author: yun.lu
 * Date: 2023/5/25 19:14
 */
@Getter
@Setter
public class ConsignmentOrderInfo extends OwnershipOrderInfo {

    /**
     * 订单号
     */
    private String dealDtlNo;

    public ConsignmentOrderInfo(BalanceOrderVo balanceOrderVo, String transferMBusinessCode) {
        super(balanceOrderVo, transferMBusinessCode);
        this.dealDtlNo = balanceOrderVo.getOrderNo();
        this.setOrderNo(dealDtlNo);
    }
}
