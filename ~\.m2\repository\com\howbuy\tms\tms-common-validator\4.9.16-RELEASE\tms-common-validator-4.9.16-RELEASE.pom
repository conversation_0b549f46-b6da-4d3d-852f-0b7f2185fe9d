<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.howbuy.tms</groupId>
	<artifactId>tms-common-validator</artifactId>
	<version>4.9.16-RELEASE</version>
	<packaging>jar</packaging>

	<name>tms-common-validator</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<java.version>1.8</java.version>
		<spring.version>3.2.16.RELEASE</spring.version>
		<log4j.version>2.17.0</log4j.version>
		<ccms.version>1.0-SNAPSHOT</ccms.version>
		<spring.version>3.2.16.RELEASE</spring.version>
		<fastjson.version>1.2.17</fastjson.version>
		<dubbo.version>2.5.3</dubbo.version>
		<zkclient.version>0.1</zkclient.version>
		<cache.version>1.2.1-RELEASE</cache.version>
		<log4j.version>2.17.0</log4j.version>
		<zookeeper.version>3.4.6</zookeeper.version>
		<cluster4spring.version>0.85</cluster4spring.version>

		<payonline.version>RELEASE</payonline.version>
		<ftxonline.version>1.2.7-RELEASE</ftxonline.version>
		<tp.common.facade.version>RELEASE</tp.common.facade.version>
		<ftxonline.search.version>1.2.7-RELEASE</ftxonline.search.version>
		<fdsonline.version>RELEASE</fdsonline.version>
		<fbsonline.version>3.3.4-RELEASE</fbsonline.version>
		<fbsonline.search.version>RELEASE</fbsonline.search.version>
		<acccenter.version>RELEASE</acccenter.version>
		<ftxconsole.version>RELEASE</ftxconsole.version>
		<interlayer.version>1.0.0-release</interlayer.version>
		<com.howbuy.tms-common-service.version>4.9.16-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.tms-common-outerservice.version>4.9.16-RELEASE</com.howbuy.tms-common-outerservice.version>
		<com.howbuy.tms-common-enums.version>4.9.16-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
		<com.howbuy.pay-online-facade.version>20250729-RELEASE</com.howbuy.pay-online-facade.version>
		<com.howbuy.ftx-order-facade.version>2.1.1-RELEASE</com.howbuy.ftx-order-facade.version>
		<com.howbuy.fbs-online-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-facade.version>
		<com.howbuy.fbs-online-search-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-search-facade.version>
		<com.howbuy.acc-center-facade.version>20250702-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.ftx-console-facade.version>1.1.32-RELEASE</com.howbuy.ftx-console-facade.version>
		<com.howbuy.product-center.version>4.8.59-RELEASE</com.howbuy.product-center.version>
		<com.howbuy.product-center-model.version>4.8.59-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.product-center-client.version>4.8.59-RELEASE</com.howbuy.product-center-client.version>
	</properties>

	<dependencies>

		<!--核心log4j2jar包 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<!--用于与slf4j保持桥接 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.2</version>
		</dependency>

		<!-- log4j2 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>

		<!-- HOWBUY -->
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-service</artifactId>
			<version>${com.howbuy.tms-common-service.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-outerservice</artifactId>
			<version>${com.howbuy.tms-common-outerservice.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
			<version>${com.howbuy.tms-common-enums.version}</version>
		</dependency>
		<!-- 外部系统包 -->
		<!-- TP依赖包 -->
		<dependency>
			<groupId>com.howbuy.common</groupId>
			<artifactId>common-facade</artifactId>
			<version>${com.howbuy.common-facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.payonline</groupId>
			<artifactId>pay-online-facade</artifactId>
			<version>${com.howbuy.pay-online-facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>ftx-order-facade</artifactId>
			<version>${com.howbuy.ftx-order-facade.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.fbs</groupId>
			<artifactId>fbs-online-facade</artifactId>
			<version>${com.howbuy.fbs-online-facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.fbs</groupId>
			<artifactId>fbs-online-search-facade</artifactId>
			<version>${com.howbuy.fbs-online-search-facade.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-facade</artifactId>
			<version>${com.howbuy.acc-center-facade.version}</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>ftx-console-facade</artifactId>
			<version>${com.howbuy.ftx-console-facade.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-client</artifactId>
			<version>${com.howbuy.product-center-client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-model</artifactId>
			<version>${com.howbuy.product-center-model.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>HowbuyServiceBus</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>HowbuyServiceCommon</artifactId>
			<version>1.0.1</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<!-- Java源码插件 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>

				<executions>
					<execution>
						<id>attach-source</id>
						<phase>install</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>
	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

</project>