<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.otc</groupId>
		<artifactId>otc-common</artifactId>
		<version>7.9.3-RELEASE</version>
	</parent>
	<artifactId>otc-common-api</artifactId>
	<packaging>jar</packaging>

	<properties>
		<hibernate-validator.version>6.1.7.Final</hibernate-validator.version>
	</properties>
	<dependencies>
		<dependency>
			<artifactId>howbuy-commons-validator</artifactId>
			<groupId>com.howbuy.commons.validator</groupId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-annotation</artifactId>
			<version>3.4.2</version>
		</dependency>
		<!--JSR303 Bean校验接口规范 依赖包 同springboot-2.3.12版本依赖的hibernate-validator保持一致-->
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>${hibernate-validator.version}</version>
		</dependency>
	</dependencies>
</project>