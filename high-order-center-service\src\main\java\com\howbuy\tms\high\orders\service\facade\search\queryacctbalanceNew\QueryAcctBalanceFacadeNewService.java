/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.BalanceBeanVO;
import com.howbuy.dtms.order.client.domain.UnconfirmeProductVO;
import com.howbuy.dtms.order.client.domain.response.cash.QueryCashBalanceByFundTxAcctResponse;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade;
import com.howbuy.dtms.order.client.facade.query.cashbalance.QueryCashBalanceByFundTxAcctFacade;
import com.howbuy.interlayer.product.model.HighProductDBInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade;
import com.howbuy.tms.high.orders.service.task.*;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 查询用户持仓,海外的转包海外接口,其他的查询之前的接口
 */
@DubboService
@Service("queryAcctBalanceNewFacade")
@Slf4j
public class QueryAcctBalanceFacadeNewService implements QueryAcctBalanceNewFacade {
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryAcctBalanceWithoutHkFacade queryAcctBalanceWithoutHkFacade;
    @Autowired
    private QueryBalanceFacade queryBalanceFacade;
    @Autowired
    private QueryCashBalanceByFundTxAcctFacade queryCashBalanceByFundTxAcctFacade;
    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Value("${query.balance.by.old:0}")
    private String queryBalanceByOld;
    @Value("${query.balance.by.old.hboneNos}")
    private String queryBalanceByOldHboneNos;

    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
        log.info("QueryAcctBalanceNewFacade-查询新持仓接口,request={}", JSON.toJSONString(request));
        if (YesOrNoEnum.YES.getCode().equals(queryBalanceByOld)) {
            log.info("持仓接口,切开关,只查询老接口,queryBalanceByOld={}", queryBalanceByOld);
            return queryAcctBalanceFacade.execute(request);
        }
        if (StringUtils.isBlank(request.getHbOneNo()) || StringUtils.isNotBlank(queryBalanceByOldHboneNos) && queryBalanceByOldHboneNos.contains(request.getHbOneNo())) {
            log.info("持仓接口,白名单用户或者一账通为空,只查询老接口,queryBalanceByOldHboneNos={}", queryBalanceByOldHboneNos);
            return queryAcctBalanceFacade.execute(request);
        }
        // 是否只需要查询香港产品
        Boolean hkProduct = null;
        if (StringUtils.isNotBlank(request.getProductCode())) {
            hkProduct = isHkProduct(request.getProductCode());
        }
        if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.YES.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = true;
        } else if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = false;
        }
        // 1.查询非海外的持仓
        List<HowbuyBaseTask> taskList = new ArrayList<>(3);
        QueryBalanceContext queryBalanceContext = new QueryBalanceContext();
        taskList.add(new QueryUnHkBalanceTask(queryBalanceContext, request, hkProduct, queryAcctBalanceWithoutHkFacade));
        // 2.查询海外持仓
        taskList.add(new QueryHkBalanceTask(request, hkProduct, queryBalanceContext, queryBalanceFacade));
        // 3.查询现金余额
        taskList.add(new QueryCashBalanceTask(request, queryBalanceContext, hkProduct, queryCashBalanceByFundTxAcctFacade));
        howBuyRunTaskUil.runTask(taskList);

        // 4.数据汇总
        return buildTotalBalanceInfo(queryBalanceContext);
    }

    /***
     * 设置汇总数据
     * @param queryBalanceContext 持仓信息
     */
    private QueryAcctBalanceResponse buildTotalBalanceInfo(QueryBalanceContext queryBalanceContext) {
        QueryAcctBalanceResponse totalBalance = new QueryAcctBalanceResponse();
        QueryBalanceResponse hkBalance = queryBalanceContext.getHkBalance();
        QueryAcctBalanceResponse unHkBalance = queryBalanceContext.getUnHkBalance();
        QueryCashBalanceByFundTxAcctResponse cashBalance = queryBalanceContext.getCashBalance();
        totalBalance.setReturnCode(unHkBalance.getReturnCode());
        totalBalance.setDescription(unHkBalance.getDescription());
        // 3.0.是否有海外产品
        if (CollectionUtils.isNotEmpty(hkBalance.getBalanceList()) || cashBalance.getTotalExchangeCashBalance() != null && cashBalance.getTotalExchangeCashBalance().compareTo(BigDecimal.ZERO) > 0) {
            totalBalance.setHasHKProduct(YesOrNoEnum.YES.getCode());
        } else {
            totalBalance.setHasHKProduct(YesOrNoEnum.NO.getCode());
        }
        // 3.1.总市值
        BigDecimal unHkTotalMarketValue = unHkBalance.getTotalMarketValue() == null ? BigDecimal.ZERO : unHkBalance.getTotalMarketValue();
        BigDecimal hkTotalMarketValue = hkBalance.getDisPlayCurrencyTotalMarketValue() == null ? BigDecimal.ZERO : hkBalance.getDisPlayCurrencyTotalMarketValue();
        BigDecimal totalExchangeCashBalance = cashBalance.getTotalExchangeCashBalance() == null ? BigDecimal.ZERO : cashBalance.getTotalExchangeCashBalance();
        totalBalance.setTotalMarketValue(unHkTotalMarketValue.add(hkTotalMarketValue).add(totalExchangeCashBalance));
        // 香港展示总市值
        totalBalance.setDisPlayHkTotalMarketValue(hkTotalMarketValue);
        // 3.2.总未确认金额
        BigDecimal unHkTotalUnconfirmedAmt = unHkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : unHkBalance.getTotalUnconfirmedAmt();
        BigDecimal hkTotalUnconfirmedAmt = hkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : hkBalance.getTotalUnconfirmedAmt();
        totalBalance.setTotalUnconfirmedAmt(unHkTotalUnconfirmedAmt.add(hkTotalUnconfirmedAmt));
        // 3.3.总未确认数量
        int unHkTotalUnconfirmedNum = unHkBalance.getTotalUnconfirmedNum() == null ? 0 : unHkBalance.getTotalUnconfirmedNum();
        int hkTotalUnconfirmedNum = hkBalance.getTotalUnconfirmedNum() == null ? 0 : hkBalance.getTotalUnconfirmedNum();
        totalBalance.setTotalUnconfirmedNum(unHkTotalUnconfirmedNum + hkTotalUnconfirmedNum);
        // 3.4.赎回未确认数量
        int unHkRedeemUnconfirmedNum = unHkBalance.getRedeemUnconfirmedNum() == null ? 0 : unHkBalance.getRedeemUnconfirmedNum();
        int hkRedeemUnconfirmedNum = hkBalance.getRedeemUnconfirmedNum() == null ? 0 : hkBalance.getRedeemUnconfirmedNum();
        totalBalance.setRedeemUnconfirmedNum(unHkRedeemUnconfirmedNum + hkRedeemUnconfirmedNum);
        // 3.5.当前总收益
        BigDecimal unHkTotalCurrentAsset = unHkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : unHkBalance.getTotalCurrentAsset();
        BigDecimal hkTotalCurrentAsset = hkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : hkBalance.getTotalCurrentAsset();
        totalBalance.setTotalCurrentAsset(unHkTotalCurrentAsset.add(hkTotalCurrentAsset));
        // 3.6.总收益计算状态: 0-计算中;1-计算成功
        String unHkTotalIncomCalStat = unHkBalance.getTotalIncomCalStat() == null ? YesOrNoEnum.YES.getCode() : unHkBalance.getTotalIncomCalStat();
        // 海外没有该字段,都是计算成功
        totalBalance.setTotalIncomCalStat(YesOrNoEnum.NO.getCode().equals(unHkTotalIncomCalStat) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        // 3.7.总回款
        BigDecimal unHkTotalCashCollection = unHkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : unHkBalance.getTotalCashCollection();
        BigDecimal hkTotalCashCollection = hkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : hkBalance.getTotalCashCollection();
        totalBalance.setTotalCashCollection(unHkTotalCashCollection.add(hkTotalCashCollection));
        // 3.8.现金余额
        totalBalance.setTotalExchangeCashBalance(cashBalance.getTotalExchangeCashBalance());
        totalBalance.setTotalExchangeFrozenAmt(cashBalance.getTotalExchangeFrozenAmt());
        totalBalance.setTotalExchangeAvailableBalance(cashBalance.getTotalExchangeAvailableBalance());

        // 3.9.是否有好臻产品
        totalBalance.setHasHZProduct(unHkBalance.getHasHZProduct());
        // 3.10.总未确认产品列表
        List<UnconfirmeProduct> unConfirmedProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unHkBalance.getUnconfirmeProducts())) {
            unConfirmedProducts.addAll(unHkBalance.getUnconfirmeProducts());
        }
        if (CollectionUtils.isNotEmpty(hkBalance.getUnconfirmeProducts())) {
            for (UnconfirmeProductVO unConfirmedProduct : hkBalance.getUnconfirmeProducts()) {
                UnconfirmeProduct unconfirmeProduct = getUnconfirmeProduct(unConfirmedProduct);
                unConfirmedProducts.add(unconfirmeProduct);
            }
        }
        totalBalance.setUnconfirmeProducts(unConfirmedProducts);
        // 3.11.持仓信息汇总
        List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList = new ArrayList<>();
        List<QueryAcctBalanceResponse.BalanceBean> unHkBalanceList = unHkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(unHkBalanceList)) {
            totalBalanceList.addAll(unHkBalanceList);
        }
        List<BalanceBeanVO> hkBalanceList = hkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(hkBalanceList)) {
            for (BalanceBeanVO balanceBeanVO : hkBalanceList) {
                QueryAcctBalanceResponse.BalanceBean balanceBean = buildHkBalance(balanceBeanVO);
                totalBalanceList.add(balanceBean);
            }
        }
        totalBalance.setBalanceList(totalBalanceList);
        return totalBalance;
    }

    /**
     * 待确认的基金信息
     *
     * @param unConfirmedProduct
     * @return
     */
    private UnconfirmeProduct getUnconfirmeProduct(UnconfirmeProductVO unConfirmedProduct) {
        UnconfirmeProduct unconfirmeProduct = new UnconfirmeProduct();
        unconfirmeProduct.setFundCode(unConfirmedProduct.getFundCode());
        unconfirmeProduct.setProductType(unConfirmedProduct.getProductType());
        unconfirmeProduct.setProductSubType(unConfirmedProduct.getProductSubType());
        unconfirmeProduct.setUnconfirmedAmt(unConfirmedProduct.getUnconfirmedAmt());
        unconfirmeProduct.setHkSaleFlag(unConfirmedProduct.getHkSaleFlag());
        unconfirmeProduct.setDisCode(unConfirmedProduct.getDisCode());
        return unconfirmeProduct;
    }

    /**
     * 构建海外持仓信息
     */
    private QueryAcctBalanceResponse.BalanceBean buildHkBalance(BalanceBeanVO balanceBeanVO) {
        QueryAcctBalanceResponse.BalanceBean balanceBean = new QueryAcctBalanceResponse.BalanceBean();
        // Replace BeanUtils.copyProperties with explicit setters
        balanceBean.setDisCode(balanceBeanVO.getDisCode());
        balanceBean.setDisCodeList(balanceBeanVO.getDisCodeList());
        balanceBean.setProductCode(balanceBeanVO.getProductCode());
        balanceBean.setSubProductCode(balanceBeanVO.getSubProductCode());
        balanceBean.setProductName(balanceBeanVO.getProductName());
        balanceBean.setProductType(balanceBeanVO.getProductType());
        balanceBean.setProductSubType(balanceBeanVO.getProductSubType());
        balanceBean.setBalanceVol(balanceBeanVO.getBalanceVol());
        balanceBean.setUnconfirmedVol(balanceBeanVO.getUnconfirmedVol());
        balanceBean.setUnconfirmedAmt(balanceBeanVO.getUnconfirmedAmt());
        balanceBean.setCurrency(balanceBeanVO.getCurrency());
        balanceBean.setNav(balanceBeanVO.getNav());
        balanceBean.setNavDt(balanceBeanVO.getNavDt());
        balanceBean.setNavDivFlag(balanceBeanVO.getNavDivFlag());
        balanceBean.setMarketValue(balanceBeanVO.getMarketValue());
        balanceBean.setCurrencyMarketValue(balanceBeanVO.getCurrencyMarketValue());
        balanceBean.setScaleType(balanceBeanVO.getScaleType());
        balanceBean.setHkSaleFlag(balanceBeanVO.getHkSaleFlag());
        balanceBean.setStageEstablishFlag(balanceBeanVO.getStageEstablishFlag());
        balanceBean.setFractionateCallFlag(balanceBeanVO.getFractionateCallFlag());
        balanceBean.setFundCXQXStr(balanceBeanVO.getFundCXQXStr());
        balanceBean.setNetBuyAmount(balanceBeanVO.getNetBuyAmount());
        balanceBean.setCurrencyNetBuyAmount(balanceBeanVO.getCurrencyNetBuyAmount());
        balanceBean.setIncomeDt(balanceBeanVO.getIncomeDt());
        balanceBean.setIncomeCalStat(balanceBeanVO.getIncomeCalStat());
        balanceBean.setCurrentAsset(balanceBeanVO.getCurrentAsset());
        balanceBean.setCurrentAssetCurrency(balanceBeanVO.getCurrentAssetCurrency());
        balanceBean.setAccumIncome(balanceBeanVO.getAccumIncome());
        balanceBean.setAccumIncomeRmb(balanceBeanVO.getAccumIncomeRmb());
        balanceBean.setAccumRealizedIncome(balanceBeanVO.getAccumRealizedIncome());
        balanceBean.setAccumRealizedIncomeRmb(balanceBeanVO.getAccumRealizedIncomeRmb());
        balanceBean.setRePurchaseFlag(balanceBeanVO.getRePurchaseFlag());
        balanceBean.setBenchmark(balanceBeanVO.getBenchmark());
        balanceBean.setBenchmarkType(balanceBeanVO.getBenchmarkType());
        balanceBean.setValueDate(balanceBeanVO.getValueDate());
        balanceBean.setDueDate(balanceBeanVO.getDueDate());
        balanceBean.setStandardFixedIncomeFlag(balanceBeanVO.getStandardFixedIncomeFlag());
        balanceBean.setInvestmentHorizon(balanceBeanVO.getInvestmentHorizon());
        balanceBean.setCooperation(balanceBeanVO.getCooperation());
        balanceBean.setCrisisFlag(balanceBeanVO.getCrisisFlag());
        balanceBean.setYieldIncome(balanceBeanVO.getYieldIncome());
        balanceBean.setYieldIncomeDt(balanceBeanVO.getYieldIncomeDt());
        balanceBean.setCopiesIncome(balanceBeanVO.getCopiesIncome());
        balanceBean.setHwSaleFlag(balanceBeanVO.getHwSaleFlag());
        balanceBean.setRegDt(balanceBeanVO.getRegDt());
        balanceBean.setOneStepType(balanceBeanVO.getOneStepType());
        balanceBean.setTwoStepType(balanceBeanVO.getTwoStepType());
        balanceBean.setSecondStepType(balanceBeanVO.getSecondStepType());
        balanceBean.setProductSaleType(balanceBeanVO.getProductSaleType());
        balanceBean.setNaProductFeeType(balanceBeanVO.getNaProductFeeType());
        balanceBean.setReceivManageFee(balanceBeanVO.getReceivManageFee());
        balanceBean.setReceivPreformFee(balanceBeanVO.getReceivPreformFee());
        balanceBean.setCurrencyMarketValueExFee(balanceBeanVO.getCurrencyMarketValueExFee());
        balanceBean.setMarketValueExFee(balanceBeanVO.getMarketValueExFee());
        balanceBean.setBalanceIncomeNew(balanceBeanVO.getBalanceIncomeNew());
        balanceBean.setBalanceIncomeNewRmb(balanceBeanVO.getBalanceIncomeNewRmb());
        balanceBean.setBalanceFactor(balanceBeanVO.getBalanceFactor());
        balanceBean.setConvertFinish(balanceBeanVO.getConvertFinish());
        balanceBean.setBalanceFactorDate(balanceBeanVO.getBalanceFactorDate());
        balanceBean.setYieldRate(balanceBeanVO.getYieldRate());
        balanceBean.setAccumIncomeNew(balanceBeanVO.getAccumIncomeNew());
        balanceBean.setAccumIncomeNewRmb(balanceBeanVO.getAccumIncomeNewRmb());
        balanceBean.setBalanceCost(balanceBeanVO.getBalanceCost());
        balanceBean.setBalanceCostCurrency(balanceBeanVO.getBalanceCostCurrency());
        balanceBean.setDailyAsset(balanceBeanVO.getDailyAsset());
        balanceBean.setDailyAssetCurrency(balanceBeanVO.getDailyAssetCurrency());
        balanceBean.setCashCollection(balanceBeanVO.getCashCollection());
        balanceBean.setCurrencyCashCollection(balanceBeanVO.getCurrencyCashCollection());
        balanceBean.setAccumYieldRate(balanceBeanVO.getAccumYieldRate());
        balanceBean.setAccumCost(balanceBeanVO.getAccumCost());
        balanceBean.setAccumCostRmb(balanceBeanVO.getAccumCostRmb());
        balanceBean.setBalanceFloatIncome(balanceBeanVO.getBalanceFloatIncome());
        balanceBean.setBalanceFloatIncomeRmb(balanceBeanVO.getBalanceFloatIncomeRmb());
        balanceBean.setBalanceFloatIncomeRate(balanceBeanVO.getBalanceFloatIncomeRate());
        balanceBean.setDayAssetRate(balanceBeanVO.getDayAssetRate());
        balanceBean.setDayIncomeGrowthRate(balanceBeanVO.getDayIncomeGrowthRate());
        balanceBean.setAccumCostNew(balanceBeanVO.getAccumCostNew());
        balanceBean.setAccumCostRmbNew(balanceBeanVO.getAccumCostRmbNew());
        balanceBean.setBalanceAmt(balanceBeanVO.getBalanceAmt());
        balanceBean.setBalanceAmtRmb(balanceBeanVO.getBalanceAmtRmb());
        balanceBean.setAccumCollection(balanceBeanVO.getAccumCollection());
        balanceBean.setAccumCollectionRmb(balanceBeanVO.getAccumCollectionRmb());
        balanceBean.setBalanceAmtExFee(balanceBeanVO.getBalanceAmtExFee());
        balanceBean.setBalanceAmtExFeeRmb(balanceBeanVO.getBalanceAmtExFeeRmb());
        balanceBean.setSxz(balanceBeanVO.getSxz());
        balanceBean.setCurrentIncome(balanceBeanVO.getCurrentIncome());
        balanceBean.setCurrentIncomeRmb(balanceBeanVO.getCurrentIncomeRmb());
        balanceBean.setCurrentAccumIncome(balanceBeanVO.getCurrentAccumIncome());
        balanceBean.setCurrentAccumIncomeRmb(balanceBeanVO.getCurrentAccumIncomeRmb());
        balanceBean.setStageFlag(balanceBeanVO.getStageFlag());
        balanceBean.setEstablishDt(balanceBeanVO.getEstablishDt());
        balanceBean.setAssetUpdateDate(balanceBeanVO.getAssetUpdateDate());
        balanceBean.setUnitBalanceCostExFee(balanceBeanVO.getUnitBalanceCostExFee());
        balanceBean.setUnitBalanceCostExFeeRmb(balanceBeanVO.getUnitBalanceCostExFeeRmb());
        balanceBean.setOwnershipTransferIdentity(balanceBeanVO.getOwnershipTransferIdentity());
        balanceBean.setSfhwcxg(balanceBeanVO.getSfhwcxg());
        balanceBean.setCpqxsm(balanceBeanVO.getCpqxsm());
        balanceBean.setNavDisclosureType(balanceBeanVO.getNavDisclosureType());
        balanceBean.setAbnormalFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setMarketValueCtl(balanceBeanVO.getMarketValueCtl());
        balanceBean.setCurrencyMarketValueCtl(balanceBeanVO.getCurrencyMarketValueCtl());
        balanceBean.setCurrencyMarketValueExFeeCtl(balanceBeanVO.getCurrencyMarketValueExFeeCtl());
        balanceBean.setMarketValueExFeeCtl(balanceBeanVO.getMarketValueExFeeCtl());
        balanceBean.setCurrentAssetCtl(balanceBeanVO.getCurrentAssetCtl());
        balanceBean.setCurrentAssetCurrencyCtl(balanceBeanVO.getCurrentAssetCurrencyCtl());
        balanceBean.setDailyAssetCtl(balanceBeanVO.getDailyAssetCtl());
        balanceBean.setDailyAssetCurrencyCtl(balanceBeanVO.getDailyAssetCurrencyCtl());
        balanceBean.setAccumIncomeCtl(balanceBeanVO.getAccumIncomeCtl());
        balanceBean.setAccumIncomeRmbCtl(balanceBeanVO.getAccumIncomeRmbCtl());
        balanceBean.setAccumRealizedIncomeCtl(balanceBeanVO.getAccumRealizedIncomeCtl());
        balanceBean.setAccumRealizedIncomeRmbCtl(balanceBeanVO.getAccumRealizedIncomeRmbCtl());
        balanceBean.setBalanceIncomeNewCtl(balanceBeanVO.getBalanceIncomeNewCtl());
        balanceBean.setBalanceIncomeNewRmbCtl(balanceBeanVO.getBalanceIncomeNewRmbCtl());
        balanceBean.setAccumIncomeNewCtl(balanceBeanVO.getAccumIncomeNewCtl());
        balanceBean.setAccumIncomeNewRmbCtl(balanceBeanVO.getAccumIncomeNewRmbCtl());
        balanceBean.setYieldRateCtl(balanceBeanVO.getYieldRateCtl());
        balanceBean.setBalanceVolCtl(balanceBeanVO.getBalanceVolCtl());
        balanceBean.setUnconfirmedVolCtl(balanceBeanVO.getUnconfirmedVolCtl());
        balanceBean.setNavCtl(balanceBeanVO.getNavCtl());
        balanceBean.setQianXiFlag(balanceBeanVO.getQianXiFlag());
        balanceBean.setUnPaidInAmt(balanceBeanVO.getUnPaidInAmt());
        balanceBean.setCurrencyUnPaidInAmt(balanceBeanVO.getCurrencyUnPaidInAmt());
        balanceBean.setPaidInAmt(balanceBeanVO.getSubTotalAmt());
        balanceBean.setPaidTotalAmt(balanceBeanVO.getPaidTotalAmt());
        balanceBean.setAccumBackRatio(balanceBeanVO.getAccumBackRatio());
        balanceBean.setPaidSubTotalRatio(balanceBeanVO.getPaidSubTotalRatio());
        balanceBean.setAbnormalFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setAssetStrategyFirstType(balanceBeanVO.getAssetStrategyFirstType());
        return balanceBean;
    }

    /**
     * 判断产品是否是香港产品
     *
     * @param fundCode 产品编码
     * @return 是否香港产品, true-是香港产品, false-不是香港产品
     */
    public boolean isHkProduct(String fundCode) {
        List<HighProductDBInfoModel> highProductInfoList = highProductService.getHighProductDBInfo(Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(highProductInfoList)) {
            log.info("isHkProduct-根据产品编码查询产品信息为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        String hkSaleFlag = highProductInfoList.get(0).getHkSaleFlag();
        if (StringUtils.isBlank(hkSaleFlag)) {
            log.info("isHkProduct-根据产品编码查询产品信息hkSaleFlag为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        return YesOrNoEnum.YES.getCode().equals(hkSaleFlag);
    }


}
