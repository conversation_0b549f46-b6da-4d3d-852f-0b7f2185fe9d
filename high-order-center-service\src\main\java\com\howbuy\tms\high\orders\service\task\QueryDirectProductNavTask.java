/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.simu.productinfo.QuerySimuProductInfoOuterService;
import com.howbuy.tms.common.outerservice.simu.productinfo.bean.SmjzBean;
import com.howbuy.tms.common.utils.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * @description:(查询私募产品信息任务类)
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年7月13日 下午7:51:19
 * @since JDK 1.7
 */
public class QueryDirectProductNavTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryDirectProductNavTask.class);

    private QuerySimuProductInfoOuterService querySimuProductInfoOuterService;

    private HighProductNavBean navBean;

    private CountDownLatch latch;

    public QueryDirectProductNavTask(QuerySimuProductInfoOuterService querySimuProductInfoOuterService, 
            HighProductNavBean navBean , CountDownLatch latch) {
        this.querySimuProductInfoOuterService = querySimuProductInfoOuterService;
        this.navBean = navBean;
        this.latch = latch;
    }


    @Override
    public RuntimeException call() throws Exception {
        try{
            String fundCode = navBean.getFundCode(); // 基金代码
            
            // 私募净值信息
            SmjzBean smjzBean = querySimuProductInfoOuterService.getDirectSmJzInfo(fundCode);
            if (smjzBean == null) {
                logger.error("QueryDirectProductNavTask|fundCode:{} smjzBean is null", fundCode);
                return new RuntimeException("smjzBean is null");
            }
            
            Double jjjz = smjzBean.getJjjz(); // 基金净值
            String jzrq = smjzBean.getJzrq(); // 净值日期
            if (smjzBean == null || StringUtils.isEmpty(jzrq)) {
                logger.error("QueryDirectProductNavTask|fundCode:{} jjjz or jzrq is null", fundCode);
                return new RuntimeException("jjjz or jzrq is null");
            }
            navBean.setNavDate(jzrq);
            navBean.setNav(new BigDecimal(jjjz.toString()));
        }catch(RuntimeException ex){
            logger.error("QueryDirectProductNavTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

