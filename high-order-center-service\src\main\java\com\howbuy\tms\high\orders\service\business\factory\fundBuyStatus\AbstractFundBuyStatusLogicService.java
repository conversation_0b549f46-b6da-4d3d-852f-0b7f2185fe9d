package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.ProductBuyUserTypeEnum;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.tms.cache.service.AbstractCacheService;
import com.howbuy.tms.cache.service.highquota.HighQuotaBeanNew;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.common.enums.busi.SupportFlagEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.AgeControlChannelEnum;
import com.howbuy.tms.common.enums.database.BranchCodeEnum;
import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.enums.database.TxOpenFlagEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.utils.ConvertCodeUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.CustomerInfoCommand;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:获取产品购买状态
 * @Author: yun.lu
 * Date: 2023/10/26 15:11
 */
@Service
public abstract class AbstractFundBuyStatusLogicService extends AbstractCacheService {
    private static final Logger logger = LogManager.getLogger(AbstractFundBuyStatusLogicService.class);


    /**
     * 高端黑名单不允许交易
     */
    @Value("${high.black.txacctnos}")
    private String blackTxAcctNos;


    /**
     * 当前处理类对应分销渠道
     *
     * @return 分销渠道
     */
    public abstract String getDisCode();


    public abstract boolean isWithe(String hbOneNo, QueryFundBuyStatusParam queryFundBuyStatusParam, HighProductInfoModel highProductInfoBean);

    /**
     * 获取产品购买状态
     */
    public FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,查询产品购买状态,queryFundBuyStatusParam={}", JSON.toJSONString(queryFundBuyStatusParam));
        FundBuyStatusDto fundBuyStatusDto = new FundBuyStatusDto();
        fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_BUY);
        fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NORMAL.getCode());
        String fundCode = queryFundBuyStatusParam.getFundCode();
        fundBuyStatusDto.setProductCode(fundCode);
        // 查询用户信息
        QueryCustInfoResult customerInfo = queryFundBuyStatusParam.getCustInfo();
        // 0.如果查询不到用户信息,用户不可以买
        if (customerInfo == null && StringUtils.isNotEmpty(queryFundBuyStatusParam.getTxAcctNo())) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.OTHERS.getCode());
            fundBuyStatusDto.setMsg("查询不到用户信息");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-查询不到用户信息不可以买,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }
        // 1.用户是否属于高端黑名单用户
        if (StringUtils.isNotEmpty(blackTxAcctNos) && customerInfo != null && StringUtils.isNotEmpty(customerInfo.getTxAcctNo()) && blackTxAcctNos.contains(customerInfo.getTxAcctNo())) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.IN_BLANKLIST.getCode());
            fundBuyStatusDto.setMsg("属于高端黑名单用户");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-属于高端黑名单用户,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }
        // 2.查询产品信息
        HighProductCanBuyInfoModel highProductCanBuyInfoModel = queryFundBuyStatusParam.getHighProductCanBuyInfoModel();
        if (highProductCanBuyInfoModel == null) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setMsg("查不到产品所有信息");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-查不到产品所有信息,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }

        HighProductInfoModel highProductInfoBean = highProductCanBuyInfoModel.getHighProductInfoModel();
        if (highProductInfoBean == null) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setMsg("查不到产品基本信息");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-查不到产品基本信息,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }
        fundBuyStatusDto.setShareClass(highProductInfoBean.getShareClass());
        // 代销关系校验
        if (queryFundBuyStatusParam.getNotCheckChannel() == null || YesOrNoEnum.NO.getCode().equals(queryFundBuyStatusParam.getNotCheckChannel())) {
            if (!validIsSupCounterOrWeb(highProductInfoBean, queryFundBuyStatusParam.getTxChannel())) {
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NOT_SUP_DIS.getCode());
                fundBuyStatusDto.setMsg("代销关系校验不通过");
                logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-代销关系校验不通过,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
                return fundBuyStatusDto;
            }
        }

        // 产品可购买类型校验
        if (!validIsSupBuyInvstType(highProductInfoBean, customerInfo)) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NOT_SUP_INVST_TYPE.getCode());
            fundBuyStatusDto.setMsg("产品不是可购买类型");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-产品不是可购买类型,queryFundBuyStatusInfo={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }

        // 直销黑名单校验
        List<String> blackFundCodes = queryFundBuyStatusParam.getBlackFundCodes();
        if (blackFundCodes.contains(fundCode)) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.IN_BLANKLIST.getCode());
            fundBuyStatusDto.setMsg("直销黑名单不通过");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-直销黑名单,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }

        // 预约日历场景下不校验预约期和状态
        if (!YesOrNoEnum.YES.getCode().equals(queryFundBuyStatusParam.getAppointmentFlag())) {
            // 校验预约期和状态
            validateProductTradeStatus(fundBuyStatusDto, queryFundBuyStatusParam, highProductInfoBean);
            if (FundBuyStatusEnum.CAN_NOT_BUY.getStatus().equals(fundBuyStatusDto.getFundBuyStatusEnum().getStatus())) {
                return fundBuyStatusDto;
            }
        }

        // 已售罄
        if (!validSoldOut(customerInfo, highProductInfoBean, queryFundBuyStatusParam, fundBuyStatusDto)) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,产品已售罄,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }

        // 是否只追加购买
        if (YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getOnlySuppleFlag()) &&
                !queryFundBuyStatusParam.getCcOrZtList().contains(highProductInfoBean.getFundCode())) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ONLY_SUPPLY_BUY.getCode());
            fundBuyStatusDto.setMsg("产品只支持追加");
            logger.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,产品只支持追加,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }
        fundBuyStatusDto.setMsg("可以购买");
        return fundBuyStatusDto;
    }


    /**
     * 产品额度/人数校验
     *
     * @param customerInfo        用户信息
     * @param highProductInfoBean 产品信息
     * @return
     */
    private boolean validSoldOut(QueryCustInfoResult customerInfo, HighProductInfoModel highProductInfoBean, QueryFundBuyStatusParam queryFundBuyStatusParam, FundBuyStatusDto fundBuyStatusDto) {
        String fundCode = highProductInfoBean.getFundCode();

        // 产品总额度
        if (highProductInfoBean.getIpoPeopleLimit() == null) {
            logger.error("validSoldOut-募集人数上线为空,fundCode={}", highProductInfoBean.getFundCode());
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL));
        }
        Integer peopleLimit = highProductInfoBean.getIpoPeopleLimit().intValue();
        BigDecimal amountLimit = highProductInfoBean.getIpoAmountLimit();
        if (peopleLimit == 0 || amountLimit == null) {
            logger.error("validSoldOut-募集金额/人数为空,fundCode={}", highProductInfoBean.getFundCode());
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL));
        }

        // 缓存: 高端产品已使用额度信息
        HighQuotaBeanNew highQuotaBean = getHighQuotaBeanNew(queryFundBuyStatusParam);

        // 无用户信息, 校验总剩余金额
        if (customerInfo == null) {
            BigDecimal leftTotalAmount = amountLimit.subtract(highQuotaBean.getLockUsedAmount()).subtract(highQuotaBean.getUnLockUsedAmount());
            if (leftTotalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.info("AbstractFundBuyStatusLogicService-validSoldOut-总剩余金额额度不足,leftTotalAmount:{},fundCode={}", leftTotalAmount, fundCode);
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
                fundBuyStatusDto.setMsg("总剩余金额额度不足");
                return false;
            }
            return true;
        }

        // 是否为追加购买
        List<String> ccOrZtList = queryFundBuyStatusParam.getCcOrZtList();
        Integer partnersNumber = queryFundBuyStatusParam.getPartnersNumber();
        partnersNumber = null != partnersNumber ? partnersNumber : 1;
        // 首次交易, 校验剩余总人数
        if (!ccOrZtList.contains(fundCode)) {
            int leftTotalCount = peopleLimit - highQuotaBean.getLockUsedCount() - highQuotaBean.getUnLockUsedCount() + (highQuotaBean.getLockExitCount() * -1);
            if (leftTotalCount < partnersNumber) {
                logger.info("AbstractFundBuyStatusLogicService-validSoldOut-总剩余人数额度不足,leftTotalCount:{}，partnersNumber:{},fundCode={}", leftTotalCount, partnersNumber, fundCode);
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
                fundBuyStatusDto.setMsg("总剩余人数额度不足");
                return false;
            }
        }

        // 高端产品配置的锁定人数/锁定金额
        // 锁定人数
        Integer lockCount = 0;
        // 锁定金额
        BigDecimal lockAmount = BigDecimal.ZERO;
        HighProductLockInfoModel highProductLockInfoBean = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductLockInfoModel();
        if (highProductLockInfoBean != null) {
            lockCount = highProductLockInfoBean.getLockCount() != null ? highProductLockInfoBean.getLockCount() : lockCount;
            lockAmount = highProductLockInfoBean.getLockAmount() != null ? highProductLockInfoBean.getLockAmount() : lockAmount;
        }
        // 白名单用户
        if (isWithe(customerInfo.getHboneNo(), queryFundBuyStatusParam, highProductInfoBean)) {
            return checkLockSoldOutCheck(fundBuyStatusDto, fundCode, highQuotaBean, ccOrZtList, partnersNumber, lockCount, lockAmount);
        } else {
            return unLockSoldOutCheck(fundBuyStatusDto, fundCode, peopleLimit, amountLimit, highQuotaBean, ccOrZtList, partnersNumber, lockCount, lockAmount);
        }
    }

    /**
     * 非白名单额度校验
     */
    private boolean unLockSoldOutCheck(FundBuyStatusDto fundBuyStatusDto, String fundCode, Integer peopleLimit, BigDecimal amountLimit, HighQuotaBeanNew highQuotaBean, List<String> ccOrZtList, Integer partnersNumber, Integer lockCount, BigDecimal lockAmount) {
        // 首次交易
        if (!ccOrZtList.contains(fundCode)) {
            int leftUnLockCount = peopleLimit - lockCount - highQuotaBean.getUnLockUsedCount() + (highQuotaBean.getLockExitCount() * -1);
            if (leftUnLockCount < partnersNumber) {
                logger.info("AbstractFundBuyStatusLogicService-validSoldOut-剩余未锁定人数不足,leftUnLockCount:{}, partnersNumber:{},fundCode:{}", leftUnLockCount, partnersNumber, fundCode);
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
                fundBuyStatusDto.setMsg("剩余未锁定人数不足");
                return false;
            }
        }
        BigDecimal leftUnLockAmount = amountLimit.subtract(lockAmount).subtract(highQuotaBean.getUnLockUsedAmount());
        boolean res = leftUnLockAmount.compareTo(BigDecimal.ZERO) > 0;
        if (!res) {
            logger.info("AbstractFundBuyStatusLogicService-validSoldOut-剩余未锁定金额不足,leftUnLockAmount:{},fundCode={}", leftUnLockAmount, fundCode);
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
            fundBuyStatusDto.setMsg("剩余未锁定金额不足");
            return false;
        }
        return res;
    }

    /**
     * 白名单额度校验
     */
    private boolean checkLockSoldOutCheck(FundBuyStatusDto fundBuyStatusDto, String fundCode, HighQuotaBeanNew highQuotaBean, List<String> ccOrZtList, Integer partnersNumber, Integer lockCount, BigDecimal lockAmount) {
        // 首次交易
        if (!ccOrZtList.contains(fundCode)) {
            int leftLockCount = lockCount - highQuotaBean.getLockUsedCount();
            if (leftLockCount < partnersNumber) {
                logger.info("AbstractFundBuyStatusLogicService-validSoldOut-锁定剩余人数不足,leftLockCount:{},partnersNumber={},fundCode={}", leftLockCount, partnersNumber, fundCode);
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
                fundBuyStatusDto.setMsg("锁定剩余人数不足");
                return false;
            }
        }
        BigDecimal leftLockAmount = lockAmount.subtract(highQuotaBean.getLockUsedAmount());
        if (leftLockAmount.compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("AbstractFundBuyStatusLogicService-validSoldOut-锁定剩余金额不足,leftLockAmount:{},fundCode:{}", leftLockAmount, fundCode);
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
            fundBuyStatusDto.setMsg("锁定剩余金额不足");
            return false;
        }
        return true;
    }

    private static HighQuotaBeanNew getHighQuotaBeanNew(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        HighQuotaBeanNew highQuotaBean = queryFundBuyStatusParam.getHighQuotaBeanNew();
        if (highQuotaBean == null) {
            highQuotaBean = new HighQuotaBeanNew();
            if (highQuotaBean.getLockUsedCount() == null) {
                highQuotaBean.setLockUsedCount(0);
            }
            if (highQuotaBean.getLockExitCount() == null) {
                highQuotaBean.setLockExitCount(0);
            }
            if (highQuotaBean.getUnLockUsedCount() == null) {
                highQuotaBean.setUnLockUsedCount(0);
            }
            if (highQuotaBean.getLockUsedAmount() == null) {
                highQuotaBean.setLockUsedAmount(BigDecimal.ZERO);
            }
            if (highQuotaBean.getUnLockUsedAmount() == null) {
                highQuotaBean.setUnLockUsedAmount(BigDecimal.ZERO);
            }
        }
        return highQuotaBean;
    }


    /**
     * 验证交易状态校验
     */
    private void validateProductTradeStatus(FundBuyStatusDto fundBuyStatusDto, QueryFundBuyStatusParam queryFundBuyStatusParam, HighProductInfoModel highProductInfoBean) {
        // 中台业务码
        String mBusinessCode = null;
        // 产品支持提前下单标志
        String appDtmStr = queryFundBuyStatusParam.getAppDt() + queryFundBuyStatusParam.getAppTm();

        // 根据当前TaTradeDt与募集结束日比较, 得出具体业务码
        // 募集结束日期
        String ipoEndDt = highProductInfoBean.getIpoEndDt();
        if (StringUtils.isEmpty(ipoEndDt)) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取产品购买状态-没有募集结束日期,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("没有募集结束日期");
            return;
        }
        mBusinessCode = BusinessCodeEnum.SUBS.getMCode();
        if (queryFundBuyStatusParam.getTaTradeDt().compareTo(ipoEndDt) > 0) {
            mBusinessCode = BusinessCodeEnum.PURCHASE.getMCode();
        }

        // 预约信息处理
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusinessCode)
                || IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(highProductInfoBean.getIsScheduledTrade())
                || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductInfoBean.getIsScheduledTrade())) {
            // 查询预约信息
            HighProductAppointmentInfoModel highProductAppointmentInfoModel = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductAppointmentInfoModel();
            if (highProductAppointmentInfoModel == null) {
                logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取产品购买状态-查询不到预约信息,fundCode={}", queryFundBuyStatusParam.getFundCode());
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setMsg("查询不到预约信息");
                return;
            }

            // 交易是否在可购买期
            if (!TxChannelEnum.COUNTER.getCode().equals(queryFundBuyStatusParam.getTxChannel())
                    && (StringUtils.isNotEmpty(highProductAppointmentInfoModel.getBuyDay())
                    && StringUtils.isNotEmpty(highProductAppointmentInfoModel.getBuyTime()))) {
                String buyDateStr = highProductAppointmentInfoModel.getBuyDay() + highProductAppointmentInfoModel.getBuyTime();
                if (buyDateStr.compareTo(appDtmStr) > 0) {
                    logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取产品购买状态-交易不在可购买期,fundBuyStatusParam={},buyDateStr={},appDtmStr={}", JSON.toJSONString(queryFundBuyStatusParam), buyDateStr, appDtmStr);
                    fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NOT_BUY_DATE.getCode());
                    fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                    fundBuyStatusDto.setMsg("查询不到预约信息");
                    return;
                }
            }
        }

        // 查询高端产品状态信息
        HighProductStatInfoModel highProductStatInfoBean = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductStatInfoModel();
        if (highProductStatInfoBean == null) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取不到产品状态信息,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("获取不到产品状态信息");
            return;
        }
        if (StringUtils.isEmpty(mBusinessCode)) {
            BusinessCodeEnum businessCodeEnum = ConvertCodeUtils.convertBuyBusiCode(highProductStatInfoBean.getFundStat());
            if (businessCodeEnum == null) {
                logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取不到产品状态信息,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setMsg("获取不到产品状态信息");
                return;
            } else {
                mBusinessCode = businessCodeEnum.getMCode();
            }

        }

        if (checkFundStatus(fundBuyStatusDto, queryFundBuyStatusParam, mBusinessCode, highProductStatInfoBean)) {
            return;
        }

        // 交易开通配置信息校验
        HighProductTxOpenCfgModel highProductTxOpenCfgModel = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductTxOpenCfgModel();
        checkProductTxOpenCfg(fundBuyStatusDto, queryFundBuyStatusParam, highProductTxOpenCfgModel);
    }

    private boolean checkFundStatus(FundBuyStatusDto fundBuyStatusDto, QueryFundBuyStatusParam queryFundBuyStatusParam, String mBusinessCode, HighProductStatInfoModel highProductStatInfoBean) {
        // 校验产品校验状态
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusinessCode) && !MDataDic.CAN_SUR_SET.contains(highProductStatInfoBean.getFundStat())) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取不到产品状态信息,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NOT_BUY_STATUS.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("产品非可以购买状态");
            return true;
        }
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusinessCode) && !MDataDic.CAN_PUR_SET.contains(highProductStatInfoBean.getFundStat())) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,获取不到产品状态信息,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.NOT_BUY_STATUS.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("产品非可以购买状态");
            return true;
        }
        return false;
    }

    /**
     * 交易开通配置信息校验
     */
    private void checkProductTxOpenCfg(FundBuyStatusDto fundBuyStatusDto, QueryFundBuyStatusParam queryFundBuyStatusParam, HighProductTxOpenCfgModel highProductTxOpenCfgBean) {

        // 非群济私募产品校验交易开通标识
        if (highProductTxOpenCfgBean == null) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,高端产品交易开通配置查不到,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("高端产品交易开通配置查不到");
            return;
        }
        if (TxOpenFlagEnum.CLOSE.getCode().equals(highProductTxOpenCfgBean.getOpenFlag())) {
            logger.info("AbstractFundBuyStatusLogicService-validateProductTradeStatus,高端产品交易已关闭,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.TX_CLOSED.getCode());
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setMsg("高端产品交易已关闭");
        }
    }

    /**
     * validAgeLimit:(校验年龄限制)
     */
    public boolean validAgeLimit(HighProductInfoBean highProductInfoBean, String txChannel, CustomerInfoCommand customerInfo) {
        String ageControlFlag = highProductInfoBean.getAgeControlFlag();
        if (StringUtils.isEmpty(ageControlFlag)) {
            return true;
        }
        String ageControlChannel = highProductInfoBean.getAgeControlChannel();
        if (SupportFlagEnum.YES.getCode().equals(highProductInfoBean.getAgeControlFlag())) {
            if (!StringUtils.isEmpty(ageControlChannel) && !AgeControlChannelEnum.CTRL_ALL.getCode().equals(ageControlChannel)) {
                if ((TxChannelEnum.COUNTER.getCode().equals(txChannel) &&
                        AgeControlChannelEnum.CTRL_UN_COUNTER.getCode().equals(ageControlChannel))
                        || (!TxChannelEnum.COUNTER.getCode().equals(txChannel) &&
                        AgeControlChannelEnum.CTRL_COUNTER.getCode().equals(ageControlChannel))) {
                    return true;
                }
            }
            Integer minAge = highProductInfoBean.getMinAge();
            Integer maxAge = highProductInfoBean.getMaxAge();
            if (minAge == null || maxAge == null) {
                return false;
            }

            if (customerInfo != null && InvstTypeEnum.INDI.getCode().equals(customerInfo.getInvstType()) && InvstTypeEnum.INST.getCode().equals(customerInfo.getIdType()) && StringUtils.isNotEmpty(customerInfo.getIdNo())) {
                return ProductInfoValidator.validateCustAge(customerInfo.getIdNo(), minAge, maxAge);
            }
        }

        return true;
    }

    /**
     * 校验代销关系
     */
    private boolean validIsSupCounterOrWeb(HighProductInfoModel highProductInfoBean, String txChannel) {
        String branchCode = highProductInfoBean.getBranchCode();
        if (BranchCodeEnum.ALL.getCode().equals(branchCode)) {
            return true;
        }
        if (TxChannelEnum.COUNTER.getCode().equals(txChannel) && (StringUtils.isEmpty(branchCode) || BranchCodeEnum.UN_COUNTER.getCode().equals(branchCode))) {
            return false;
        }
        return TxChannelEnum.COUNTER.getCode().equals(txChannel) || (!StringUtils.isEmpty(branchCode) && !BranchCodeEnum.COUNTER.getCode().equals(branchCode));
    }


    /**
     * 校验产品可购买类型
     */
    private boolean validIsSupBuyInvstType(HighProductInfoModel highProductInfoBean, QueryCustInfoResult customerInfo) {
        String buyUserType = highProductInfoBean.getBuyUserType();
        if (StringUtils.isBlank(buyUserType)) {
            return true;
        }
        if (buyUserType.contains(ProductBuyUserTypeEnum.ALL.getCode())) {
            return true;
        }
        if (customerInfo != null) {
            String userType = ProductBuyUserTypeEnum.getByInvstType(customerInfo.getInvstType());
            if (!StringUtils.isBlank(userType)) {
                return buyUserType.contains(userType);
            }
        }
        return true;
    }

    /**
     * 检查特定客户延期购买时间范围
     * 
     * @description: 场景1：配置中的开放结束日所属的日历上的预约结束日+结束时间<当前时间<=配置中的延期下单截止日期+时间<配置中的开放结束日所属的日历上的的开放结束日期+15点
     * @param deferConfig 延期购买配置
     * @param currentDtmStr 当前时间字符串(YYYYMMDDHHMMSS)
     * @return boolean 是否在允许的时间范围内
     * @author: 陈杰文
     * @date: 2025-09-02 11:07:07
     * @since JDK 1.8
     */
    private boolean checkDeferPurchaseTimeRange(HighSpecialCustDeferPurchaseCfgModel deferConfig, 
                                                String currentDtmStr) {
        try {
            // 获取预约结束日期+时间
            String appointmentEndDtmStr = deferConfig.getApponitEndDt() + deferConfig.getApponitEndTm();
            
            // 获取延期下单截止日期+时间
            String deferOrderEndDtmStr = deferConfig.getDeferOrderDt() + deferConfig.getDeferOrderTm();
            
            // 获取开放结束日期+15点(150000)
            String openEndDtmStr = deferConfig.getOpenEndDt() + "150000";
            
            logger.info("AbstractFundBuyStatusLogicService-checkDeferPurchaseTimeRange,时间范围检查,appointmentEndDtm={},currentDtm={},deferOrderEndDtm={},openEndDtm={}", 
                       appointmentEndDtmStr, currentDtmStr, deferOrderEndDtmStr, openEndDtmStr);
            
            // 场景1条件：预约结束日+结束时间 < 当前时间 <= 延期下单截止日期+时间 < 开放结束日期+15点
            boolean condition1 = appointmentEndDtmStr.compareTo(currentDtmStr) < 0;
            boolean condition2 = currentDtmStr.compareTo(deferOrderEndDtmStr) <= 0;
            boolean condition3 = deferOrderEndDtmStr.compareTo(openEndDtmStr) < 0;
            
            logger.info("AbstractFundBuyStatusLogicService-checkDeferPurchaseTimeRange,条件检查结果,condition1={},condition2={},condition3={}", condition1, condition2, condition3);
            
            return condition1 && condition2 && condition3;
            
        } catch (Exception e) {
            logger.error("AbstractFundBuyStatusLogicService-checkDeferPurchaseTimeRange,时间范围检查异常", e);
            return false;
        }
    }

}
