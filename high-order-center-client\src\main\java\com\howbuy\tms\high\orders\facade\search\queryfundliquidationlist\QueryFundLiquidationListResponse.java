/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidationlist;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 清仓产品列表查询响应结果
 * <AUTHOR>
 * @date 2025/9/4 20:05
 * @since JDK 1.8
 */
@Data
public class QueryFundLiquidationListResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 清仓产品列表
     */
    private List<FundLiquidationInfo> fundLiquidationList;

    /**
     * 清仓产品信息
     */
    @Data
    public static class FundLiquidationInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 产品收益
         * 净值型产品展示累计收益，非净值型产品展示累计回款
         * 千分位格式展示，四舍五入保留两位小数
         */
        private String productAsset;

        /**
         * 累计天数
         * 累计持有天数，单位：天
         */
        private String totalDays;

        /**
         * 单位
         * 默认是元
         */
        private String unit = "元";
    }
}
