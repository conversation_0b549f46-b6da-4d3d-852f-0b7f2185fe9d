/**
 * Copyright (c) 2018, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.business.busiprocess.common;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.enums.RedeemTypeEnum;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MDataDic;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.ForceSubmitFlagEnum;
import com.howbuy.tms.common.enums.busi.RedeemStatusEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.FundOpenFlagEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.enums.database.ProtocolTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLimitBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductStatInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductTxOpenCfgBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.common.validator.account.TxAcctValidator;
import com.howbuy.tms.common.validator.amtorvol.AmtOrVolValidator;
import com.howbuy.tms.common.validator.bankcard.CustBankCardValidator;
import com.howbuy.tms.common.validator.business.ProtocolTypeValidator;
import com.howbuy.tms.common.validator.business.RedeemVolValidator;
import com.howbuy.tms.common.validator.highproductinfo.ProductInfoValidator;
import com.howbuy.tms.common.validator.highproductinfo.ProductLimitValidator;
import com.howbuy.tms.common.validator.tainfo.TaInfoValidator;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.trade.redeem.BaseMergeRedeemRequest;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.service.redeemLogicService.QueryCustomerRedeemAppointInfoLogicService;
import com.howbuy.tms.high.orders.service.facade.search.queryredeemfundstatus.CustomerCpAcctRedeemInfo;
import com.howbuy.tms.high.orders.service.facade.search.queryredeemfundstatus.CustomerFundRedeemInfo;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(赎回业务公共处理类)
 * @reason:
 * @date 2018年3月9日 下午4:18:37
 * @since JDK 1.6
 */
public abstract class AbstractRedeemBusiProcess extends AbstractBusiProcess {
    private static final Logger logger = LogManager.getLogger(AbstractRedeemBusiProcess.class);

    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private QueryCustomerRedeemAppointInfoLogicService queryCustomerRedeemAppointInfoLogicService;

    /**
     * processBusiness:(业务处理)
     * 原RedeemWebBusiProcess、RedeemCounterBusiProcess里process的公共部分
     *
     * @param request
     * @param context
     * <AUTHOR>
     * @date 2017年4月12日 上午10:23:28
     */
    public void processBusiness(BaseMergeRedeemRequest request, OrderCreateContext context, String forceSubmitFlag) {
        // 校验份额和协议类型
        ProtocolTypeValidator.validateProtocolTypeMatch(request.getProtocolType(), ProtocolTypeEnum.HIGH_FUND.getCode());
        request.getRedeemDetailList().forEach(detail -> AmtOrVolValidator.validateAppVol(detail.getAppVol()));

        // 获取高端产品信息
        HighProductInfoBean highProductInfoBean = queryHighProductOuterService.getHighProductInfo(request.getFundCode());
        context.setHighProductInfoBean(highProductInfoBean);

        // 设置电子合同版本号
        setContractVersion(highProductInfoBean, context);

        // 验证产品信息
        validateProductInfo(highProductInfoBean, request.getRedeemCapitalFlag());

        // 获取当前TA工作日
        String taTradeDt = queryTradeDayOuterService.getWorkDay(request.getAppDt(), request.getAppTm());
        context.setTaTradeDt(taTradeDt);
        String appDtmStr = request.getAppDt() +  request.getAppTm();
        Date appDate = DateUtils.formatToDate(appDtmStr, DateUtils.YYYYMMDDHHMMSS);
        // 查询高端产品交易开通配置信息
        HighProductTxOpenCfgBean highProductTxOpenCfgBean =
                queryHighProductOuterService.getHighProductTxOpenCfg(highProductInfoBean.getFundCode(), BusinessCodeEnum.REDEEM.getCode());

        // 校验交易是否开通
        ProductInfoValidator.validateTxOpenFlag(highProductTxOpenCfgBean);

        // 校验TA状态
        TaInfoValidator.validateTaStat(highProductInfoBean.getTaStat());

        context.getRedeemList().forEach(detail -> {
            // 调用账户中心查询客户交易账户信息和资金账户信息
            QueryAllCustInfoResult custInfo = getCustInfo(request.getTxAcctNo(), detail.getCpAcctNo(), request.getDisCode());
            detail.setCustInfo(custInfo);

            // 验证账户及银行卡信息
            validateAcctAndBank(custInfo, highProductInfoBean.getProductChannel());

            detail.setSupportAdvanceFlag(highProductInfoBean.getIsScheduledTrade());
        });
        // 处理预约信息
        txControlProcess(request, context, highProductInfoBean);

        // 验证产品限额及可用份额(锁定期)
        validateProductLimitAndAvailVol(request, context, highProductInfoBean, forceSubmitFlag);
        // 额外添加是否可以赎回逻辑(原赎回校验不纯粹)
        if (YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getIsCyclicLock())) {
            // 查询赎回信息
            CustomerFundRedeemInfo customerFundRedeemInfo = queryCustomerRedeemAppointInfoLogicService.queryCustomerFundRedeemInfo(request.getTxAcctNo(), request.getFundCode(),appDate);
            if (RedeemStatusEnum.NOT_ALLOW.getCode().equals(customerFundRedeemInfo.getRedeemStatus())) {
                logger.error("AbstractRedeemBusiProcess-用户产品不可赎回,customerFundRedeemInfo={}", JSON.toJSONString(customerFundRedeemInfo));
                throw new BusinessException("Z3000033", MessageSource.getMessageByCode("Z3000033"));
            }
            // 赎回明细
            List<CustomerCpAcctRedeemInfo> customerCpAcctRedeemInfoList = customerFundRedeemInfo.getCustomerCpAcctRedeemInfoList();
            if (CollectionUtils.isEmpty(customerCpAcctRedeemInfoList)) {
                logger.error("AbstractRedeemBusiProcess-没有赎回明细,不能赎回,customerFundRedeemInfo={}", JSON.toJSONString(customerFundRedeemInfo));
                throw new BusinessException("Z3000033", MessageSource.getMessageByCode("Z3000033"));
            }
            // 对比每个资金账号下可赎回份额是否足够
            Map<String, BigDecimal> canRedeemVolMap = customerCpAcctRedeemInfoList.stream().collect(Collectors.toMap(CustomerCpAcctRedeemInfo::getCpAcctNo, CustomerCpAcctRedeemInfo::getCanRedeemVol));
            for (OrderCreateContext.RedeemBean redeemBean : context.getRedeemList()) {
                BigDecimal canRedeemVol = canRedeemVolMap.get(redeemBean.getCpAcctNo());
                if (canRedeemVol == null || canRedeemVol.compareTo(redeemBean.getAppVol()) < 0) {
                    logger.error("AbstractRedeemBusiProcess-该资金账号下可用份额不足,不能赎回,cpAcctNo={},canRedeemVol={},appVol={}", redeemBean.getCpAcctNo(), canRedeemVol, redeemBean.getAppVol());
                    throw new BusinessException("Z3000033", MessageSource.getMessageByCode("Z3000033"));
                }
            }
        }
        // 设置子订单的预约单信息
        setSubOrderAppointmentInfo(context);
    }

    /**
     * validateAcctAndBank:(验证账户及银行卡信息)
     *
     * @param custInfo
     * <AUTHOR>
     * @date 2017年7月7日 下午6:20:24
     */
    protected void validateAcctAndBank(QueryAllCustInfoResult custInfo, String productChannel) {
        // 校验账户状态:交易账户, 分销交易账户, 基金交易账户
        TxAcctValidator.validatAcctStat(custInfo.getCustInfo().getTxAcctNo(), custInfo.getAcTxAcct(), custInfo.getDisAcTxAcct());

        // 校验银行账户信息
        CustBankCardValidator.validateExistBankCard(custInfo.getCustBankCardInfo());
    }

    /**
     * validateProductInfo:(验证产品信息)
     *
     * @param highProductInfoBean
     * @param redeemCapitalFlag
     * <AUTHOR>
     * @date 2017年7月7日 下午6:26:27
     */
    protected void validateProductInfo(HighProductInfoBean highProductInfoBean, String redeemCapitalFlag) {
        // 校验高端产品信息是否存在
        ProductInfoValidator.validateProductInfoExsit(highProductInfoBean);

        // 校验产品类型是否为高端产品
        ProductInfoValidator.validateProductClass(highProductInfoBean.getProductClass());

        // 验证赎回去向
        ProductInfoValidator.validateRedeemDirection(highProductInfoBean, redeemCapitalFlag);
    }

    /**
     * txControlProcess:(交易控制处理)
     *
     * @param request
     * @param context
     * @param highProductInfoBean
     * <AUTHOR>
     * @date 2017年4月25日 上午10:49:48
     */
    protected void txControlProcess(BaseMergeRedeemRequest request, OrderCreateContext context, HighProductInfoBean highProductInfoBean) {
        // 验证预约订单号是否已使用
        validateAppointDealNo(request.getAppointmentDealNo());

        String isScheduledTrade = highProductInfoBean.getIsScheduledTrade();
        if (StringUtils.isEmpty(isScheduledTrade)) {
            throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL));
        }
        String submitTaDt = null;
        // 当前的ta工作日
        String taTradeDt = context.getTaTradeDt();
        // 支持提前下单
        if (IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(isScheduledTrade) || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade)) {
            // 查询预约信息
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(
                    highProductInfoBean.getFundCode(), "1", highProductInfoBean.getShareClass(), request.getDisCode(), context.getAppDtm());
            // 支持提前下单的产品, 必须维护开放日历; 且当前TA工作日一定小于等于开放截止日
            if (productAppointmentInfoBean == null) {
                throw new ValidateException(ExceptionCodes.APPOINTMENT_INFO_IS_NULL, MessageSource.getMessageByCode(ExceptionCodes.APPOINTMENT_INFO_IS_NULL));
            }

            String dateTem = taTradeDt;
            // 开放日第一天
            String openStartDt = productAppointmentInfoBean.getOpenStartDt();
            if (taTradeDt.compareTo(openStartDt) <= 0) {
                dateTem = openStartDt;
            }

            // 查询高端产品状态信息
            HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductInfoBean.getFundCode(), dateTem);
            if (highProductStatInfoBean == null || StringUtils.isEmpty(highProductStatInfoBean.getFundStat())
                    || !validateFundRedeemStat(highProductStatInfoBean, highProductInfoBean)) {
                logger.error("AbstractRedeemBusiProcess|txControlProcess 高端产品不允许赎回 fundStat:{} openFlag:{}",
                        highProductStatInfoBean == null ? null : highProductStatInfoBean.getFundStat(), highProductInfoBean.getOpenFlag());
                throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_NOT_ALLOW_RDM,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_NOT_ALLOW_RDM));
            } else {
                submitTaDt = dateTem;
            }
        } else {
            // 根据当前TA工作日, 获取高端产品净值信息
            HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductInfoBean.getFundCode(), taTradeDt);
            if (highProductStatInfoBean == null || !validateFundRedeemStat(highProductStatInfoBean, highProductInfoBean)) {
                logger.error("AbstractRedeemBusiProcess|txControlProcess 高端产品不允许赎回 fundStat:{} openFlag:{}",
                        highProductStatInfoBean == null ? null : highProductStatInfoBean.getFundStat(), highProductInfoBean.getOpenFlag());
                throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_NOT_ALLOW_RDM,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_NOT_ALLOW_RDM));
            }
            submitTaDt = taTradeDt;
        }
        context.setSubmitTaDt(submitTaDt);
    }

    /**
     * validateFundRedeemStat:(校验基金赎回状态)
     *
     * @param highProductStatInfoBean
     * @param highProductInfoBean
     * @return
     * <AUTHOR>
     * @date 2017年4月25日 下午2:02:24
     * @modifier gang.li01
     * @date 2017-11-20
     */
    private boolean validateFundRedeemStat(HighProductStatInfoBean highProductStatInfoBean, HighProductInfoBean highProductInfoBean) {
        if (!FundOpenFlagEnum.OPEN.getCode().equals(highProductInfoBean.getOpenFlag())) {
            return false;
        }

        if (StringUtils.isEmpty(highProductStatInfoBean.getFundStat())) {
            return false;
        }

        if (!MDataDic.CAN_REDEEM_SET.contains(highProductStatInfoBean.getFundStat())) {
            return false;
        }
        return true;
    }


    /**
     * validateProductLimitAndAvailVol:(验证产品限额及可用份额(锁定期))
     *
     * @param request
     * @param context
     * @param productInfo
     * <AUTHOR>
     * @date 2017年7月7日 下午6:29:58
     */
    protected void validateProductLimitAndAvailVol(BaseMergeRedeemRequest request, OrderCreateContext context, HighProductInfoBean productInfo, String forceSubmitFlag) {
        // 查询持仓列表
        Map<String, BalanceVo> balanceVoMap = queryBalanceVoMap(request, context, productInfo);
        if (balanceVoMap == null) {
            throw new BusinessException(ExceptionCodes.CUST_AVAIL_VOL_LT_APP_VOL,
                    MessageSource.getMessageByCode(ExceptionCodes.CUST_AVAIL_VOL_LT_APP_VOL));
        }

        BigDecimal sumAppVol = BigDecimal.ZERO;
        // 校验单卡可用份额 (申请份额 <= 可用份额)
        for (OrderCreateContext.RedeemBean redeem : context.getRedeemList()) {
            sumAppVol = sumAppVol.add(redeem.getAppVol());
            BigDecimal availVol = BigDecimal.ZERO;
            BalanceVo balanceVo = balanceVoMap.get(redeem.getCpAcctNo());
            if (balanceVo != null) {
                // 客户协议持仓可用份额
                availVol = balanceVo.getBalanceVol().subtract(balanceVo.getUnconfirmedVol()).subtract(balanceVo.getJustFrznVol());
                // 客户协议持仓可用份额-锁定份额
                if (RedeemTypeEnum.YES.getCode().equals(productInfo.getHasLockPeriod())) {
                    availVol = availVol.subtract(balanceVo.getLockingPeriodVol());
                }
            }
            logger.info("validateProductLimitAndAvailVol|availVol:{},balanceVo:{}", availVol, JSON.toJSONString(balanceVo));
            // 验证产品可用份额(锁定期除外): 申请份额 <= 可用份额
            RedeemVolValidator.validateRedeemVol(redeem.getAppVol(), availVol);
        }

        // 查询当前TA, 用户当日总赎回申请份额
        BigDecimal sumRedeemVol = highDealOrderDtlRepository.countSumRedeemVol(request.getTxAcctNo(), context.getTaTradeDt(), productInfo.getFundCode());

        HighProductLimitBean highProductLimitBean = queryHighProductOuterService.getHighProductLimitInfo(request.getFundCode(), productInfo.getShareClass(),
                context.getBusinessCode().getCode(), context.getRedeemList().get(0).getCustInfo().getCustInfo().getInvstType());

        // 强制上报不校验日累计限额
        if (!ForceSubmitFlagEnum.YES.getCode().equals(forceSubmitFlag)) {
            // TP私募和专户,校验个人日单日累计赎回限额
            ProductLimitValidator.validateDailyMaxSumRedeemVol(highProductLimitBean, sumAppVol, sumRedeemVol);
        }

        // 查询客户基金在中台的所有份额
        BigDecimal mAllAvailVol = BigDecimal.ZERO;
        CustBooksPo custBooksPo = custBooksRepository.selectCustAllBooksByTxAcctNoAndFundCode(request.getTxAcctNo(), productInfo.getFundCode(), null);
        if (custBooksPo != null) {
            mAllAvailVol = custBooksPo.getBalanceVol().subtract(custBooksPo.getUnconfirmedVol()).subtract(custBooksPo.getJustFrznVol());
        }
        logger.info("AppVol:{}, mAllAvailVol:{}, MinAcctVol:{}", sumAppVol, mAllAvailVol, productInfo.getMinAcctVol());

        // 基金赎回限额校验
        ProductLimitValidator.validateSingleFundRedeemLimit(highProductLimitBean, sumAppVol, mAllAvailVol,
                productInfo.getMinAcctVol(), forceSubmitFlag);
    }

    /**
     * 查询持仓列表map
     *
     * @param request
     * @param context
     * @param highProductInfoBean
     * @return java.util.Map<java.lang.String, com.howbuy.tms.high.orders.dao.vo.BalanceVo>
     * @author: huaqiang.liu
     * @date: 2021/3/22 17:05
     * @since JDK 1.8
     */
    private Map<String, BalanceVo> queryBalanceVoMap(BaseMergeRedeemRequest request, OrderCreateContext context,
                                                     HighProductInfoBean highProductInfoBean) {
        // 查询持仓列表
        String txAcctNo = request.getTxAcctNo();
        String disCode = request.getDisCode();
        String fundCode = request.getFundCode();
        String protocolNo = request.getProtocolNo();
        String redeemType = highProductInfoBean.getHasLockPeriod();
        logger.info("validateProductLimitAndAvailVol|disCode:{}, txAcctNo:{}, protocolNo:{}, fundCode:{}, submitTaDt:{}, redeemType:{}", new Object[]{disCode, txAcctNo, protocolNo, fundCode, context.getSubmitTaDt(), redeemType});
        List<BalanceVo> balanceVoList = custBooksRepository.selectBalanceDtl(disCode, txAcctNo, protocolNo, fundCode, null, context.getSubmitTaDt());
        logger.info("validateProductLimitAndAvailVol|balanceVoList:{}", JSON.toJSONString(balanceVoList));

        if (CollectionUtils.isEmpty(balanceVoList)) {
            return null;
        }

        // 转成map
        Map<String, BalanceVo> balanceVoMap = new HashMap<>(balanceVoList.size());
        balanceVoList.forEach(balanceVo -> balanceVoMap.put(balanceVo.getCpAcctNo(), balanceVo));

        return balanceVoMap;
    }

    /**
     * 设置子订单的预约单信息
     *
     * @param context
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/2/25 16:41
     * @since JDK 1.8
     */
    protected void setSubOrderAppointmentInfo(OrderCreateContext context) {
        List<OrderCreateContext.RedeemBean> redeemList = context.getRedeemList();
        OrderCreateContext.RedeemBean mainOrder = redeemList.get(0);
        if (redeemList.size() <= 1 || StringUtils.isBlank(mainOrder.getAppointmentDealNo())) {
            return;
        }

        redeemList.subList(1, redeemList.size()).forEach(redeemBean -> {
            //设置预约信息
            redeemBean.setAppointmentDealNo(mainOrder.getAppointmentDealNo());
            redeemBean.setAppointmentType(mainOrder.getAppointmentType()); // 预约单类型
            redeemBean.setOrderFormType(mainOrder.getOrderFormType());// 成单方式
        });

    }
}
