/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.tms.high.orders.service.task;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighStructProductNavBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Description:查询结构化产品净值任务类
 * 
 * @reason:
 * <AUTHOR>
 * @date 2017年4月12日 下午5:52:16
 * @since JDK 1.7
 */
public class QueryStructProductNavTask implements Callable<RuntimeException> {

    private static final Logger logger = LogManager.getLogger(QueryStructProductNavTask.class);

    private QueryHighProductOuterService queryHighProductOuterService;

    private Map<String, HighProductNavBean> fundNavMap;
    
    private BalanceBean balanceBean;

    private CountDownLatch latch;

    public QueryStructProductNavTask(QueryHighProductOuterService queryHighProductOuterService, Map<String, HighProductNavBean> fundNavMap,
                                     BalanceBean balanceBean, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.fundNavMap = fundNavMap;
        this.balanceBean = balanceBean;
        this.latch = latch;
    }

    @Override
    public RuntimeException call() throws Exception {
        try{
            String productCode = balanceBean.getProductCode();
            // 锁定期份额确认日期
            String ackDt = balanceBean.getEstablishDt();
            
            // 获取子基金代码及对应的净值信息
            HighStructProductNavBean highStructProductNavBean = queryHighProductOuterService.getHighStructProductNav(productCode, ackDt);
            if (highStructProductNavBean == null || highStructProductNavBean.getNav() == null) {
                logger.warn("QueryStructProductNavTask|highStructProductNavBean is null, productCode:{}, ackDt:{}", productCode, ackDt);
                return null;
            }
            // 子基金代码
            balanceBean.setSubProductCode(highStructProductNavBean.getProductId());
            // 分期成立的产品的每期对应的净值和净值日期
            HighProductNavBean highProductNavBean = new HighProductNavBean();
            highProductNavBean.setNavDate(highStructProductNavBean.getNavDate());
            highProductNavBean.setNav(highStructProductNavBean.getNav());
            highProductNavBean.setIsDivNav(highStructProductNavBean.getIsDivNav());
            fundNavMap.put(highStructProductNavBean.getProductId(), highProductNavBean);
        }catch(RuntimeException ex){
            logger.error("QueryStructProductNavTask|RuntimeException.", ex);
            return ex;
        } finally {
            latch.countDown();
        }
        return null;
    }

}

