package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description:卖出非香港待确认订单交易记录Bean
 * @Author: yun.lu
 * Date: 2025/9/2 15:31
 */
@Getter
@Setter
public class SellUnConfirmOrderDTO extends BaseDto {
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 中台业务类型
     */
    private String mBusiCode;

    /**
     * 上报状态
     * 0-无需上报，1-未上报，2-上报完成，3-需重新上报
     */
    private String notifySubmitFlag;

    /**
     * 上报日,yyyyMMdd
     */
    private String submitTaDt;
}
