package com.howbuy.tms.high.orders.service.common.utils;

import com.howbuy.cachemanagement.dto.HealthCheckResult;
import com.howbuy.cachemanagement.exception.MgetDataException;
import com.howbuy.cachemanagement.sync.SyncCacheListener;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存服务接口
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 2014-7-25 上午9:38:25
 */
public interface CacheService {
    /**
     * 写入缓存,指定超时时间
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    <T> boolean put(Integer expire, String key, T value);

    /**
     * 为相同前缀的key的集合获取值,如果key的前缀并非一致会抛出异常
     * @param keys
     * @param <T>
     * @return
     * @throws MgetDataException
     */
    public <T extends Serializable> List<T> mget4SamePrefixKeys(String...keys) throws MgetDataException;

    /**
     * 写入缓存
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    <T> boolean put(String key, T value);

    /**
     * 写入缓存,指定超时时间，带CAS版本
     *
     * @param key     键
     * @param value   值
     * @param version 版本号
     * @return 成功返回true，失败false
     */
    //<T> boolean put(Integer expire, String key, T value, Long version);

    /**
     * 写入缓存，带CAS版本
     *
     * @param key     键
     * @param value   值
     * @param version 版本号
     * @return 成功返回true，失败false
     */
    //<T> boolean put(String key, T value, Long version);

    /**
     * 当不存在时，写入缓存
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    <T> boolean putIfAbsent(String key, T value);

    /**
     * 读取缓存 有本地读本地，本地为空，读远端，并写入本地
     *
     * @param key 键
     * @return 缓存值
     */
    <T> T get(String key);

    /**
     * 读取多key缓存
     *
     * @param keys 键
     * @return 缓存值
     */
    <T> List<T> mget(String... keys);

    /**
     * 读取缓存 本地为空，读远端，并写入本地。 本地不空，但本地版本旧于fromVersion，也读远端，并写入本地。
     *
     * @param key         键
     * @param fromVersion 起始版本
     * @return 缓存值
     */
    //<T> T get(String key, Long fromVersion);

    /**
     * 向列表“右侧”追加元素
     *
     * @param key   列表的Key
     * @param value 追加的值
     * @return 追加完后列表元素个数
     */
    <T> Long append(String key, T value);

    /**
     * 向列表“右侧”追加元素
     *
     * @param key      列表的Key
     * @param value    追加的值
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return 追加完后列表元素个数，重试后仍失败，返回-1L
     */
    <T> Long append(String key, T value, boolean reExpire);

    /**
     * 从列表中批量取出多个元素
     *
     * @param key    列表的键
     * @param amount 元素个数
     * @return 对象列表
     */
    <T> List<T> take(String key, Long amount);

    /**
     * 从列表中批量取出多个元素
     *
     * @param key    列表的键
     * @param amount 元素个数
     * @return 对象列表
     */
    <T> List<T> takeWithThrowExcp(String key, Long amount);

    /**
     * 删除缓存值
     *
     * @param key 键
     */
    void remove(String key);

    /**
     * 从列表中批量读取多个元素
     *
     * @param key    列表的键
     * @param amount 元素个数
     * @return 对象列表
     */
    <T> List<T> list(String key, Long amount);

    /**
     * 写入Map的一个元素 目前只适用于Redis
     *
     * @param key   Map的Key
     * @param field Map的域
     * @param obj   Map的对象值
     * @return 如果元素已存在，更新之，返回0；不存在，新建之，返回1
     */
    Long putToMap(String key, String field, String obj);

    /**
     * 写入Map的一个元素 目前只适用于Redis
     *
     * @param key      Map的Key
     * @param field    Map的域
     * @param obj      Map的对象值
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return 如果元素已存在，更新之，返回0；不存在，新建之，返回1
     */
    Long putToMap(String key, String field, String obj, boolean reExpire);

    /**
     * 将Map整体写入缓存 目前只适用于Redis
     *
     * @param key Map的Key
     * @param map 写入的Hash
     * @return OK为成功
     */
    String putToMap(String key, Map<String, String> map);

    /**
     * 将Map整体写入缓存 目前只适用于Redis
     *
     * @param key      Map的Key
     * @param map      写入的Hash
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return OK为成功
     */
    String putToMap(String key, Map<String, String> map, boolean reExpire);

    /**
     * 从Map中删除一批数据
     *
     * @param key    Map的键
     * @param fields 待删除的域list
     * @return 域存在，删除后返回1，否则返回0
     */
    Long removeFromMap(String key, List<String> fields);

    /**
     * 根据<键,域>对，从Hash中删除一个对象
     *
     * @param key   键
     * @param field 域
     * @return 存在，删除之，返回1；不存在，返回0
     */
    Long removeFromMap(String key, String field);

    /**
     * 判断field是否在Map中
     *
     * @param key   缓存键
     * @param field Map中的域
     * @return 存在true，不存在false
     */
    boolean existsInMap(String key, String field);

    /**
     * 从缓存Map中获取一组值 目前只适用于Redis
     *
     * @param key    Map的Key
     * @param fields 需要获取的缓存域
     * @return 一组值
     */
    List<String> getFromMap(String key, List<String> fields);

    /**
     * 根据键和域，取得对象
     *
     * @param key   键
     * @param field 域
     * @return 对象
     */
    Object getObjFromMap(String key, String field);

    /**
     * 根据键和域，取得对象
     *
     * @param key   键
     * @param field 域
     * @return 对象
     */
    Object getObjFromMap(String key, String field, boolean reExpire);

    /**
     * 写入Map的一个元素 目前只适用于Redis
     *
     * @param key   Map的Key
     * @param field Map的域
     * @param obj   Map的对象值
     * @return 如果元素已存在，更新之，返回0；不存在，新建之，返回1
     */
    Long putObjToMap(String key, String field, Object obj);

    /**
     * 写入Map的一个元素 目前只适用于Redis
     *
     * @param key      Map的Key
     * @param field    Map的域
     * @param obj      Map的对象值
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return 如果元素已存在，更新之，返回0；不存在，新建之，返回1
     */
    Long putObjToMap(String key, String field, Object obj, boolean reExpire);

    /**
     * 获取Map中的全部数据
     *
     * @param key 键
     * @return Map数据
     */
    Map<String, String> getFromMap(String key);

    /**
     * 获取Map中的全部数据
     *
     * @param key 键
     * @return Map数据
     */
    Map<String, Object> getFromObjMap(String key);

    /**
     * 从缓存中获取值和版本 目前只适用于Memcached
     *
     * @param key 缓存的Key
     * @return ValueWithVersion，包含值与版本
     */
    //<T> ValueWithVersion<T> getWithVersion(String key);

    /**
     * 从缓存中获取版本号 目前只适用于Memcached
     *
     * @param key 缓存的Key
     * @return 版本
     */
    //Long getRemoteVersion(String key);

    /**
     * 自增某一增量
     *
     * @param key  键
     * @param step 增量
     * @return 自增后的结果
     */
    Long incrBy(String key, Long step);

    /**
     * 自增某一增量
     *
     * @param key      键
     * @param step     增量
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return 自增后的结果
     */
    Long incrBy(String key, Long step, boolean reExpire);

    /**
     * 设置过期时长
     *
     * @param key     键
     * @param seconds 过期时长
     */
    void expires(String key, Integer seconds);

    /**
     * 为Key添加同步监听器，包含前置、后置监听
     *
     * @param key               键
     * @param syncCacheListener 监听器
     */
    void addSyncCacheListener(String key, SyncCacheListener syncCacheListener);

    /**
     * 删除某Key的同步监听器
     *
     * @param key 键
     */
    void removeSyncCacheListener(String key);

    /**
     * 指定位置，写入List
     *
     * @param key list的键
     * @param map <位置，对象>
     */
    <T> void putToList(String key, Map<Long, T> map);

    /**
     * 将Map中的某一域增加step
     *
     * @param key   键
     * @param field 域
     * @param step  增量
     * @return 增加后数值
     */
    Long incrByInMap(String key, String field, Long step);

    /**
     * 获取某Key的Hash中所有的field
     *
     * @param key 键
     * @return 某Key的Hash中所有的field
     */
    Set<String> getFieldsFromMap(String key);

    /**
     * 按序写入Sorted Set 按CCMS配置重置失效时间
     *
     * @param key   键
     * @param score 排序权重分
     * @param obj   存入的对象
     * @return 0——已经存在；1——成功插入
     */
    <T extends Serializable> Long putToSortedSet(String key, Long score, T obj);

    /**
     * 按序写入Sorted Set
     *
     * @param key      键
     * @param score    排序权重分
     * @param obj      存入的对象
     * @param reExpire 是否按CCMS配置重置失效时间，true/false
     * @return 0——已经存在；1——成功插入
     */
    <T extends Serializable> Long putToSortedSet(String key, Long score, T obj,
                                                 boolean reExpire);

    /**
     * 从Sorted Set中获取某成员的排序值
     *
     * @param key 键
     * @param obj 查询对象
     * @return 排序值
     */
    <T extends Serializable> Long getRankFromSortedSet(String key, T obj);

    /**
     * 从Sorted Set中读取指定范围的数据
     *
     * @param key   键
     * @param start 起始下标， 缺省从0开始
     * @param end   终止下标，缺省取剩余全部
     * @return Sorted Set中数据组成的集合
     */
    <T extends Serializable> List<T> getFromSortedSet(String key, Long start,
                                                      Long end);

    /**
     * 从Sorted Set中读取指定score范围的数据
     *
     * @param key 键
     * @param min 最小score， 缺省取起始全部
     * @param max 最大score，缺省取剩余全部
     * @return Sorted Set中数据组成的集合
     */
    <T extends Serializable> List<T> getFromSortedSetByScore(String key,
                                                             Double min, Double max);

    /**
     * 从Sorted Set中删除指定范围
     *
     * @param key   键
     * @param start 起始下标
     * @param end   终止下标
     * @return 成功时，返回删除的记录数；如果start或end为null，抛异常。
     */
    Long removeFromSortedSetByRank(String key, Long start, Long end);

    /**
     * 从Sorted Set中删除指定score范围
     *
     * @param key   键
     * @param start 起始score
     * @param end   终止score
     * @return 成功时，返回删除的记录数；如果start或end为null，抛异常。
     */
    Long removeFromSortedSetByScore(String key, Double start, Double end);

    /**
     * 判断键值存在
     *
     * @param key
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 判断元素是否在Set中
     *
     * @param key    缓存键
     * @param member Set中的元素
     * @return 存在true，不存在false
     */
    boolean existsInSet(String key, String member);

    /**
     * 设置缓存服务的尝试次数
     *
     * @param tryTimes 尝试次数
     */
    void setTryTimes(Integer tryTimes);

    /**
     * 手动初始化一个Key
     *
     * @param key key
     * @return 成功true，失败false
     */
    boolean initializeKey(String key);

    /**
     * 字符串写入缓存,指定超时时间
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    boolean putStr(Integer expire, String key, String value);

    /**
     * 字符串写入缓存
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    boolean putStr(String key, String value);

    /**
     * 写入缓存,指定超时时间，带CAS版本
     *
     * @param key     键
     * @param value   值
     * @param version 版本号
     * @return 成功返回true，失败false
     */
     //boolean putStr(Integer expire, String key, String value, Long version);

    /**
     * 写入缓存，带CAS版本
     *
     * @param key     键
     * @param value   值
     * @param version 版本号
     * @return 成功返回true，失败false
     */
    //boolean putStr(String key, String value, Long version);


    /**
     * 当不存在时，字符串写入缓存
     *
     * @param key   键
     * @param value 值
     * @return 成功返回true，失败false
     */
    boolean putStrIfAbsent(String key, String value);

    /**
     * 读取缓存 有本地读本地，本地为空，读远端，并写入本地
     *
     * @param key 键
     * @return 缓存值
     */
    String getStr(String key);

    /**
     * 读取多key缓存
     *
     * @param keys 键
     * @return 缓存值
     */
    List<String> mgetStr(String... keys);

    /**
     * 读取缓存 本地为空，读远端，并写入本地。 本地不空，但本地版本旧于fromVersion，也读远端，并写入本地。
     *
     * @param key         键
     * @param fromVersion 起始版本
     * @return 缓存值
     */
    //String getStr(String key, Long fromVersion);

    /**
     * 读一个Hash中的元素数量
     *
     * @param key 键名
     * @return 数量
     */
    Long countInMap(String key);

    /**
     * 读一个List中的元素数量
     *
     * @param key 键名
     * @return 数量
     */
    Long countInList(String key);

    /**
     * 读一个Set中的元素数量
     *
     * @param key 键名
     * @return 数量
     */
    Long countInSet(String key);

    /**
     * 读一个有序Set中的元素数量
     *
     * @param key 键名
     * @return 数量
     */
    Long countInSortedSet(String key);

    /**
     * 将一个值置于Set中
     *
     * @param key   键
     * @param value 值
     * @return 新加则为1，覆盖则为0
     */
    Long putToSet(String key, String value);

    /**
     * 按模式匹配key的集合
     * 只能用于Redis，且分片集群下无用
     *
     * @param keyPattern 模式，只能为合规的key加上*组成
     * @return 集合
     */
    Set<String> keys(String keyPattern);

    /**
     * 自增step，如果是首次自增，设置过期时间
     *
     * @param key           键
     * @param step          自增量
     * @param expireSeconds 过期时间
     * @return 增后值
     */
    Long incrAndExpire(String key, Long step, Integer expireSeconds);

    /**
     * 获取过期剩余时间
     *
     * @param key
     * @return
     */
    Long ttl(String key);

    Long bitCount(String key);

    Long bitCount(String key, long start, long end);

    Boolean setBit(String key, long offset, boolean value);

    Boolean setBit(String key, long offset, String value);

    Boolean getBit(String key, long offset);

    <T extends Serializable> boolean lock(String key, T value);

    Set<String> smembers(String key);

    Long srem(String key, String member);

    /**
     * 写入缓存
     *
     * @param key     键
     * @param value   值
     * @param nxxx   值
     *                  nx ： not exists, 只有key 不存在时，才把key value set 到redis
     *                  xx ： is exists ，只有key 存在时，  才把key value set 到redis

     * @param secondTime 过期时间
     * @return 是否执行成功
     */
    <T extends Serializable> boolean put(String key, T value, String nxxx, int secondTime);


    HealthCheckResult healthCheck();
}
