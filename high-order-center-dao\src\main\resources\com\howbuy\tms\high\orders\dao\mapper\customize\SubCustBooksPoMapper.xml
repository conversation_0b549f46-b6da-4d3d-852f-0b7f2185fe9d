<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.SubCustBooksPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.SubCustBooksPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.SubCustBooksPoAutoMapper.BaseResultMap">
    </resultMap>
  <!-- 自定义 -->
  <!-- 查询子账本表 (开放赎回日期维度) -->
  <select id="selectSubCustBooksByOpenRedeDt" parameterType="map" resultMap="BaseResultMap">
  		select *
	    	from (select t.ext_vol_dtl_no,
	    				 t.books_dtl_no,
		                 t.tx_acct_no,
		                 t.fund_code,
		                 t.protocol_no,
		                 t.cp_acct_no,
		                 t.ack_dt,
		                 t.reg_dt,
		                 t.open_rede_dt,
		                 t.fund_share_class,
                         sum(t.avail_vol) as avail_vol,
		                 sum(t.balance_vol) as balance_vol,
		                 abs(sum(t.frzn_vol)) as frzn_vol,
		                 abs(sum(t.just_frzn_vol)) as just_frzn_vol
	           		 from (select t1.ext_vol_dtl_no,
	           		              t1.books_dtl_no,
			                      t1.tx_acct_no,
			                   	  t1.fund_code,
			                   	  t1.protocol_no,
			                   	  t1.cp_acct_no,
			                   	  t1.ack_dt,
			                   	  t1.reg_dt,
			                   	  t1.open_rede_dt,
			                   	  t1.fund_share_class,
			                   	  t1.balance_vol,
                                  ifnull(t1.avail_vol, 0) as avail_vol,
			                   	  ifnull(t1.frzn_vol, 0) as frzn_vol,
                                  ifnull(t1.just_frzn_vol, 0) as just_frzn_vol
				           from sub_cust_books t1
				           where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                              <if test="disCodeList != null and disCodeList.size() > 0 ">
                                  and t1.dis_code  in
                                  <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                                      #{disCode}
                                  </foreach>
                              </if>
				             <if test="fundCode != null and fundCode != ''">
				                and t1.fund_code = #{fundCode,jdbcType=VARCHAR}
				             </if>
				             <if test="cpAcctNo != null and cpAcctNo != ''">
				                and t1.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
				             </if>
				             <if test="openRedeDt != null and openRedeDt != ''">
				                and t1.open_rede_dt &lt;= #{openRedeDt,jdbcType=VARCHAR}
				             </if>
				             and t1.protocol_type = '4'
				          ) t
		           		group by t.tx_acct_no,
		                    t.fund_code,
		                    t.fund_share_class,
		                    t.protocol_no,
		                    t.cp_acct_no,
		                    t.open_rede_dt,
		                    t.ext_vol_dtl_no,
		                    t.books_dtl_no,
		                    t.ack_dt,
		                    t.reg_dt) tt
   		where tt.balance_vol &gt; 0 or tt.frzn_vol &gt; 0 or tt.just_frzn_vol &gt; 0
   		order by tt.open_rede_dt asc, tt.ext_vol_dtl_no asc
  </select>
  <!-- 查询子账本 -->
  <select id="selectSubCustBooks" parameterType="map" resultMap="BaseResultMap">
  		select *
	    	from (select
	    				 
		                 t.tx_acct_no,
		                 t.fund_code,
		                 t.protocol_no,
		                 t.DIS_CODE,
		                 t.ack_dt,
		                 t.reg_dt,
		                 t.open_rede_dt,
		                 t.fund_share_class,
		                 sum(t.balance_vol) as balance_vol,
		                 abs(sum(t.frzn_vol)) as frzn_vol,
		                 abs(sum(t.just_frzn_vol)) as just_frzn_vol,
                         t.ESTABLISH_DT
	           		 from (select 
			                      t1.tx_acct_no,
			                   	  t1.fund_code,
			                   	  t1.protocol_no,
			                   	  t1.ack_dt,
                                  t1.DIS_CODE,
			                   	  t1.reg_dt,
			                   	  t1.open_rede_dt,
			                   	  t1.fund_share_class,
			                   	  t1.balance_vol,
                                  ifnull(t1.frzn_vol, 0) as frzn_vol,
                                  ifnull(t1.just_frzn_vol, 0) as just_frzn_vol,
                                  t1.ESTABLISH_DT
				           from sub_cust_books t1
				           where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
                              <if test="disCodeList != null and disCodeList.size() > 0 ">
                                  and t1.dis_code  in
                                  <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                                      #{disCode}
                                  </foreach>
                              </if>
				             <if test="fundCode != null and fundCode != ''">
				                and t1.fund_code = #{fundCode,jdbcType=VARCHAR}
				             </if>
				             <if test="openRedeDt != null and openRedeDt != ''">
				                and t1.open_rede_dt &lt;= #{openRedeDt,jdbcType=VARCHAR}
				             </if>
				             and t1.protocol_type = '4'
				             ) t
		           		group by t.tx_acct_no,
		                    t.fund_code,
		                    t.fund_share_class,
		                    t.protocol_no,
		                    t.open_rede_dt,
		                    t.ack_dt,
                            t.DIS_CODE,
		                    t.reg_dt,
                            t.ESTABLISH_DT) tt
      <where>
          <bind name="status" value="balanceStatus.toString()" />
          <if test='status == "0"'>
              tt.balance_vol = 0 or tt.frzn_vol = 0 or tt.just_frzn_vol = 0
          </if>
          <if test='status == "1"'>
              tt.balance_vol &gt; 0 or tt.frzn_vol &gt; 0 or tt.just_frzn_vol &gt; 0
          </if>
          <if test='status == "2"'>
              1=1
          </if>
      </where>
   		order by tt.open_rede_dt asc
  </select>
  
  
  <!-- 查询客户产品专户持仓明细信息 -->
  <select id="selectZhBalanceDtl" resultMap="BaseResultMap" parameterType="map" >
    select * from (
          select  t.protocol_no as protocol_no, 
		          t.product_code as fund_code,
	       	      t.fund_share_class as fund_share_class,
	       	      t.cp_acct_no as cp_acct_no,
	       	      sum(t.balance_vol) as balance_vol,
	              abs(sum(t.unconfirmed_vol)) as frzn_vol,
	              abs(sum(t.locking_period_vol)) as locking_period_vol,
	              abs(sum(t.just_frzn_vol)) as just_frzn_vol,
	              '6' as product_channel
	           from
                ( select t2.product_code as product_code,
	                 t2.fund_share_class as fund_share_class,
	                 t2.protocol_no as protocol_no,
	                 t2.cp_acct_no as cp_acct_no,
                     ifnull(t2.ack_vol, 0) as balance_vol,
                     ifnull(t2.app_vol, 0) as unconfirmed_vol,
	                 0  as locking_period_vol,
	                 0 as just_frzn_vol
	            from cust_books_dtl t2
	           <where> 
	                 t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR} 
		             and t2.dis_code = #{disCode,jdbcType=VARCHAR} 
		             <if test="productCode != null and productCode != ''">
						and t2.product_code = #{productCode,jdbcType=VARCHAR}
					 </if>
					 <if test="cpAcctNo != null and cpAcctNo != ''">
						and t2.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}  
			      	 </if>
		             and t2.protocol_type = '4'
		             and t2.product_channel ='6'
	           </where>
	          union all
	          select t3.product_code as product_code,
	                 t3.fund_share_class as fund_share_class,
	                 t3.protocol_no as protocol_no,
	                 t3.cp_acct_no as cp_acct_no,
                     ifnull(t3.balance_vol, 0) as balance_vol,
                     ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
	                 0  as locking_period_vol,
                     ifnull(t3.just_frzn_vol, 0) as just_frzn_vol
	            from cust_books t3
	           where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR} 
	             and t3.dis_code = #{disCode,jdbcType=VARCHAR} 
	             <if test=" productCode != null and productCode != ''">
					and t3.product_code = #{productCode,jdbcType=VARCHAR}
				 </if>
				 <if test=" cpAcctNo != null and cpAcctNo != ''">
					and t3.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}  
		      	 </if>
	             and t3.protocol_type = '4'
	             and  t3.product_channel ='6') t
	    group by t.protocol_no, t.product_code, t.fund_share_class, t.cp_acct_no
    ) tt
	where balance_vol &gt; 0
  </select>
  
  <select id="selectSubCustBooksByTxAcctNo" parameterType="map" resultMap="BaseResultMap">
  		select *
		  from (select t.tx_acct_no,
		               t.fund_code,
		               t.cp_acct_no,
		               t.protocol_no,
		               t.ack_dt,
		               t.reg_dt,
		               t.open_rede_dt,
		               t.fund_share_class,
		               sum(t.balance_vol) as balance_vol,
		               abs(sum(t.frzn_vol)) as frzn_vol,
		               abs(sum(t.just_frzn_vol)) as just_frzn_vol,
		               t.product_channel
		          from (select t1.tx_acct_no,
		                       t1.fund_code,
		                       t1.cp_acct_no,
		                       t1.protocol_no,
		                       t1.ack_dt,
		                       t1.reg_dt,
		                       t1.open_rede_dt,
		                       t1.fund_share_class,
		                       t1.balance_vol,
		                       ifnull(t1.frzn_vol, 0) as frzn_vol,
		                       ifnull(t1.just_frzn_vol, 0) as just_frzn_vol,
		                       t1.product_channel
		                  from sub_cust_books t1
		                 where t1.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
                          <if test="disCodeList != null and disCodeList.size() > 0 ">
                              and t1.dis_code  in
                              <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                                  #{disCode}
                              </foreach>
                          </if>
		                   <if test = "fundCode != null and fundCode != ''">
		                   and t1.fund_code = #{fundCode,jdbcType = VARCHAR}
		                   </if> 
		                   <if test = "cpAcctNo != null and cpAcctNo != ''">
		                   and t1.cp_acct_no = #{cpAcctNo,jdbcType = VARCHAR} 
		                   </if>
		                   and t1.protocol_type = '4') t
		         group by t.tx_acct_no,
		                  t.fund_code,
		                  t.cp_acct_no,
		                  t.fund_share_class,
		                  t.protocol_no,
		                  t.open_rede_dt,
		                  t.ack_dt,
		                  t.reg_dt,
		                  t.product_channel) tt
		 where tt.balance_vol &gt;0 or tt.frzn_vol &gt;0 or tt.just_frzn_vol &gt;0 order by tt.open_rede_dt asc
  </select>

    <resultMap id="subCustBookDetailResultMap" type="com.howbuy.tms.high.orders.dao.vo.SubCustBooksVo">
        <id column="fundCode" jdbcType="VARCHAR" property="fundCode" />
        <result column="txAcctNo" jdbcType="VARCHAR" property="txAcctNo" />
        <result column="cpAcctNo" jdbcType="VARCHAR" property="cpAcctNo" />
        <result column="ackDt" jdbcType="VARCHAR" property="ackDt" />
        <result column="openRedeemDt" jdbcType="VARCHAR" property="openRedeemDt" />
        <result column="balanceVol" jdbcType="DECIMAL" property="balanceVol" />
        <result column="frznVol" jdbcType="DECIMAL" property="frznVol" />
        <result column="justFrznVol" jdbcType="DECIMAL" property="justFrznVol" />
        <result column="unconfirmedVol" jdbcType="DECIMAL" property="redeemUnconfirmedVol" />
    </resultMap>

    <select id="selectAcctSubCustBooks" parameterType="map" resultMap="subCustBookDetailResultMap">
       select
           t1.FUND_CODE as fundCode,
           t1.TX_ACCT_NO as txAcctNo,
           t1.CP_ACCT_NO as cpAcctNo,
           t1.ACK_DT as ackDt,
           t1.open_rede_dt as openRedeemDt,
           sum(t1.balance_vol) as balanceVol,
           abs(sum(t1.frzn_vol)) as frznVol,
           abs(sum(t1.just_frzn_vol)) as justFrznVol
        from sub_cust_books t1
        where t1.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
        and t1.fund_code = #{fundCode,jdbcType = VARCHAR}
        and t1.protocol_type = '4'
        and t1.balance_vol &gt;0  group by t1.FUND_CODE,t1.TX_ACCT_NO,t1.CP_ACCT_NO,t1.ACK_DT,t1.open_rede_dt
    </select>

    <select id="queryAllBalanceVolFund"  resultType="java.lang.String">
        select distinct(fund_code) from sub_cust_books where AVAIL_VOL>0
    </select>

    <select id="selectSubCustBookSumByAckDt" parameterType="map" resultMap="BaseResultMap">
        select tt.tx_acct_no,
                     tt.fund_code,
                     tt.protocol_no,
                     tt.ack_dt,
                     tt.dis_code,
                     tt.reg_dt,
                     tt.open_rede_dt,
                     tt.fund_share_class,
                     tt.balance_vol,
                     tt.frzn_vol,
                     tt.just_frzn_vol,
                     tt.product_channel
        from (select t.tx_acct_no,
                     t.fund_code,
                     t.protocol_no,
                     t.ack_dt,
                     t.DIS_CODE,
                     t.reg_dt,
                     t.open_rede_dt,
                     t.fund_share_class,
                     sum(t.balance_vol) as balance_vol,
                     abs(sum(t.frzn_vol)) as frzn_vol,
                     abs(sum(t.just_frzn_vol)) as just_frzn_vol,
                     t.product_channel
               from (select t1.tx_acct_no,
                            t1.fund_code,
                            t1.protocol_no,
                            t1.ack_dt,
                            t1.reg_dt,
                            t1.DIS_CODE,
                            t1.open_rede_dt,
                            t1.fund_share_class,
                            t1.balance_vol,
                            ifnull(t1.frzn_vol, 0) as frzn_vol,
                            ifnull(t1.just_frzn_vol, 0) as just_frzn_vol,
                            t1.product_channel
                     from sub_cust_books t1
                     where t1.tx_acct_no = #{txAcctNo,jdbcType = VARCHAR}
                           and t1.fund_code = #{fundCode,jdbcType = VARCHAR}
                            <if test="list != null and list.size() > 0 ">
                                and t1.dis_code  in
                                <foreach collection="list" index="index" item="disCode" open="(" separator="," close=")">
                                    #{disCode}
                                </foreach>
                            </if>
                           and t1.protocol_type = '4') t
              group by t.tx_acct_no,
                       t.fund_code,
                       t.fund_share_class,
                       t.protocol_no,
                       t.DIS_CODE,
                       t.open_rede_dt,
                       t.ack_dt,
                       t.reg_dt,
                       t.product_channel) tt
        where
        <bind name="status" value="balanceStatus.toString()"/>
        <if test='status == "0"'>
            tt.balance_vol =0 and tt.frzn_vol =0 and tt.just_frzn_vol =0
        </if>
        <if test='status == "1"'>
            tt.balance_vol &gt;0 or tt.frzn_vol &gt;0 or tt.just_frzn_vol &gt;0
        </if>
        <if test='status == "2"'>
            1=1
        </if>
        order by tt.open_rede_dt asc
    </select>

    <select id="selectZhBalanceDtlWithOutSubBook" resultMap="BaseResultMap" parameterType="map">
        select tt.* from (
        select  t.protocol_no as protocol_no,
        t.product_code as fund_code,
        t.fund_share_class as fund_share_class,
        t.cp_acct_no as cp_acct_no,
        sum(t.balance_vol) as balance_vol,
        abs(sum(t.unconfirmed_vol)) as frzn_vol,
        abs(sum(t.locking_period_vol)) as locking_period_vol,
        abs(sum(t.just_frzn_vol)) as just_frzn_vol,
        '6' as product_channel
        from
        ( select t2.product_code as product_code,
        t2.fund_share_class as fund_share_class,
        t2.protocol_no as protocol_no,
        t2.cp_acct_no as cp_acct_no,
        ifnull(t2.ack_vol, 0) as balance_vol,
        ifnull(t2.app_vol, 0) as unconfirmed_vol,
        0  as locking_period_vol,
        0 as just_frzn_vol
        from cust_books_dtl t2
        <where>
            t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
            <if test="disCodeList != null and disCodeList.size() > 0 ">
                and t2.dis_code  in
                <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                    #{disCode}
                </foreach>
            </if>
            <if test="productCode != null and productCode != ''">
                and t2.product_code = #{productCode,jdbcType=VARCHAR}
            </if>
            <if test="cpAcctNo != null and cpAcctNo != ''">
                and t2.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
            </if>
            and t2.protocol_type = '4'
            and t2.product_channel ='6'
        </where>
        union all
        select t3.product_code as product_code,
        t3.fund_share_class as fund_share_class,
        t3.protocol_no as protocol_no,
        t3.cp_acct_no as cp_acct_no,
        ifnull(t3.balance_vol, 0) as balance_vol,
        ifnull(t3.unconfirmed_vol, 0) as unconfirmed_vol,
        0  as locking_period_vol,
        ifnull(t3.just_frzn_vol, 0) as just_frzn_vol
        from cust_books t3
        where t3.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.dis_code  in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test=" productCode != null and productCode != ''">
            and t3.product_code = #{productCode,jdbcType=VARCHAR}
        </if>
        <if test=" cpAcctNo != null and cpAcctNo != ''">
            and t3.cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR}
        </if>
        and t3.protocol_type = '4'
        and  t3.product_channel ='6') t
        group by t.protocol_no, t.product_code, t.fund_share_class, t.cp_acct_no
        ) tt
        where tt.balance_vol &gt; 0
        and not exists( select 1 from SUB_CUST_BOOKS t where t.FUND_CODE = tt.FUND_CODE  and t.CP_ACCT_NO = tt.CP_ACCT_NO)
    </select>


  
</mapper>