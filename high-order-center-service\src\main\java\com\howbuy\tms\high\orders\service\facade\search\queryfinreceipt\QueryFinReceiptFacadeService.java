/**
 * Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfinreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.paramcenter.serverfacade.dto.FundManDto;
import com.howbuy.paramcenter.serverfacade.dto.TaDto;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.outerservice.paramcenter.queryalltafacade.QueryAllTaService;
import com.howbuy.tms.common.outerservice.paramcenter.queryfundman.QueryFundManService;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptRequest;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptResponse;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description:查询在途笔数、资金到账提醒数据接口实现
 * @reason:
 * @date 202年1月10日 下午8:24:12
 * @since JDK 1.8
 */
@DubboService
@Service("queryFinReceiptFacade")
public class QueryFinReceiptFacadeService implements QueryFinReceiptFacade {

    private static final Logger logger = LogManager.getLogger(QueryFinReceiptFacadeService.class);

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private TradeDayService tradeDayService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryFundManService queryFundManService;

    @Autowired
    private QueryAllTaService queryAllTaService;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade.execute(QueryFinReceiptRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryFinReceiptFacadeService
     * @apiName execute
     * @apiDescription 查询在途笔数、资金到账提醒数据接口实现
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} onlyHkProduct 是否仅需香港产品
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=a251NH&hbOneNo=XQ&pageSize=8517&disCode=Utv&txChannel=yNRoFsZ&appTm=fNn&disCodeList=mJTC&subOutletCode=ZhL4Lx&pageNo=4988&operIp=jvw6EpT&txAcctNo=bTU1hPz&onlyHkProduct=s&appDt=mMo1KAYe&dataTrack=4eYdaJ3l0&notFilterHkFund=Pmze&txCode=kwt&outletCode=5O
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} hbOneNo 一账通账号
     * @apiSuccess (响应结果) {Number} buyUnrefundedPiece 购买待退款订单数
     * @apiSuccess (响应结果) {Number} redeemUnrefundedPiece 赎回待回款订单数
     * @apiSuccess (响应结果) {Array} unpaidList 待付款订单
     * @apiSuccess (响应结果) {String} unpaidList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unpaidList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {Array} unconfirmedList 待确认订单
     * @apiSuccess (响应结果) {String} unconfirmedList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unconfirmedList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"p","hbOneNo":"R7WZ","totalPage":3439,"pageNo":7413,"txAcctNo":"fFmMkOStY","unpaidList":[{"dealNo":"uMUAcfFy","dealType":"i"}],"description":"PZS","buyUnrefundedPiece":4768,"redeemUnrefundedPiece":5889,"totalCount":9102,"unconfirmedList":[{"dealNo":"gskzZG","dealType":"lGxcn1y"}]}
     */
    @Override
    public QueryFinReceiptResponse execute(QueryFinReceiptRequest request) {
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
        QueryFinReceiptResponse response = new QueryFinReceiptResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        List<String> disCodeList = request.getDisCodeList();
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(txAcctNo)) {
            logger.error("QueryFinReceiptResponse|execute() txAcctNo is null, hbOneNo:{}", hbOneNo);
            response.setReturnCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR));
            return response;
        }

        response.setTxAcctNo(txAcctNo);
        response.setHbOneNo(hbOneNo);
        // 获取资金提醒笔数
        getRefundList(response, txAcctNo, disCodeList, request.getNotFilterHkFund(), request.getNotFilterHzFund(), request.getOnlyHkProduct());
        // 在途笔数
        getOnWay(response, txAcctNo, disCodeList, request.getNotFilterHkFund(), request.getNotFilterHzFund(), request.getOnlyHkProduct());

        return response;
    }

    /**
     * 获取资金提醒笔数
     *
     * @param response
     * @param txAcctNo
     * @param disCodeList
     */
    private void getRefundList(QueryFinReceiptResponse response, String txAcctNo, List<String> disCodeList, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        // 获取当前自然日
        String naturalDt = DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN);
        // 获取当前工作日
        String tradeDt = queryTradeDayOuterService.getWorkDay(new Date());
        // 获取前3个工作日的日期
        String before3TradeDay = tradeDayService.addTradeDaysByCache(tradeDt, -3);
        // 获取前15个工作日的日期
        String before15TradeDay = tradeDayService.addTradeDaysByCache(tradeDt, -15);
        List<DealOrderVo> buyRefundList = new ArrayList<>();
        List<DealOrderVo> redeemReturnList = new ArrayList<>();
        try {
            // 如果只需要查询香港的,那么下单未退款的就不需要查询了,因为这种都是代销,代销没有香港的
            if (StringUtils.isBlank(onlyHkProduct) || YesOrNoEnum.NO.getCode().equals(onlyHkProduct)) {
                buyRefundList = getBuyRefundList(txAcctNo, disCodeList, naturalDt, before3TradeDay, notFilterHkFund, notFilterHzFund);
            }
            // 赎回待回款订单数：取满足以下条件的订单笔数
            redeemReturnList = getRedeemReturnList(txAcctNo, disCodeList, naturalDt, before3TradeDay, before15TradeDay, notFilterHkFund, notFilterHzFund, onlyHkProduct);
        } catch (Exception e) {
            logger.error("QueryFinReceiptFacadeService|e:", e);
        }
        response.setBuyUnrefundedPiece(buyRefundList.size());
        response.setRedeemUnrefundedPiece(redeemReturnList.size());
    }

    /**
     * 获取在途笔数
     *
     * @param response
     * @param txAcctNo
     * @param disCodeList
     */
    private void getOnWay(QueryFinReceiptResponse response, String txAcctNo, List<String> disCodeList, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        // 待付款订单
        List<QueryAcctBalanceResponse.DealOrderBean> unpaidList = new ArrayList<>();
        // 待确认订单
        List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList = new ArrayList<>();

        // 待付款订单
        List<DealOrderVo> dbUnpaidList = dealOrderRepository.selectUnpaid(disCodeList, txAcctNo,  notFilterHkFund,  notFilterHzFund,onlyHkProduct);
        if (CollectionUtil.isNotEmpty(dbUnpaidList)) {
            dbUnpaidList = excludeMergeSubmit(dbUnpaidList);
            unpaidList = JSON.parseArray(JSON.toJSONString(dbUnpaidList), QueryAcctBalanceResponse.DealOrderBean.class);
        }
        // 待确认订单数-代销unconfirmedList
        List<DealOrderVo> dbUnconfirmedList = dealOrderRepository.selectUnconfirmed(disCodeList, txAcctNo, notFilterHkFund,  notFilterHzFund,onlyHkProduct);
        if (CollectionUtil.isNotEmpty(dbUnconfirmedList)) {
            dbUnconfirmedList = excludeMergeSubmit(dbUnconfirmedList);
            unconfirmedList = JSON.parseArray(JSON.toJSONString(dbUnconfirmedList), QueryAcctBalanceResponse.DealOrderBean.class);
        }
        // 待付款订单
        response.setUnpaidList(unpaidList);
        // 待确认订单
        response.setUnconfirmedList(unconfirmedList);
    }

    /**
     * 购买待退款订单数
     *
     * @param txAcctNo
     * @param disCodeList
     * @param naturalDt
     * @param before3TradeDay
     * @return
     */
    private List<DealOrderVo> getBuyRefundList(String txAcctNo, List<String> disCodeList, String naturalDt, String before3TradeDay, String notFilterHkFund, String notFilterHzFund) {
        List<DealOrderVo> buyRefundList = new ArrayList<>();
        // 代销
        List<DealOrderVo> consignmentBuyRefundList = dealOrderRepository.selectConsignmentBuyRefund(disCodeList,
                txAcctNo, before3TradeDay, naturalDt, before3TradeDay, notFilterHkFund, notFilterHzFund);
        if (CollectionUtil.isEmpty(consignmentBuyRefundList)) {
            logger.info("QueryFinReceiptFacadeService|getConsignmentBuyRefund is empty");
            return buyRefundList;
        }
        // 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
        consignmentBuyRefundList = excludeMergeSubmit(consignmentBuyRefundList);
        buyRefundList.addAll(consignmentBuyRefundList);
        // 直销-无
        return buyRefundList;
    }

    /**
     * 赎回待回款订单数
     *
     * @param txAcctNo
     * @param disCodeList
     * @param naturalDt
     * @param before3TradeDay
     * @param before15TradeDay
     * @return
     */
    private List<DealOrderVo> getRedeemReturnList(String txAcctNo, List<String> disCodeList, String naturalDt,
                                                  String before3TradeDay, String before15TradeDay, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        List<DealOrderVo> redeemReturnList = new ArrayList<>();
        // 代销
        if (StringUtils.isBlank(onlyHkProduct) || YesOrNoEnum.NO.getCode().equals(onlyHkProduct)) {
            // 代销
            List<DealOrderVo> consignmentRedeemReturnList = getConsignmentRedeemReturnList(txAcctNo, disCodeList,
                    naturalDt, before15TradeDay, before3TradeDay, notFilterHkFund, notFilterHzFund);
            redeemReturnList.addAll(consignmentRedeemReturnList);
        }
        // 直销
        List<DealOrderVo> dbUnconfirmedList = dealOrderRepository.selectDirectRedeemReturn(disCodeList,
                txAcctNo, before3TradeDay, notFilterHkFund, notFilterHzFund,onlyHkProduct);
        if (dbUnconfirmedList == null) {
            logger.info("QueryFinReceiptFacadeService|getDirectRedeemReturn is null,disCodeList:{}," +
                    "txAcctNo:{},before3TradeDay:{}", disCodeList, txAcctNo, before15TradeDay);
            dbUnconfirmedList = new ArrayList<>();
        }
        redeemReturnList.addAll(dbUnconfirmedList);

        logger.info("getRedeemReturnList-带赎回订单,redeemReturnList={}", JSON.toJSONString(redeemReturnList));
        return redeemReturnList;
    }

    /**
     * 赎回待回款订单数-代销
     *
     * @param txAcctNo
     * @param disCodeList
     * @param naturalDt
     * @param before15TradeDay
     * @return
     */
    private List<DealOrderVo> getConsignmentRedeemReturnList(String txAcctNo, List<String> disCodeList, String naturalDt,
                                                             String before15TradeDay, String before3TradeDay, String notFilterHkFund, String notFilterHzFund) {
        List<DealOrderVo> consignmentRedeemReturnList = new ArrayList<>();
        // 1\【资金状态】不是已出款的、当前工作日-确认日期&lt;=15个工作日的、基金所属的TA不是直销、或者基金所属的TA是直销且基金管理人生成托管指令的、
        //          现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
        //          add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
        // 2\【资金状态】不是已出款的、当前工作日-确认日期&lt;=3个工作日的、基金所属的TA是直销且基金管理人不生成托管指令的、
        //          现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
        //          add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
        List<DealOrderVo> consignmentRedeemReturn15List = new ArrayList<>();
        List<DealOrderVo> consignmentRedeemReturn3List = new ArrayList<>();
        List<DealOrderVo> consignmentRedeemReturn3ListUnFilter = new ArrayList<>();
        List<String> orders = new ArrayList<>();
        // 查询
        List<DealOrderVo> consignmentRedeemReturnNotPayList = dealOrderRepository.selectConsignmentRedeemReturnNotPay(
                disCodeList, txAcctNo, before15TradeDay,  notFilterHkFund, notFilterHzFund);
        if (CollectionUtil.isEmpty(consignmentRedeemReturnNotPayList)) {
            logger.info("QueryFinReceiptFacadeService|getConsignmentRedeemReturnNotPay is empty,disCodeList:{},txAcctNo:{}," +
                    "before15TradeDay:{}", disCodeList, txAcctNo, before15TradeDay);
        } else {
            List<String> productCodes = consignmentRedeemReturnNotPayList.stream().map(DealOrderVo::getProductCode)
                    .distinct().collect(Collectors.toList());
            // 查询产品基本信息，获取ta及基金管理人
            List<HighProductBaseInfoBean> highProductBaseInfoList = queryHighProductOuterService.getHighProductBaseInfoList(productCodes);
            if (highProductBaseInfoList == null) {
                logger.info("QueryFinReceiptFacadeService|getHighProductBaseInfoList is null,productCodes:{}", productCodes);
                highProductBaseInfoList = new ArrayList<>();
            }
            // 转map
            Map<String, HighProductBaseInfoBean> highProductBaseInfoMap = highProductBaseInfoList.stream()
                    .collect(Collectors.toMap(HighProductBaseInfoBean::getFundCode, bean -> bean));
            // 查询 isCreEntrustOrder-基金管理人是否生成托管指令 0-否；1-是
            Map<String, FundManDto> fundManDtoMap = queryFundManService.execute();
            // 查询TA是否直销
            Map<String, TaDto> taDtoMap = queryAllTaService.execute();
            // 设置产品类型信息
            setFundTypeInfo(consignmentRedeemReturn15List, consignmentRedeemReturn3ListUnFilter, orders, consignmentRedeemReturnNotPayList, highProductBaseInfoMap, fundManDtoMap, taDtoMap);
            // 由于sql查询时，确认日期范围是15天，需求要求3天，需过滤
            consignmentRedeemReturn3List = consignmentRedeemReturn3ListUnFilter.stream().filter(
                    vo -> Integer.parseInt(vo.getAckDt()) >= Integer.parseInt(before3TradeDay)).collect(Collectors.toList());

            consignmentRedeemReturnList.addAll(consignmentRedeemReturn15List);
            consignmentRedeemReturnList.addAll(consignmentRedeemReturn3List);
        }

        // 3【资金状态】是已出款的、【出款日期】=当前工作日、当前工作日-确认日期<=15个工作日的、
        //          现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】， add 确认金额不为0的 20230209
        //          add 过滤掉异常赎回的交易、确认状态是终态（确认成功、确认失败、部分确认的） 20230213
        List<DealOrderVo> consignmentRedeemReturnPayList = dealOrderRepository.selectConsignmentRedeemReturnPay(disCodeList,
                txAcctNo, naturalDt, before15TradeDay, notFilterHkFund, notFilterHzFund);
        if (CollectionUtil.isNotEmpty(consignmentRedeemReturnPayList)) {
            for (DealOrderVo vo : consignmentRedeemReturnPayList) {
                if (orders.contains(vo.getDealNo())) {
                    continue;
                }
                orders.add(vo.getDealNo());
                consignmentRedeemReturnList.add(vo);
            }
        }

        if (CollectionUtil.isEmpty(consignmentRedeemReturnList)) {
            return consignmentRedeemReturnList;
        }
        // 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
        consignmentRedeemReturnList = excludeMergeSubmit(consignmentRedeemReturnList);
        logger.info("getConsignmentRedeemReturnList-consignmentRedeemReturnList={}", JSON.toJSONString(consignmentRedeemReturnList));
        return consignmentRedeemReturnList;
    }

    /**
     * 设置产品类型信息
     */
    private void setFundTypeInfo(List<DealOrderVo> consignmentRedeemReturn15List, List<DealOrderVo> consignmentRedeemReturn3ListUnFilter, List<String> orders, List<DealOrderVo> consignmentRedeemReturnNotPayList, Map<String, HighProductBaseInfoBean> highProductBaseInfoMap, Map<String, FundManDto> fundManDtoMap, Map<String, TaDto> taDtoMap) {
        // 遍历分类
        for (DealOrderVo vo : consignmentRedeemReturnNotPayList) {

            // 拆单处理
            if (orders.contains(vo.getDealNo())) {
                continue;
            }
            orders.add(vo.getDealNo());

            HighProductBaseInfoBean productBaseInfo = highProductBaseInfoMap.get(vo.getProductCode());
            if (productBaseInfo == null) {
                logger.error("QueryFinReceiptFacadeService|productBaseInfo is null, productCode:{}", vo.getProductCode());
                continue;
            }
            TaDto taDto = taDtoMap.get(productBaseInfo.getTaCode());
            if (taDto == null) {
                logger.error("QueryFinReceiptFacadeService|taDto is null, taCode:{}", productBaseInfo.getTaCode());
                continue;
            }
            FundManDto fundManDto = fundManDtoMap.get(productBaseInfo.getFundManCode());
            if (fundManDto == null) {
                logger.error("QueryFinReceiptFacadeService|fundManDto is null, fundManCode:{}", productBaseInfo.getFundManCode());
                continue;
            }
            // TaDto#saleType:0-直销，1-代销
            // FundManDto#isCreEntrustOrder:0-否；1-是
            if ("1".equals(taDto.getSaleType()) || ("0".equals(taDto.getSaleType()) && "1".equals(fundManDto.getIsCreEntrustOrder()))) {
                // 基金所属的TA不是直销、或者基金所属的TA是直销且基金管理人生成托管指令
                consignmentRedeemReturn15List.add(vo);
            } else if ("0".equals(taDto.getSaleType()) && "0".equals(fundManDto.getIsCreEntrustOrder())) {
                //基金所属的TA是直销且基金管理人不生成托管指令
                consignmentRedeemReturn3ListUnFilter.add(vo);
            } else {
                logger.error("QueryFinReceiptFacadeService|存在未匹配场景,TaDto#taCode:{},TaDto#saleType:{},FundManDto#fundManCode:{}," +
                                "FundManDto#isCreEntrustOrder{}", productBaseInfo.getTaCode(), taDto.getSaleType(),
                        productBaseInfo.getFundManCode(), fundManDto.getIsCreEntrustOrder());
            }
        }
    }

    /**
     * 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
     *
     * @param list
     */
    private List<DealOrderVo> excludeMergeSubmit(List<DealOrderVo> list) {
        Set<String> consignmentBuyRefundSet = new HashSet<>();
        List<DealOrderVo> returnList = new ArrayList<>();
        for (DealOrderVo vo : list) {
            if (YesOrNoEnum.YES.getCode().equals(vo.getMergeSubmitFlag())) {
                if (!consignmentBuyRefundSet.contains(vo.getMainDealOrderNo())) {
                    consignmentBuyRefundSet.add(vo.getMainDealOrderNo());
                    returnList.add(vo);
                }
            } else {
                returnList.add(vo);
            }
        }
        return returnList;
    }
}
