package com.howbuy.tms.high.orders.service.repository;

import com.howbuy.tms.common.enums.database.OrderStatusEnum;
import com.howbuy.tms.high.orders.dao.mapper.customize.CmCusttradeDirectPoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPoExample;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class CmCusttradeDirectRepository {
    @Autowired
    private CmCusttradeDirectPoMapper cmCusttradeDirectPoMapper;

    public List<BalanceOrderVo> selectBalanceDirectOrderVo(String hbOneNo, List<String> fundCodeList, List<String> disCodeList) {
        return cmCusttradeDirectPoMapper.selectBalanceDirectOrderVo(hbOneNo, fundCodeList, disCodeList);
    }

    public List<CmCusttradeDirectPo> getOnWayDirectBalance(QueryAcctBalanceBaseInfoParamVo paramVo) {
        return cmCusttradeDirectPoMapper.getOnWayDirectBalance(paramVo);
    }

    public CmCusttradeDirectPo getFirstAckInfoForDirect(String hbOneNo, String productCode) {
        return cmCusttradeDirectPoMapper.getFirstAckInfoForDirect(hbOneNo, productCode);
    }

    public CmCusttradeDirectPo getLastAckInfoForDirect(String hbOneNo, String productCode) {
        return cmCusttradeDirectPoMapper.getLastAckInfoForDirect(hbOneNo, productCode);
    }

    public List<AckDealOrderInfo> selectAckDealDtl(String hbOneNo, List<String> disCodeList, List<String> fundCodeList) {
        return cmCusttradeDirectPoMapper.selectAckDealDtl(hbOneNo, disCodeList, fundCodeList);
    }

    public List<String> queryNoConfirmFundCodeList(List<String> noConfirmedOrderFundCodeList, String hboneNo) {
        if (CollectionUtils.isEmpty(noConfirmedOrderFundCodeList) || StringUtils.isBlank(hboneNo)) {
            return noConfirmedOrderFundCodeList;
        }
        // 1.查询出有确认交易记录的订单
        List<String> confirmedOrderStatusList = new ArrayList<>();
        confirmedOrderStatusList.add(OrderStatusEnum.PART_ACK_SUCCESS.getCode());
        confirmedOrderStatusList.add(OrderStatusEnum.ACK_SUCCESS.getCode());
        CmCusttradeDirectPoExample cmCusttradeDirectPoExample = new CmCusttradeDirectPoExample();
        cmCusttradeDirectPoExample.createCriteria().andFundcodeIn(noConfirmedOrderFundCodeList).andHbonenoEqualTo(hboneNo).andOrderstateIn(confirmedOrderStatusList);
        List<CmCusttradeDirectPo> confirmedList = cmCusttradeDirectPoMapper.selectByExample(cmCusttradeDirectPoExample);
        if (CollectionUtils.isEmpty(confirmedList)) {
            return noConfirmedOrderFundCodeList;
        }
        // 2.将有确认订单的过滤掉
        List<String> haveConfirmFundCodeList = confirmedList.stream().map(CmCusttradeDirectPo::getFundcode).distinct().collect(Collectors.toList());
        noConfirmedOrderFundCodeList = noConfirmedOrderFundCodeList.stream().filter(x -> !haveConfirmFundCodeList.contains(x)).collect(Collectors.toList());
        return noConfirmedOrderFundCodeList;
    }
}
