<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath />
	</parent>


	<groupId>com.howbuy.fbs</groupId>
	<artifactId>fbs-online</artifactId>
	<version>3.40.5-RELEASE</version>
	<packaging>pom</packaging>
	<name>fbs-online</name>

    <properties>
		<java.version>1.8</java.version>
		<maven.test.skip>true</maven.test.skip>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<dubbo.version>2.7.15</dubbo.version>
		<druid.version>1.1.22</druid.version>
		<commons.pool.version>1.6</commons.pool.version>
		<mybatis.plus.version>3.4.2</mybatis.plus.version>
		<hps.verison>RELEASE</hps.verison>
		<freemarker.version>2.3.30</freemarker.version>
		<hutool.version>5.8.0</hutool.version>
		<fastjson.version>1.2.47</fastjson.version>
		<guava.version>19.0</guava.version>
		<fst.version>2.57</fst.version>
		<caffeine.version>2.9.0</caffeine.version>
		<powermock.version>2.0.2</powermock.version>
		<zk.version>3.4.14</zk.version>
		<curator.version>4.2.0</curator.version>
		<ehcache.version>1.3.0</ehcache.version>
		<jedis.version>2.9.3</jedis.version>
		
		<lombok.version>1.18.24</lombok.version>
		<joda.time.version>2.3</joda.time.version>
		<nacos.version>2.2.8.RELEASE</nacos.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
	    <com.howbuy.fbs-online.version>3.40.5-RELEASE</com.howbuy.fbs-online.version>
		<fds.batch.center.facade.new>RELEASE</fds.batch.center.facade.new>
		<com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
		<com.howbuy.acc-center-facade.version>3.6.3-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
		<com.howbuy.hps-boot-starter.version>3.7.0-RELEASE</com.howbuy.hps-boot-starter.version>
		<com.howbuy.fbs-common-facade.version>3.17.0-RELEASE</com.howbuy.fbs-common-facade.version>
		<com.howbuy.fbs-common-utils.version>3.7.0-RELEASE</com.howbuy.fbs-common-utils.version>
		<com.howbuy.howbuy-auth-facade.version>2.2.0-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.param-center-new.version>3.40.5-RELEASE</com.howbuy.param-center-new.version>
		<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.github.ben-manes.caffeine</groupId>
				<artifactId>caffeine</artifactId>
				<version>${caffeine.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zk.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-framework</artifactId>
				<version>${curator.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-recipes</artifactId>
				<version>${curator.version}</version>
			</dependency>
			<dependency>
				<artifactId>druid</artifactId>
				<groupId>com.alibaba</groupId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fds</groupId>
				<artifactId>hps-boot-starter</artifactId>
				<version>${com.howbuy.hps-boot-starter.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.javassist</groupId>
						<artifactId>javassist</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis.plus.version}</version>
			</dependency>
			<dependency>
				<groupId>org.freemarker</groupId>
				<artifactId>freemarker</artifactId>
				<version>${freemarker.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>de.ruedigermoeller</groupId>
				<artifactId>fst</artifactId>
				<version>${fst.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-facade</artifactId>
				<version>${com.howbuy.fbs-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-service</artifactId>
				<version>${com.howbuy.fbs-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-dao</artifactId>
				<version>${com.howbuy.fbs-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-common-utils</artifactId>
				<version>${com.howbuy.fbs-common-utils.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>howbuy-cache-client-trade</artifactId>
						<groupId>com.howbuy.pa.cache</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-facade</artifactId>
				<version>${com.howbuy.acc-center-facade.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>common-facade</artifactId>
						<groupId>com.howbuy.common</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.acc</groupId>
						<artifactId>acc-common-cache</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.pa.cache</groupId>
						<artifactId>howbuy-cache-client-trade</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.empire-db</groupId>
						<artifactId>empire-db</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-utils</artifactId>
				<version>${com.howbuy.acc-common-utils.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
					<exclusion>
						<artifactId>acc-common-cache</artifactId>
						<groupId>com.howbuy.acc</groupId>
					</exclusion>
                </exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-auth-facade</artifactId>
				<version>${com.howbuy.howbuy-auth-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>org.javassist</groupId>
				<artifactId>javassist</artifactId>
				<version>3.21.0-GA</version>
			</dependency>
			<dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>param-server-facade</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>${nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-module-testng</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-api-mockito2</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
				<version>${ehcache.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>druid</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>



</project>