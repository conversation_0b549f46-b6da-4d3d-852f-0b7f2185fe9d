package com.howbuy.tms.high.orders.service.facade.search.queryesorderrecordlist;

import com.google.common.collect.Lists;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.OrderStatusEnum;
import com.howbuy.tms.common.enums.elasticsearch.ProductClassEnum;
import com.howbuy.tms.common.enums.elasticsearch.TradeStatusEnum;
import com.howbuy.tms.common.enums.elasticsearch.TradeTypeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.elasticsearch.facade.query.querytraderecord.QueryTradeRecordFacade;
import com.howbuy.tms.elasticsearch.facade.query.querytraderecord.QueryTradeRecordRequest;
import com.howbuy.tms.elasticsearch.facade.query.querytraderecord.QueryTradeRecordResponse;
import com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist.QueryEsOrderRecordListFacade;
import com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist.QueryEsOrderRecordListRequest;
import com.howbuy.tms.high.orders.facade.search.queryesorderrecordlist.QueryEsOrderRecordListResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:查询es交易记录
 * @Author: yun.lu
 * Date: 2025/6/24 9:49
 */
@DubboService
@Service("queryEsOrderRecordListFacade")
public class QueryEsOrderRecordListService implements QueryEsOrderRecordListFacade {
    @Autowired
    private QueryTradeRecordFacade queryTradeRecordFacade;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;


    @Override
    public QueryEsOrderRecordListResponse execute(QueryEsOrderRecordListRequest queryEsOrderRecordListRequest) {
        QueryEsOrderRecordListResponse response = new QueryEsOrderRecordListResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 1.构建查询入参
        QueryTradeRecordRequest queryTradeRecordRequest = buildEsOrderRecordListRequest(queryEsOrderRecordListRequest);
        // 2.查询es交易记录
        QueryTradeRecordResponse recordResponse = queryTradeRecordFacade.execute(queryTradeRecordRequest);
        response.setTotalCount(recordResponse.getTotalCount());
        response.setTotalPage(recordResponse.getTotalPage());
        response.setPageNo(recordResponse.getPageNo());
        List<QueryTradeRecordResponse.TradeInfoBean> tradeInfoList = recordResponse.getTradeInfoList();
        if (CollectionUtils.isEmpty(tradeInfoList)) {
            return response;
        }
        // 3.交易记录结果转换
        List<String> fundCodeList = tradeInfoList.stream().map(QueryTradeRecordResponse.TradeInfoBean::gettFundCode).distinct().collect(Collectors.toList());
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        List<QueryEsOrderRecordListResponse.DealOrderBean> dealOrderList = new ArrayList<>(tradeInfoList.size());
        for (QueryTradeRecordResponse.TradeInfoBean tradeInfoBean : tradeInfoList) {
            QueryEsOrderRecordListResponse.DealOrderBean dealOrderBean = new QueryEsOrderRecordListResponse.DealOrderBean();
            // 基本订单信息
            dealOrderBean.setDealNo(tradeInfoBean.getDealNo());
            dealOrderBean.setDisCode(tradeInfoBean.getDisCode());
            dealOrderBean.setTxAcctNo(queryTradeRecordRequest.getTxAcctNo());
            dealOrderBean.setCpAcctNo(tradeInfoBean.getCpAcctNo());
            dealOrderBean.setBankAcct(tradeInfoBean.getBankAcct());
            dealOrderBean.setBankCode(tradeInfoBean.getBankCode());
            dealOrderBean.setTradeStatus(tradeInfoBean.getTradeStatus());
            // 产品信息
            dealOrderBean.setProductCode(tradeInfoBean.gettFundCode());
            dealOrderBean.setProductName(tradeInfoBean.getProductName());
            HighProductDBInfoBean productInfo = highProductDbInfoBeanMap.get(tradeInfoBean.gettFundCode());
            if (productInfo != null) {
                dealOrderBean.setProductAttr(productInfo.getFundAttr());
                dealOrderBean.setProductType(productInfo.getFundType());
                dealOrderBean.setProductSubType(productInfo.getFundSubType());
                dealOrderBean.setCurrency(productInfo.getCurrency());
                dealOrderBean.setHkSaleFlag(productInfo.getHkSaleFlag());
            }
            // 交易金额信息
            dealOrderBean.setAppAmt(tradeInfoBean.getAppAmt());
            dealOrderBean.setAppVol(tradeInfoBean.getAppVol());
            dealOrderBean.setAckAmt(tradeInfoBean.getAckAmt());
            // 交易日期信息
            if (StringUtils.isNotEmpty(tradeInfoBean.getAppDate()) && StringUtils.isNotEmpty(tradeInfoBean.getAppTime())) {
                try {
                    String appDateTime = tradeInfoBean.getAppDate() + tradeInfoBean.getAppTime();
                    dealOrderBean.setAppDtm(DateUtils.formatToDate(appDateTime, DateUtils.YYYYMMDDHHMMSS));
                } catch (Exception e) {
                    // 如果解析失败，设置为null
                    dealOrderBean.setAppDtm(null);
                }
            }
            dealOrderBean.setTaTradeDt(tradeInfoBean.getTaTradeDt());
            dealOrderBean.setAckDt(tradeInfoBean.getAckDt());
            dealOrderBean.setSubmitTaDt(tradeInfoBean.getAppDate());
            // 交易状态信息
            dealOrderBean.setOrderStatus(convertTradeStatusToOrderStatus(tradeInfoBean.getTradeStatus()));
            dealOrderBean.setPayStatus(convertTradeStatusToPayStatus(tradeInfoBean.getTradeStatus()));
            // 业务类型
            dealOrderBean.setmBusiCode(tradeInfoBean.getmBusiCode());
            // 分红方式
            dealOrderBean.setDivMode(tradeInfoBean.getFundDivMode());
            // 销售类型：默认为代销
            dealOrderBean.setScaleType("2");
            // 确认标识
            dealOrderBean.setTxAckFlag(StringUtils.isNotEmpty(tradeInfoBean.getAckDt()) ? "1" : "0");
            dealOrderList.add(dealOrderBean);
        }
        response.setDealOrderList(dealOrderList);
        return response;
    }

    /**
     * 构建查询交易记录列表入参
     */
    private QueryTradeRecordRequest buildEsOrderRecordListRequest(QueryEsOrderRecordListRequest queryEsOrderRecordListRequest) {
        QueryTradeRecordRequest queryTradeRecordRequest = new QueryTradeRecordRequest();
        // 账号处理
        String txAcctNo = queryEsOrderRecordListRequest.getTxAcctNo();
        String hbOneNo = queryEsOrderRecordListRequest.getHbOneNo();
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        queryTradeRecordRequest.setTxAcctNo(txAcctNo);
        // 业务类型转换
        if (CollectionUtils.isNotEmpty(queryEsOrderRecordListRequest.getmBusiCodeList())) {
            queryTradeRecordRequest.setTradeSubTypeList(buildTradeSubTypeList(queryEsOrderRecordListRequest.getmBusiCodeList()));
        }
        // 订单状态转换
        if (CollectionUtils.isNotEmpty(queryEsOrderRecordListRequest.getOrderStatusList())) {
            queryTradeRecordRequest.setTradeStatusList(buildTradeStatusList(queryEsOrderRecordListRequest.getOrderStatusList()));
        }
        queryTradeRecordRequest.setAppDateStart(queryEsOrderRecordListRequest.getAppDateStart());
        queryTradeRecordRequest.setAppDateEnd(queryEsOrderRecordListRequest.getAppDateEnd());
        if(StringUtils.isNotBlank(queryEsOrderRecordListRequest.getCpAcctNo())){
            queryTradeRecordRequest.setCpAcctNoList(Collections.singletonList(queryEsOrderRecordListRequest.getCpAcctNo()));
        }
        queryTradeRecordRequest.setProductName(queryEsOrderRecordListRequest.getFundName());
        // 授权条件转换
        if (YesOrNoEnum.NO.getCode().equals(queryEsOrderRecordListRequest.getNotFilterHkFund())) {
            queryTradeRecordRequest.setContainsHkProduct(YesOrNoEnum.NO.getCode());
            queryTradeRecordRequest.setHkAssetIsolateFlag(YesOrNoEnum.YES.getCode());
        } else {
            queryTradeRecordRequest.setContainsHkProduct(YesOrNoEnum.YES.getCode());
            queryTradeRecordRequest.setHkAssetIsolateFlag(YesOrNoEnum.NO.getCode());
        }
        List<String> disCodeList = getDisCodeList(queryEsOrderRecordListRequest);
        if (YesOrNoEnum.NO.getCode().equals(queryEsOrderRecordListRequest.getNotFilterHzFund()) && CollectionUtils.isNotEmpty(disCodeList)) {
            disCodeList.remove(DisCodeEnum.HZ.getCode());
            queryTradeRecordRequest.setDisCodes(disCodeList);
        } else {
            queryTradeRecordRequest.setDisCodes(disCodeList);
        }
        queryTradeRecordRequest.setIsAuth(queryEsOrderRecordListRequest.getIsAuth());
        // 只查询私募的
        queryTradeRecordRequest.setProductCategoryList(Lists.newArrayList(ProductClassEnum.SM.getCode()
                , ProductClassEnum.FIXED_SM.getCode()
                , ProductClassEnum.STOCK_SM.getCode()
                , ProductClassEnum.SUN_SM.getCode()));
        queryTradeRecordRequest.setPageNo(queryEsOrderRecordListRequest.getPageNo());
        queryTradeRecordRequest.setPageSize(queryEsOrderRecordListRequest.getPageSize());
        return queryTradeRecordRequest;
    }

    /**
     * 分销渠道转换
     */
    private List<String> getDisCodeList(QueryEsOrderRecordListRequest queryEsOrderRecordListRequest) {
        List<String> disCodeList = queryEsOrderRecordListRequest.getDisCodeList();
        if (CollectionUtils.isEmpty(disCodeList)) {
            disCodeList = new ArrayList<>();
            disCodeList.add(DisCodeEnum.HZ.getCode());
            disCodeList.add(DisCodeEnum.HM.getCode());
            disCodeList.add(DisCodeEnum.OTC_MIDDLE.getCode());
        }
        return disCodeList;
    }

    /**
     * 交易状态转换
     */
    private List<String> buildTradeStatusList(List<String> orderStatusList) {
        Set<String> tradeStatusList = new HashSet<>();
        for (String orderStatus : orderStatusList) {
            if (OrderStatusEnum.APP_SUCCESS.getCode().equals(orderStatus)) {
                tradeStatusList.add(TradeStatusEnum.CONFIRM.getCode());
                tradeStatusList.add(TradeStatusEnum.PAYING.getCode());
                tradeStatusList.add(TradeStatusEnum.PAY_SUCCESS.getCode());
                tradeStatusList.add(TradeStatusEnum.SM_PART_SUC_CONFIRM.getCode());
                tradeStatusList.add(TradeStatusEnum.WAIT_CONFIRM.getCode());
            } else if (OrderStatusEnum.SELF_CANCELED.getCode().equals(orderStatus) || OrderStatusEnum.FORCE_CANCELED.getCode().equals(orderStatus)) {
                tradeStatusList.add(TradeStatusEnum.CANCEL.getCode());
                tradeStatusList.add(TradeStatusEnum.CANCELING.getCode());
                tradeStatusList.add(TradeStatusEnum.FORCE_CANCEL.getCode());
            } else if (OrderStatusEnum.PART_ACK_SUCCESS.getCode().equals(orderStatus) || OrderStatusEnum.ACK_SUCCESS.getCode().equals(orderStatus)) {
                tradeStatusList.add(TradeStatusEnum.PART_SUCCESS.getCode());
                tradeStatusList.add(TradeStatusEnum.TRADE_SUCCESS.getCode());
            } else if (OrderStatusEnum.ACK_FAIL.getCode().equals(orderStatus)) {
                tradeStatusList.add(TradeStatusEnum.TRADE_FAIL.getCode());
            }
        }
        return new ArrayList<>(tradeStatusList);
    }

    /**
     * 交易类型转换
     */
    private List<String> buildTradeSubTypeList(List<String> mBusiCodeList) {
        List<String> subTradeTypeList = new ArrayList<>();
        for (String mBusiCode : mBusiCodeList) {
            if (BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode) || BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)) {
                subTradeTypeList.add(TradeTypeEnum.BUY_A.getSubCode());
            } else if (BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode)) {
                subTradeTypeList.add(TradeTypeEnum.SELL_B.getSubCode());
            } else if (BusinessCodeEnum.DIV.getMCode().equals(mBusiCode)) {
                subTradeTypeList.add(TradeTypeEnum.BONUS_ZT.getSubCode());
            } else if (BusinessCodeEnum.DIV_MODE_CHANGE.getMCode().equals(mBusiCode)) {
                subTradeTypeList.add(TradeTypeEnum.OTHER_A.getSubCode());
            }
        }
        return subTradeTypeList;
    }

    /**
     * 将ES交易状态转换为订单状态
     */
    private String convertTradeStatusToOrderStatus(String tradeStatus) {
        if (StringUtils.isEmpty(tradeStatus)) {
            return null;
        }
        // 根据buildTradeStatusList方法的逆向逻辑进行转换
        if (TradeStatusEnum.CONFIRM.getCode().equals(tradeStatus) ||
                TradeStatusEnum.PAYING.getCode().equals(tradeStatus) ||
                TradeStatusEnum.PAY_SUCCESS.getCode().equals(tradeStatus) ||
                TradeStatusEnum.SM_PART_SUC_CONFIRM.getCode().equals(tradeStatus) ||
                TradeStatusEnum.WAIT_CONFIRM.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.APP_SUCCESS.getCode();
        } else if (TradeStatusEnum.CANCEL.getCode().equals(tradeStatus) ||
                TradeStatusEnum.CANCELING.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.SELF_CANCELED.getCode();
        } else if (TradeStatusEnum.FORCE_CANCEL.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.FORCE_CANCELED.getCode();
        } else if (TradeStatusEnum.PART_SUCCESS.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.PART_ACK_SUCCESS.getCode();
        } else if (TradeStatusEnum.TRADE_SUCCESS.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.ACK_SUCCESS.getCode();
        } else if (TradeStatusEnum.TRADE_FAIL.getCode().equals(tradeStatus)) {
            return OrderStatusEnum.ACK_FAIL.getCode();
        } else {
            return tradeStatus;
        }
    }

    /**
     * 将ES交易状态转换为付款状态
     */
    private String convertTradeStatusToPayStatus(String tradeStatus) {
        if (StringUtils.isEmpty(tradeStatus)) {
            return null;
        }

        // 根据交易状态判断付款状态
        switch (tradeStatus) {
            case "PAYING":
                return "1"; // 付款中
            case "PAY_SUCCESS":
            case "CONFIRM":
            case "WAIT_CONFIRM":
            case "SM_PART_SUC_CONFIRM":
            case "PART_SUCCESS":
            case "TRADE_SUCCESS":
                return "2"; // 付款成功
            case "CANCEL":
            case "CANCELING":
            case "FORCE_CANCEL":
            case "TRADE_FAIL":
                return "3"; // 付款失败
            default:
                return "0"; // 未付款
        }
    }
}
