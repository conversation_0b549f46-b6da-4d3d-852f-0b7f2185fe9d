/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.business.sequence;

import com.howbuy.tms.common.sequence.KeyGenerator;
import com.howbuy.tms.common.sequence.KeyGeneratorOptimized;
import com.howbuy.tms.common.sequence.TableBusiCode;
import org.springframework.stereotype.Service;

/**
 * Description:获取各种订单号或sequence
 *
 * @reason:
 * <AUTHOR>
 * @date 2016年9月20日 下午5:12:51
 * @since JDK 1.7
 */
@Service("sequenceService")
public class SequenceService {

    /**
     * getProtocolNo:获取客户投资协议号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * <AUTHOR>
     * @date 2016-9-20 上午11:44:37
     */
    public String getProtocolNo(String txAcctNo) {
        return KeyGeneratorOptimized.generateRecommended(TableBusiCode.CUST_PROTOCOL, txAcctNo);
    }

    /**
     *
     * getDealNo:获取交易订单号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:08:17
     */
    public String getDealNo(String txAcctNo) {
        return KeyGeneratorOptimized.generateRecommended(TableBusiCode.DEAL_ORDER, txAcctNo);
    }

    /**
     *
     * getDealDtlNo:获取交易订单明细好
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:08:39
     */
    public String getDealDtlNo(String txAcctNo) {
        return KeyGeneratorOptimized.generateRecommended(TableBusiCode.FUND_DEAL_ORDER_DTL, txAcctNo);
    }

    /**
     *
     * getPmtDealNo:获取支付订单明细号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:08:54
     */
    public String getPmtDealNo(String txAcctNo) {
        return KeyGeneratorOptimized.generateRecommended(TableBusiCode.PAYMENT_ORDER_DTL, txAcctNo);
    }

    /**
     * 获取表单单号
     * @param txAcctNo 交易账号
     * @return 表单单号
     */
    public String getFormNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.FORM_NO, txAcctNo);
    }

    /**
     *
     * getCustBooksDtlNo:获取份额变动明细号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:09:10
     */
    public String getCustBooksDtlNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.CUST_BOOKS_DTL, txAcctNo);
    }

    /**
     *
     * getDealNo:获取子账本变动明细记录号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:08:17
     */
    public String getSubCustBooksDtlNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.SUB_CUST_BOOKS_DTL, txAcctNo);
    }

    /**
     *
     * getCustBooksNo:获取份额账本号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * @return String
     * <AUTHOR>
     * @date 2016年9月14日 下午6:09:29
     */
    public String getCustBooksNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.CUST_BOOKS, txAcctNo);
    }

    /**
     *
     * getSubmitDealNo:获取上报订单号
     *
     * @param txAcctNo
     *            交易账号
     * @return
     * <AUTHOR>
     * @date 2016年9月19日 下午7:11:26
     */
    public String getSubmitDealNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.FUND_CHECK_ORDER, txAcctNo);
    }

    /**
     *
     * getConditionalOrderExecRecNo:获取条件单执行记录号
     *
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2016年9月28日 下午8:45:34
     */
    public String getConditionalOrderExecRecNo(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.CONDITIONAL_ORDER_EXEC_REC, txAcctNo);
    }

    /**
     *
     * getSignatureSid:获取电子签名订单流水号
     *
     * @param txAcctNo
     * @return
     * @return String
     * <AUTHOR>
     * @date 2017年4月13日 下午7:09:15
     */
    public String getSignatureSid(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.OTHER, txAcctNo);
    }

    /**
     *
     * getCounterOperaLogId:(获取客户高端复购协议号)
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年4月15日 上午11:48:25
     */
    public String getHighCustRepurchaseProtocol(String txAcctNo) {
        return KeyGenerator.generate(TableBusiCode.HIGH_CUST_REPURCHASE_PROTOCOL, txAcctNo);
    }
}
