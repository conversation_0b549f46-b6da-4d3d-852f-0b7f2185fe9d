### 2.卖出非香港待确认交易记录(新增模块)

- 请求地址

| 类名                                                         | 方法名                          | 描述                     |
| :----------------------------------------------------------- | :------------------------------ | :----------------------- |
| com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService | querySellUnConfirmUnHkOrderList | 卖出待确认非香港订单记录 |

- 入参 

| 字段        | 字段注释 | 类型         | 是否必填 | 备注 |
| :---------- | :------- | :----------- | :------- | :--- |
| txAcctNo    | 用户号   | String       | 是       |      |
| hboneNo     | 一账通   | String       | 是       |      |
| fundCode    | 基金代码 | String       | 否       |      |
| disCodeList | 分销渠道 | List<String> | 是       |      |

- 出参

| 字段             | 字段注释                                                 | 类型       | 备注 |
| :--------------- | :------------------------------------------------------- | :--------- | :--- |
| dealNo           | 订单号                                                   | String     | ``   |
| fundCode         | 产品编码                                                 | String     |      |
| appVol           | 申请份额                                                 | BigDecimal |      |
| mBusiCode        | 中台业务类型                                             | String     |      |
| notifySubmitFlag | 上报状态*0-无需上报，1-未上报，2-上报完成，3-需重新上报* | String     |      |



- 业务逻辑

  - 根据disCodeList判断查询的数据范围查询直销,代销的非香港交易记录,实现:

    - highDealOrderDtlRepository.queryUnConfirmUnHkSellOrderList(txAcctNo,hboneNo,fundCode,disCodeList)返回实体字段 dealNo 订单号;mergeSubmitFlag 是否合并上报,1:是,0:不是;mainDealOrderNo 主订单号;dealType:订单交易类型,1:代销,0:直销;fundCode 基金代码;appVol:申请份额;m_busiCode:业务类型;notifySubmitFlag:上报状态,*0-无需上报，1-未上报，2-上报完成，3-需重新上报*

    - sql如下

      ```mysql
       select d.deal_no,
          d.merge_submit_flag,
          d.main_deal_order_no,
          '1' as deal_type,
          d.fund_code ,
          d.app_vol,
          d.m_busi_code ,
          d.notify_submit_flag
          from high_deal_order_dtl d
          where d.m_busi_code = '1124'
          and d.tx_app_flag = '0'
          <if test = "fundCode != null and fundCode != ''">
              and d.fund_code = #{fundCode,jdbcType = VARCHAR}
          </if>
          <if test="disCodeList != null and disCodeList.size() > 0 ">
              and d.DIS_CODE in
              <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                  #{disCode}
              </foreach>
          </if>
          and (d.tx_ack_flag is null or d.tx_ack_flag in ('1', '2'))
          and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          union all
      select  c.appserialno as deal_no,
          '0' as merge_submit_flag,
          '' as main_deal_order_no,
          '0' as deal_type,
           ifnull(c.mjjdm,c.fundcode) as fund_code,
           c.appvol ,
           CONCAT('1', c.busicode) AS m_busi_code,
           '1' as notify_submit_flag
      from cm_custtrade_direct c
      where c.busicode in ('124','136','19B')
        and c.RECSTAT='0'
        and c.prebookstate = '2'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
              and c.DISCODE in
              <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                  #{disCode}
              </foreach>
       </if>
       <if test = "fundCode != null and fundCode != ''">
              and c.fundcode= #{fundCode,jdbcType = VARCHAR}
       </if>
        and c.IS_HK_PRODUCT !='1'
        and c.orderstate = '1'
        and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        and c.tradedt <![CDATA[ >= ]]> '20221101'
      ```

      

- 遍历代销部分交易记录,找出 mergeSubmitFlag ='1' && mainDealOrderNo is not null 的,然后按照将这部分交易记录,按照mainDealOrderNo 分组,每组的申请份额汇总求和 ,每组合并为一条新的交易记录,替换为分组前的这部分交易记录,dealNo=mainDealOrderNo ,appVol=每组的净申请金额汇总求和, fundCode 取dealNo=mainDealOrderNo那条记录的fundCode,notifySubmitFlag取dealNo=mainDealOrderNo那条记录的notifySubmitFlag,mBusiCode取dealNo=mainDealOrderNo那条记录的mBusiCode
- 返回将交易记录,转为符合接口定义返回结果字段的交易记录