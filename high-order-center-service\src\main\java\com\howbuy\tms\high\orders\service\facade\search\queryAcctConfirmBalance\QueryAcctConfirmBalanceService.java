package com.howbuy.tms.high.orders.service.facade.search.queryAcctConfirmBalance;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance.AcctBalanceBaseInfoDto;
import com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance.QueryAcctConfirmBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance.QueryAcctConfirmBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance.QueryAcctConfirmBalanceResponse;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description:查询用户确认持仓
 * @Author: yun.lu
 * Date: 2023/12/18 15:13
 */
@DubboService
@Service("queryAcctConfirmBalanceFacade")
@Slf4j
public class QueryAcctConfirmBalanceService implements QueryAcctConfirmBalanceFacade {
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctconfirmbalance.QueryAcctConfirmBalanceFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctConfirmBalanceService
     * @apiName execute
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=ZOJK&pageSize=3346&disCode=7R6wFmkZJL&txChannel=j&appTm=WiHUi2UytG&fundCode=tR9N&subOutletCode=azVJvRDx&pageNo=9622&operIp=t&txAcctNo=BU&appDt=uX&dataTrack=fy3Kz&txCode=JQcqgwIxk8&outletCode=5
     * @apiSuccess (响应结果) {Array} acctBalanceBaseInfoDtoList
     * @apiSuccess (响应结果) {String} acctBalanceBaseInfoDtoList.txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} acctBalanceBaseInfoDtoList.hbOneNo 一账通账号
     * @apiSuccess (响应结果) {String} acctBalanceBaseInfoDtoList.fundCode 产品编码
     * @apiSuccess (响应结果) {Number} acctBalanceBaseInfoDtoList.balanceVol 持仓份额
     * @apiSuccess (响应结果) {String} acctBalanceBaseInfoDtoList.isHkProduct 是否是香港产品
     * @apiSuccess (响应结果) {String} acctBalanceBaseInfoDtoList.disCode 分销渠道
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"jFzW44x","totalPage":8072,"pageNo":504,"description":"04cQ03pBT","acctBalanceBaseInfoDtoList":[{"hbOneNo":"SkF","fundCode":"hqu","txAcctNo":"FeTjU","balanceVol":1970.7777849617125,"disCode":"ZYLZ6fThF","isHkProduct":"4lyKwNWA"}],"totalCount":5301}
     */
    @Override
    public QueryAcctConfirmBalanceResponse execute(QueryAcctConfirmBalanceRequest request) {
        QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
        param.setTxAcctNo(request.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(request.getFundCode()));
        param.setDisCodeList(Collections.singletonList(request.getDisCode()));
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(param);
        List<AcctBalanceBaseInfoDto> acctBalanceBaseInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList)) {
            for (AcctBalanceBaseInfo acctBalanceBaseInfo : confirmBalanceBaseInfoList) {
                AcctBalanceBaseInfoDto acctBalanceBaseInfoDto = new AcctBalanceBaseInfoDto();
                BeanUtils.copyProperties(acctBalanceBaseInfo, acctBalanceBaseInfoDto);
                acctBalanceBaseInfoDtoList.add(acctBalanceBaseInfoDto);
            }
        }
        QueryAcctConfirmBalanceResponse response = new QueryAcctConfirmBalanceResponse();
        response.setAcctBalanceBaseInfoDtoList(acctBalanceBaseInfoDtoList);
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }
}
