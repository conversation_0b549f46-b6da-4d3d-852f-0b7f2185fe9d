/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfundliquidationlist;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.asset.dubbo.FundLiquidationOuterService;
import com.howbuy.tms.common.outerservice.asset.request.QueryFundLiquidationListRequestDTO;
import com.howbuy.tms.common.outerservice.asset.response.QueryFundLiquidationListResponseDTO;
import com.howbuy.tms.common.outerservice.dtms.QueryCustFundClearanceOuterService;
import com.howbuy.tms.common.outerservice.dtms.request.QueryCustFundClearanceRequestDTO;
import com.howbuy.tms.common.outerservice.dtms.response.QueryCustFundClearanceResponseDTO;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidationlist.QueryFundLiquidationListFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidationlist.QueryFundLiquidationListRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidationlist.QueryFundLiquidationListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 清仓产品列表查询服务实现
 * <AUTHOR>
 * @date 2025/9/4 20:15
 * @since JDK 1.8
 */
@DubboService
@Service("queryFundLiquidationListFacade")
@Slf4j
public class QueryFundLiquidationListFacadeService implements QueryFundLiquidationListFacade {

    @Resource
    private FundLiquidationOuterService fundLiquidationOuterService;

    @Resource
    private QueryHighProductOuterService queryHighProductOuterService;

    @Resource
    private QueryCustFundClearanceOuterService queryCustFundClearanceOuterService;

    /**
     * 渠道编码常量
     */
    private static final String CHANNEL_HOWBUY = "1";      // 好买分销
    private static final String CHANNEL_HAOZHEN = "2";     // 好臻分销
    private static final String CHANNEL_OVERSEAS = "3";    // 好买香港分销

    @Override
    public QueryFundLiquidationListResponse execute(QueryFundLiquidationListRequest request) {
        log.info("查询清仓产品列表开始, request: {}", request);
        
        QueryFundLiquidationListResponse response = new QueryFundLiquidationListResponse();
        
        try {
            // 参数校验
            if (StringUtils.isBlank(request.getHbOneNo())) {
                throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY,"参数错误,一账通号不能为空");
            }
            
            if (CollectionUtils.isEmpty(request.getChannelCodeList())) {
                throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY,"参数错误,分销渠道编码列表不能为空");
            }

            // 获取不同渠道的清仓产品基金代码
            Set<String> collect = new HashSet<>(request.getChannelCodeList());
            List<String> fundCodeList = getClearanceFundCodes(collect, request.getHbOneNo());
            
            if (CollectionUtils.isEmpty(fundCodeList)) {
                log.info("未找到清仓产品基金代码, hbOneNo: {}, channelCodeList: {}", 
                    request.getHbOneNo(), request.getChannelCodeList());
                response.setFundLiquidationList(new ArrayList<>());
                return response;
            }

            // 调用资产中心接口查询清仓产品信息
            QueryFundLiquidationListRequestDTO assetRequest = new QueryFundLiquidationListRequestDTO();
            assetRequest.setHboneNo(request.getHbOneNo());
            assetRequest.setFundCodes(fundCodeList);
            assetRequest.setOnlyClearFlag("Y"); // 仅查询清仓产品

            QueryFundLiquidationListResponseDTO assetResponse = fundLiquidationOuterService.queryFundLiquidationList(assetRequest);
            
            if (assetResponse == null || CollectionUtils.isEmpty(assetResponse.getProductList())) {
                log.info("资产中心返回清仓产品列表为空, hbOneNo: {}", request.getHbOneNo());
                response.setFundLiquidationList(new ArrayList<>());
                return response;
            }

            // 批量查询基金信息
            List<String> responseFundCodes = assetResponse.getProductList().stream()
                    .map(QueryFundLiquidationListResponseDTO.LiquidationProduct::getFundCode)
                    .collect(Collectors.toList());

            Map<String, HighProductDBInfoBean> highProductDBInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(responseFundCodes);

            // 封装返回结果
            List<QueryFundLiquidationListResponse.FundLiquidationInfo> resultList = buildFundLiquidationList(
                    assetResponse.getProductList(), highProductDBInfoMap);

            response.setFundLiquidationList(resultList);

            log.info("查询清仓产品列表成功, hbOneNo: {}, 返回数量: {}", request.getHbOneNo(), resultList.size());

        } catch (Exception e) {
            log.error("查询清仓产品列表异常, request: {}", request, e);
        }

        return response;
    }

    /**
     * 获取不同渠道的清仓产品基金代码
     */
    private List<String> getClearanceFundCodes(Set<String> channelCodeList, String hbOneNo) {
        List<String> allFundCodes = new ArrayList<>();
        
        for (String channelCode : channelCodeList) {
            List<String> channelFundCodes = getClearanceFundCodesByChannel(channelCode, hbOneNo);
            if (CollectionUtils.isNotEmpty(channelFundCodes)) {
                allFundCodes.addAll(channelFundCodes);
            }
        }
        
        // 去重
        return allFundCodes.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据渠道获取清仓产品基金代码
     */
    private List<String> getClearanceFundCodesByChannel(String channelCode, String hbOneNo) {
        switch (channelCode) {
            case CHANNEL_HOWBUY:
                // 好买分销渠道 - TODO: 待实现
                log.info("好买分销渠道清仓产品查询待实现, channelCode: {}, hbOneNo: {}", channelCode, hbOneNo);
                return new ArrayList<>();

            case CHANNEL_HAOZHEN:
                // 好臻分销渠道 - TODO: 待实现
                log.info("好臻分销渠道清仓产品查询待实现, channelCode: {}, hbOneNo: {}", channelCode, hbOneNo);
                return new ArrayList<>();

            case CHANNEL_OVERSEAS:
                // 海外分销渠道：查询海外的用户清仓基金编码接口
                try {
                    QueryCustFundClearanceRequestDTO queryCustFundClearanceRequestDTO = new QueryCustFundClearanceRequestDTO();
                    queryCustFundClearanceRequestDTO.setHboneNo(hbOneNo);
                    QueryCustFundClearanceResponseDTO queryCustFundClearanceResponseDTO = queryCustFundClearanceOuterService.queryCustFundClearance(queryCustFundClearanceRequestDTO);
                    return queryCustFundClearanceResponseDTO.getClearanceFundList();
                } catch (Exception e) {
                    log.error("查询海外清仓基金编码异常, hbOneNo: {}", hbOneNo, e);
                    return new ArrayList<>();
                }

            default:
                log.warn("不支持的渠道编码: {}", channelCode);
                return new ArrayList<>();
        }
    }

    /**
     * 构建清仓产品列表
     */
    private List<QueryFundLiquidationListResponse.FundLiquidationInfo> buildFundLiquidationList(
            List<QueryFundLiquidationListResponseDTO.LiquidationProduct> productList,
            Map<String, HighProductDBInfoBean> productInfoMap) {
        
        List<QueryFundLiquidationListResponse.FundLiquidationInfo> resultList = new ArrayList<>();
        
        for (QueryFundLiquidationListResponseDTO.LiquidationProduct product : productList) {
            QueryFundLiquidationListResponse.FundLiquidationInfo info = new QueryFundLiquidationListResponse.FundLiquidationInfo();
            
            // 基金代码
            info.setFundCode(product.getFundCode());
            
            // 基金名称
            HighProductDBInfoBean productInfo = productInfoMap.get(product.getFundCode());
            if (productInfo != null) {
                info.setFundName(productInfo.getFundAttr());
            } else {
                info.setFundName(product.getFundCode()); // 如果没有找到基金信息，使用基金代码
            }
            
            // 产品收益：净值型产品展示累计收益，非净值型产品展示累计回款
            BigDecimal assetValue = getProductAssetValue(product, productInfo);
            info.setProductAsset(formatAmount(assetValue));
            
            // 累计持有天数
            info.setTotalDays(product.getTotalHoldDays() != null ? product.getTotalHoldDays().toString() : "0");
            
            // 单位默认是元
            info.setUnit("元");
            
            resultList.add(info);
        }
        
        return resultList;
    }

    /**
     * 获取产品收益值
     * 净值型产品展示累计收益，非净值型产品展示累计回款
     */
    private BigDecimal getProductAssetValue(QueryFundLiquidationListResponseDTO.LiquidationProduct product,
                                            HighProductDBInfoBean productInfo) {
        // 判断是否为净值型产品
        boolean isNetValueProduct = isNetValueProduct(productInfo);
        
        if (isNetValueProduct) {
            // 净值型产品展示累计收益
            return product.getAccumIncomeExFee() != null ? product.getAccumIncomeExFee() : BigDecimal.ZERO;
        } else {
            // 非净值型产品展示累计回款
            return product.getAccumCollection() != null ? product.getAccumCollection() : BigDecimal.ZERO;
        }
    }

    /**
     * 判断是否为净值型产品
     */
    private boolean isNetValueProduct(HighProductDBInfoBean productInfo) {
        // TODO: 根据产品信息判断是否为净值型产品，这里需要根据实际业务逻辑实现
        // 暂时默认返回true，表示净值型产品
        return true;
    }

    /**
     * 格式化金额：千分位格式展示，四舍五入保留两位小数
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        
        // 四舍五入保留两位小数
        BigDecimal roundedAmount = amount.setScale(2, RoundingMode.HALF_UP);
        
        // 千分位格式化
        DecimalFormat formatter = new DecimalFormat("#,##0.00");
        return formatter.format(roundedAmount);
    }
}
