package com.howbuy.tms.high.orders.service.business.busiprocess.common;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.base.PreBookTradeTypeEnum;
import com.howbuy.crm.prebook.request.PreBookFullInfoQueryRequest;
import com.howbuy.crm.prebook.response.PreBookDetailVO;
import com.howbuy.interlayer.product.enums.ProductBuyUserTypeEnum;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.cache.service.highquota.HighQuotaBean;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.GetFeeRateMethodEnum;
import com.howbuy.tms.common.enums.busi.PreBookStateEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.auth.encryptsingle.EncryptSingleOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.SendEmailWithAttachmentOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendemailwithattachment.bean.SendEmailWithAttachmentBean;
import com.howbuy.tms.common.outerservice.crm.td.queryprebooklist.QueryPreBookListOuterService;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.HzFundAmtLockCfgService;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.bean.HzFundAmtLockCfgDto;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLimitBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLockInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.ExcelUtiles;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.DealOrderExtendPo;
import com.howbuy.tms.high.orders.dao.po.DealOrderPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.po.HighOrderAppointinfoPo;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.BaseMergeSubsOrPurRequest;
import com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus.HzFundBuyStatusLogicService;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.business.ordercreater.PaidAmtChangeInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryHzSubscribeAmtInfo.QuerySubscribeAmtParam;
import com.howbuy.tms.high.orders.service.facade.search.queryHzSubscribeAmtInfo.SubscribeAmtInfoDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.CustomerInfoCommand;
import com.howbuy.tms.high.orders.service.facade.trade.cancelorder.hzcanceloriginalorder.CancelOriginalOrderRequest;
import com.howbuy.tms.high.orders.service.facade.trade.cancelorder.hzcanceloriginalorder.HzCancelOriginalOrderService;
import com.howbuy.tms.high.orders.service.repository.CustomerSubmitFormRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HighOrderAppointinfoRepository;
import com.howbuy.tms.high.orders.service.repository.SubscribeAmtDetailRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:好臻下单校验逻辑处理逻辑
 * @Author: yun.lu
 * Date: 2023/12/12 16:56
 */
@Slf4j
@Service
public class HzCreateOrderLogicProcess extends AbstractDisCodeCreateOrderLogicProcess {
    @Autowired
    private HzFundBuyStatusLogicService hzFundBuyStatusLogicService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private HzFundAmtLockCfgService hzFundAmtLockCfgService;
    @Autowired
    private SubscribeAmtDetailRepository subscribeAmtDetailRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private HzCancelOriginalOrderService hzCancelOriginalOrderService;
    @Autowired
    private EncryptSingleOuterService encryptSingleOuterService;
    @Autowired
    private SendEmailWithAttachmentOuterService sendEmailWithAttachmentOuterService;
    @Autowired
    private QueryPreBookListOuterService queryPreBookListOuterService;
    @Resource
    private HighProductService highProductService;
    @Autowired
    private HighOrderAppointinfoRepository highOrderAppointinfoRepository;
    /**
     * 实缴金额变更通知邮件
     */
    @Value("${paidAmt.change.notify.email}")
    private String paidAmtChangeNotifyEmail;


    protected void validateAppointDealNo(String appointmentDealNo, OrderCreateContext context) {
        List<String> dealNoList = highOrderAppointinfoRepository.selectByDealNoByDealNo(appointmentDealNo);
        if (CollectionUtils.isEmpty(dealNoList)) {
            return;
        }

        // 好臻在途的订单只会时一笔
        HighDealOrderDtlPo highDealOrderDtlPo = context.getOnWayHighDealOrderDtlPoList().get(0);
        if (dealNoList.contains(highDealOrderDtlPo.getDealNo())) {
            logger.info("修改订单的场景,同一个预约单,可以允许预约单重复");
        } else {
            throw new ValidateException(ExceptionCodes.HIGH_APPOINTMENTDEALNO_HAS_USED,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_APPOINTMENTDEALNO_HAS_USED));
        }
    }

    @Override
    public void createOrderPreCheck(BaseMergeSubsOrPurRequest request, OrderCreateContext context,
                                    String forceSubmitFlag, AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess) {
        // 1.印章id必填
        checkHzSpecialField(request, context);
        // 2.公共校验
        super.createOrderPreCheck(request, context, forceSubmitFlag, abstractSubsOrPurLogicProcess);
        // 3.校验代销关系,可购买用户类型
        validateChannelAndBranchCode(context.getHighProductInfoBean(),request.getTxChannel(),context.getCustInfo().getCustInfo());
        // 4.在途订单校验
        validateOnWayOrder(request, context);
        // 5.持仓校验
        validateConfirmBalance(request, context);
        // 6.首单/追加判断
        firstBuyFlagProcess(request, context);
        // 7.年龄校验
        validAgeLimit(request, context);
        // 8.人数/金额额度校验
        validatePeopleCountAndAmt(request, context);
        // 9.手续费校验
        validateFee(request, context, abstractSubsOrPurLogicProcess);
        // 10.验证预约订单号是否已使用
        validateAppointDealNo(request.getAppointmentDealNo(), context);
        // 11.线上下单,好臻首次,formNo必填
        List<String> needList = new ArrayList<>();
        needList.add(TxChannelEnum.WAP.getCode());
        needList.add(TxChannelEnum.APP.getCode());
        if (needList.contains(request.getTxChannel())) {
            if (FirstBuyFlagEnum.YES.getCode().equals(context.getBuyBean().getFirstBuyFlag()) && StringUtils.isBlank(context.getFormNo())) {
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "好臻合格投资者信息确认单No没有");
            }
        }
    }

    /**
     * 校验代销关系,可购买用户类型
     */
    private void validateChannelAndBranchCode(HighProductInfoBean highProductInfoBean, String txChannel, CustInfoBean custInfo){
        // 代销关系校验
        String branchCode = highProductInfoBean.getBranchCode();
        if (TxChannelEnum.COUNTER.getCode().equals(txChannel)
                && (StringUtils.isEmpty(branchCode) || BranchCodeEnum.UN_COUNTER.getCode().equals(branchCode))) {
            throw new ValidateException(ExceptionCodes.HIGH_CONSIGNMENT_NOT_SUPPORT, MessageSource.getMessageByCode(ExceptionCodes.HIGH_CONSIGNMENT_NOT_SUPPORT));
        }
        if (!TxChannelEnum.COUNTER.getCode().equals(txChannel)
                && (StringUtils.isEmpty(branchCode) || BranchCodeEnum.COUNTER.getCode().equals(branchCode))) {
            throw new ValidateException(ExceptionCodes.HIGH_CONSIGNMENT_NOT_SUPPORT, MessageSource.getMessageByCode(ExceptionCodes.HIGH_CONSIGNMENT_NOT_SUPPORT));
        }

        // 可购买客户类型校验
        String buyUserType = highProductInfoBean.getBuyUserType();
        if (!StringUtils.isBlank(buyUserType)) {
            String invstType = custInfo.getInvstType();
            String userType = ProductBuyUserTypeEnum.getByInvstType(invstType);
            if (!buyUserType.contains(ProductBuyUserTypeEnum.ALL.getCode())
                    && (userType==null || !buyUserType.contains(userType))) {
                throw new ValidateException(ExceptionCodes.HIGH_ORDER_INVST_TYPE_NOT_SUPPORT, MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_INVST_TYPE_NOT_SUPPORT));
            }
        }
    }



    @Override
    public void firstBuyFlagProcess(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 好臻的,分次call,如果没有确认持仓就是首单
        String firstBuyFlag = CollectionUtils.isEmpty(context.getConfirmBalanceBaseInfoList()) ? FirstBuyFlagEnum.YES.getCode() : FirstBuyFlagEnum.NO.getCode();
        context.getBuyBean().setFirstBuyFlag(firstBuyFlag);
        for (OrderCreateContext.BuyBean buyBean : context.getBuyList()) {
            buyBean.setFirstBuyFlag(firstBuyFlag);
        }
    }

    /**
     * 好臻特殊字段校验
     *
     * @param request 请求入参
     */
    private void checkHzSpecialField(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        List<Object> notNeedValidateTxChannel = new ArrayList<>();
        notNeedValidateTxChannel.add(TxChannelEnum.COUNTER.getCode());
        notNeedValidateTxChannel.add(TxChannelEnum.INST.getCode());
        if (StringUtils.isEmpty(request.getSealId()) && !notNeedValidateTxChannel.contains(request.getTxChannel())) {
            logger.info("HzCreateOrderCheckLogicProcess-processBusiness,印章id不能为空");
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "印章id不能为空");
        }
        context.setSealId(request.getSealId());
        // 如果是好臻分次call,认缴金额必须有
        if (YesOrNoEnum.YES.getCode().equals(context.getHighProductInfoBean().getPeDivideCallFlag())) {
            if (request.getSubsAmt() == null) {
                logger.info("HzCreateOrderCheckLogicProcess-processBusiness,好臻分次call,认缴金额不能为空");
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "好臻分次call,认缴金额不能为空");
            }
        }
    }

    @Override
    protected BigDecimal getBuildFeeBaseAmt(AbstractSubsOrPurLogicProcess abstractSubsOrPurLogicProcess, BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 按照基金费率取值方式,实缴,就返回实缴金额作废费率计算基础金额,否则就返回认缴金额,但是查询的时候只用认缴的去查
        HighProductFeeRateBean highProductFeeRateBean = getFeeRateInfo(context.getHighProductInfoBean().getFundCode(), context.getFeeRateBusinessCode().getCode(), context.getCustInfo().getCustInfo().getInvstType(), context.getHighProductInfoBean().getShareClass(), request.getSubsAmt());
        if (highProductFeeRateBean == null) {
            logger.info("getBuildFeeBaseAmt-根据产品信息查不到费率配置,fundCode={},busiCode={},invstType={},shareClass={},subsAmt={}", context.getHighProductInfoBean().getFundCode(),
                    context.getFeeRateBusinessCode().getCode(), context.getCustInfo().getCustInfo().getInvstType(), context.getHighProductInfoBean().getShareClass(), request.getSubsAmt());
            return null;
        }
        context.setHighProductFeeRateBean(highProductFeeRateBean);
        context.setGetFeeRateMethod(highProductFeeRateBean.getGetFeeRateMethod());
        context.setConstantFee(highProductFeeRateBean.getConstantFee());
        if (GetFeeRateMethodEnum.PAID.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod())) {
            return abstractSubsOrPurLogicProcess.calNetAppAmt(request.getAppAmt(), request.getEsitmateFee(), context.getHighProductInfoBean().getFeeCalMode());
        }
        return request.getSubsAmt();
    }

    @Override
    protected BigDecimal getTotalAppointAmt(OrderCreateContext context) {
        BigDecimal appointmentAmt = null;
        if (GetFeeRateMethodEnum.PAID.getCode().equals(context.getGetFeeRateMethod())) {
            for (OrderCreateContext.BuyBean bean : context.getBuyList()) {
                BigDecimal appointAmt = getAppointAmt(context, bean);
                if (appointAmt != null) {
                    if (appointmentAmt == null) {
                        appointmentAmt = appointAmt;
                    } else {
                        appointmentAmt = appointmentAmt.add(appointAmt);
                    }
                }
            }
        } else {
            OrderCreateContext.BuyBean buyBean = context.getBuyList().get(0);
            HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();
            // 非分次call就用预约金额
            if (YesOrNoEnum.NO.getCode().equals(highProductInfoBean.getPeDivideCallFlag())) {
                return buyBean.getAppointmentAmt();
            } else {
                // 分次call,按照认缴/实缴
                return getAppointAmt(context, buyBean);
            }

        }

        return appointmentAmt;
    }

    private BigDecimal getAppointAmt(OrderCreateContext context, OrderCreateContext.BuyBean bean) {
        if (GetFeeRateMethodEnum.PAID.getCode().equals(context.getGetFeeRateMethod())) {
            return bean.getAppointmentAmt();
        }
        return bean.getAppointSubsAmt();
    }


    @Override
    public List<OrderCreateBean> createMergeOrders(OrderCreateContext context) {
        // 由于好臻是没有合并支付的场景,所以好臻的合并支付就是单笔
        OrderCreateBean order = createOrder(context);
        return Collections.singletonList(order);
    }

    @Override
    public void createDealOrderExtend(OrderCreateContext context, OrderCreateBean bean) {
        OrderCreateContext.OrderExtendBean orderExtendBean = context.getOrderExtendBean();
        if (orderExtendBean == null) {
            return;
        }

        DealOrderPo DealOrderPo = bean.getOrderPo();
        DealOrderExtendPo extendPo = new DealOrderExtendPo();
        extendPo.setDealNo(DealOrderPo.getDealNo());
        extendPo.setRiskAckDtm(orderExtendBean.getRiskAckDtm());
        extendPo.setTransactorName(orderExtendBean.getTransactorName());
        extendPo.setTransactorIdNo(orderExtendBean.getTransactorIdNo());
        extendPo.setTransactorIdType(orderExtendBean.getTransactorIdType());
        extendPo.setOperatorNo(orderExtendBean.getOperatorNo());
        extendPo.setConsCode(orderExtendBean.getConsCode());
        extendPo.setCardAddress(orderExtendBean.getCardAddress());
        extendPo.setLiveAddress(orderExtendBean.getLiveAddress());
        extendPo.setAppointmentDealNo(orderExtendBean.getAppointmentDealNo());
        extendPo.setNormalCustTipDtm(orderExtendBean.getNormalCustTipDtm());
        extendPo.setHighRiskTipDtm(orderExtendBean.getHighRiskTipDtm());

        extendPo.setRiskToleranceDate(orderExtendBean.getRiskToleranceDate());
        extendPo.setInvestorQualifiedDate(orderExtendBean.getInvestorQualifiedDate());
        extendPo.setRiskHintConfirmDtm(orderExtendBean.getRiskHintConfirmDtm());
        extendPo.setInvestorQualifiedHintConfirmDtm(orderExtendBean.getInvestorQualifiedHintConfirmDtm());

        extendPo.setPortfolioFlag(PortfolioFlagEnum.NOT_PORTFOLIO.getCode());
        extendPo.setCreateDtm(context.getNow());
        extendPo.setUpdateDtm(context.getNow());
        extendPo.setRecStat(RecStatEnum.EFFECTIVE.getCode());
        extendPo.setInvestAckDtm(orderExtendBean.getInvestAckDtm());
        List<Object> notNeedValidateTxChannel = new ArrayList<>();
        notNeedValidateTxChannel.add(TxChannelEnum.COUNTER.getCode());
        notNeedValidateTxChannel.add(TxChannelEnum.INST.getCode());
        if (!notNeedValidateTxChannel.contains(context.getOrderTradeBaseRequest().getTxChannel())) {
            extendPo.setCompliancePrompt(YesOrNoEnum.YES.getCode());
        }
        bean.setDealOrderExtendPo(extendPo);
    }

    /**
     * 取消原订单
     *
     * @param context 下单上下文
     */
    private void cancelOriginalOrder(OrderCreateContext context, OrderCreateBean order) {
        // 1.好臻在途的订单只会时一笔
        HighDealOrderDtlPo highDealOrderDtlPo = context.getOnWayHighDealOrderDtlPoList().get(0);
        DealOrderPo oldDealOrder = dealOrderRepository.selectByDealNo(highDealOrderDtlPo.getDealNo());
        // 2.取消原订单
        CancelOriginalOrderRequest cancelOriginalOrderRequest = new CancelOriginalOrderRequest();
        cancelOriginalOrderRequest.setDealNo(highDealOrderDtlPo.getDealNo());
        cancelOriginalOrderRequest.setNewDealNo(order.getOrderPo().getDealNo());
        cancelOriginalOrderRequest.setTxAcctNo(highDealOrderDtlPo.getTxAcctNo());
        cancelOriginalOrderRequest.setExternalDealNo(oldDealOrder.getExternalDealNo());
        cancelOriginalOrderRequest.setForceCancelFlag(YesOrNoEnum.NO.getCode());
        cancelOriginalOrderRequest.setDisCode(DisCodeEnum.HZ.getCode());
        hzCancelOriginalOrderService.cancelOrder(cancelOriginalOrderRequest);
    }

    /**
     * 下单
     *
     * @param context 生成订单上下文
     * @return 创建订单实体
     */
    @Override
    public OrderCreateBean createOrder(OrderCreateContext context) {
        // 0.如果是修改订单,原订单预约单需要赋值给新订单
        if (CollectionUtils.isNotEmpty(context.getOnWayHighDealOrderDtlPoList())) {
            HighOrderAppointinfoPo highOrderAppointinfoPo = highOrderAppointinfoRepository.selectByDealNo(context.getOnWayHighDealOrderDtlPoList().get(0).getDealNo());
            OrderCreateContext.HighOrderAppointInfoBean highOrderAppointInfoBean = context.getHighOrderAppointInfoBean();
            if (highOrderAppointinfoPo != null) {
                if (highOrderAppointInfoBean != null) {
                    highOrderAppointInfoBean.setAppointmentDealNo(highOrderAppointinfoPo.getAppointmentDealNo());
                } else {
                    highOrderAppointInfoBean = new OrderCreateContext.HighOrderAppointInfoBean();
                    highOrderAppointInfoBean.setAppointmentDealNo(highOrderAppointinfoPo.getAppointmentDealNo());
                    context.setHighOrderAppointInfoBean(highOrderAppointInfoBean);
                }
            }
        }
        // 1.构建数据持久化bean
        OrderCreateBean order = super.createOrder(context);
        // 3.如果有在途
        if (CollectionUtils.isNotEmpty(context.getOnWayHighDealOrderDtlPoList())) {
            // 修改订单的场景,需要将原订单撤单
            cancelOriginalOrder(context, order);
            // 认缴金额告警
            checkPaidAmt(context, order);
        }
        // 4.如果有未使用的好臻金额锁定配置,变更配置为已使用
        if (CollectionUtils.isNotEmpty(context.getHzFundAmtLockCfgDtoList())) {
            List<Integer> unUsedCfgIdList = context.getHzFundAmtLockCfgDtoList().stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).map(HzFundAmtLockCfgDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unUsedCfgIdList)) {
                hzFundAmtLockCfgService.hzFundAmtLockCfgUsed(unUsedCfgIdList, order.getOrderPo().getDealNo());
            }
        }
        return order;
    }


    /**
     * 新订单的实缴金额低于原订单实缴金额，且原订单支付状态为支付成功，自动发送实缴金额修改通知邮件
     */
    private void checkPaidAmt(OrderCreateContext context, OrderCreateBean order) {
        HighDealOrderDtlPo highDealOrderDtlPo = context.getOnWayHighDealOrderDtlPoList().get(0);
        List<DealOrderPo> dealOrderPos = dealOrderRepository.queryByDealNoList(Collections.singletonList(highDealOrderDtlPo.getDealNo()));
        if (!PayStatusEnum.PAY_SUC.getCode().equals(dealOrderPos.get(0).getPayStatus())) {
            logger.info("原订单,不是已付款,不需要处理,实缴金额校验,dealNo={}", highDealOrderDtlPo.getDealNo());
            return;
        }
        BigDecimal oldAppAmt = highDealOrderDtlPo.getNetAppAmt();
        BigDecimal newAppAmt = order.getOrderDtlList().get(0).getNetAppAmt();
        if (newAppAmt.compareTo(oldAppAmt) < 0) {
            sendPaidAmtChangeEmail(context, oldAppAmt, newAppAmt);
        } else {
            logger.info("新订单实缴金额大于等于原订单实缴金额,不需要发送邮件,dealNo={}", highDealOrderDtlPo.getDealNo());
        }

    }

    /**
     * 发送实缴金额变更邮件
     *
     * @param context   下单上下文
     * @param oldAppAmt 原实缴金额
     * @param newAppAmt 新订单实缴金额
     */
    private void sendPaidAmtChangeEmail(OrderCreateContext context, BigDecimal oldAppAmt, BigDecimal newAppAmt) {
        try {
            PaidAmtChangeInfoBean paidAmtChangeInfoBean = new PaidAmtChangeInfoBean();
            paidAmtChangeInfoBean.setExcelName("实缴金额修改信息");
            paidAmtChangeInfoBean.setTaTradeDt(context.getTaTradeDt());
            paidAmtChangeInfoBean.setAppDtm(DateUtil.getAppTm());
            paidAmtChangeInfoBean.setTxAcctNo(context.getCustInfo().getCustInfo().getTxAcctNo());
            paidAmtChangeInfoBean.setCustomerName(context.getCustInfo().getCustInfo().getCustName());
            paidAmtChangeInfoBean.setFundCode(context.getHighProductInfoBean().getFundCode());
            paidAmtChangeInfoBean.setFundName(context.getHighProductInfoBean().getFundName());
            paidAmtChangeInfoBean.setNewPaidAmt(newAppAmt);
            paidAmtChangeInfoBean.setOldPaidAmt(oldAppAmt);
            byte[] bytes = buildExcelByData(paidAmtChangeInfoBean);
            // 4.发送邮件
            sendPaidAmtChangeEmail(bytes);
        } catch (IOException e) {
            logger.error("sendPaidAmtChangeEmail,发送实缴金额变更邮件失败,txAcctNo,fundCode={},taTradeDt={},e={}", context.getCustInfo().getCustInfo().getTxAcctNo(), context.getHighProductInfoBean().getFundCode(), context.getTaTradeDt(), e);
        }
    }

    /**
     * 将数据转换为excel数据流
     */
    public byte[] buildExcelByData(PaidAmtChangeInfoBean paidAmtChangeInfoBean) throws IOException {
        String[] excelHeader = {"TA交易日", "申请时间", "客户号", "客户名称", "产品编码", "产品名称", "修改后金额", "修改前金额"};
        String[] ds_titles = {"taTradeDt", "appDtm", "txAcctNo", "customerName", "fundCode", "fundName", "newPaidAmt", "oldPaidAmt"};
        int[] ds_format = {2, 2, 2, 2, 2, 2, 2, 2};
        Map<String, Object> map = new HashMap<>();
        map.put("taTradeDt", paidAmtChangeInfoBean.getTaTradeDt());
        map.put("appDtm", paidAmtChangeInfoBean.getAppDtm());
        map.put("txAcctNo", paidAmtChangeInfoBean.getTxAcctNo());
        map.put("customerName", paidAmtChangeInfoBean.getCustomerName());
        map.put("fundCode", paidAmtChangeInfoBean.getFundCode());
        map.put("fundName", paidAmtChangeInfoBean.getFundName());
        map.put("newPaidAmt", paidAmtChangeInfoBean.getNewPaidAmt());
        map.put("oldPaidAmt", paidAmtChangeInfoBean.getOldPaidAmt());
        return ExcelUtiles.export(paidAmtChangeInfoBean.getExcelName(), "待处理数据", excelHeader, ds_titles, ds_format, null, Collections.singletonList(map));

    }

    /**
     * 发送未匹配股权订单告警邮件
     */
    private void sendPaidAmtChangeEmail(byte[] emailExcel) {
        logger.info("sendPaidAmtChangeEmail-实缴金额修改通知邮件-start");
        //1.构建发送邮件入参
        SendEmailWithAttachmentBean emailBean = new SendEmailWithAttachmentBean();
        emailBean.setBusinessId("60481");
        // 邮件内容
        Map<String, Object> contentMap = new HashMap<>();
        contentMap.put("content", "新订单的实缴金额低于原订单实缴金额，且原订单支付状态为支付成功，请smop查看确认");
        emailBean.setTemplateVar(JSON.toJSONString(contentMap));
        // 标题
        emailBean.setTitle("实缴金额修改通知邮件");
        // 邮件附件
        SendEmailWithAttachmentBean.AttachmentBean attachmentBean = new SendEmailWithAttachmentBean.AttachmentBean();
        attachmentBean.setFile(emailExcel);
        attachmentBean.setDisplayFileName("新订单的实缴金额低于原订单实缴金额信息");
        attachmentBean.setFileFormat(".xls");
        emailBean.setAttachmentList(Collections.singletonList(attachmentBean));
        // 2.发送邮件,需要按照遍历收件人,一个个的发
        for (String email : paidAmtChangeNotifyEmail.split(",")) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(email)) {
                String encryptEmail = encryptSingleOuterService.encryptSingle(email);
                emailBean.setEmail(encryptEmail);
                Integer result = sendEmailWithAttachmentOuterService.execute(emailBean);
                if (result == null || result != 0) {
                    logger.error("sendPaidAmtChangeEmail-实缴金额修改通知邮件,发送邮件出现失败,email={},encryptEmail={}", email, encryptEmail);
                }
            }
        }
    }

    /**
     * 人数/金额额度校验
     */
    private void validatePeopleCountAndAmt(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 1.获取任教金额锁定配置
        List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList = hzFundAmtLockCfgService.queryValidHzFundAmtLockCfg(request.getHbOneNo(), request.getFundCode(), context.getAppointId());
        context.setHzFundAmtLockCfgDtoList(hzFundAmtLockCfgDtoList);
        // 2.人数/金额校验
        validatePeopleAndAmt(request, context);
        // 3.认缴实缴金额校验
        validateSubscribeAndPaidAmt(request, context);
        // 4.交易限额/级差校验
        validateTradeLimit(request, context);
    }

    /**
     * 交易限额校验
     * 首次实缴、且未使用好臻金额锁定及修改配置下单时才需要校验
     */
    private void validateTradeLimit(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        SubscribeAmtInfoDto subscribeAmtInfoDto = context.getSubscribeAmtInfoDto();
        if (YesOrNoEnum.NO.getCode().equals(subscribeAmtInfoDto.getNeedCheckAmt())) {
            logger.info("validateTradeLimit-不需要校验限额,subscribeAmtInfoDto={}", JSON.toJSONString(subscribeAmtInfoDto));
            return;
        }
        HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();
        // 查询交易限额
        HighProductLimitBean highProductLimitInfo = queryHighProductOuterService.getHighProductLimitInfo(highProductInfoBean.getFundCode(), highProductInfoBean.getShareClass(), context.getBusinessCode().getCode(), context.getCustInfo().getCustInfo().getInvstType());
        if (highProductLimitInfo == null || highProductLimitInfo.getMaxPurchaseAmt() == null || highProductLimitInfo.getMinPurchaseAmt() == null) {
            logger.info("validateTradeLimit-交易限额信息查询不到,highProductInfoBean={},invstType={},businessCode={}", JSON.toJSONString(highProductInfoBean), context.getCustInfo().getCustInfo().getInvstType(), context.getBusinessCode().getCode());
            throw new ValidateException(ExceptionCodes.FUND_LIMIT_IS_NULL, MessageSource.getMessageByCode(ExceptionCodes.FUND_LIMIT_IS_NULL));
        }
        // 交易限额校验
        if (highProductLimitInfo.getMaxPurchaseAmt().compareTo(request.getSubsAmt()) < 0) {
            logger.info("validateTradeLimit-认缴金额不高于净金额上限,highProductLimitInfo={},subsAmt={}}", JSON.toJSONString(highProductLimitInfo), request.getSubsAmt());
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "认缴金额不高于净金额上限");
        }
        if (highProductLimitInfo.getMinPurchaseAmt().compareTo(request.getSubsAmt()) > 0) {
            logger.info("validateTradeLimit-认缴金额不低于净金额下限,highProductLimitInfo={},subsAmt={}}", JSON.toJSONString(highProductLimitInfo), request.getSubsAmt());
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "认缴金额不低于净金额下限");
        }

        // 级差校验
        if (highProductInfoBean.getProdAppDiffer() == null || highProductInfoBean.getProdAppDiffer() == 0) {
            logger.info("validateTradeLimit-首次购买极差为空，不校验");
            return;
        }
        // （认缴金额—“净金额下限”）%“级差”，余数为0
        BigDecimal temAppAmt = request.getSubsAmt().subtract(highProductLimitInfo.getMinPurchaseAmt());
        if (temAppAmt.longValue() % highProductInfoBean.getProdAppDiffer() != 0) {
            logger.info("validateTradeLimit-极差校验不通过,subsAmt={},minPurchaseAmt={},temAppAmt={},prodAppDiffer={}", request.getSubsAmt(), highProductLimitInfo.getMinPurchaseAmt(), temAppAmt, highProductInfoBean.getProdAppDiffer());
            throw new ValidateException(ExceptionCodes.HIGH_PROD_APP_DIFFER_VALIDATE_FAILED, MessageSource.getMessageByCode(ExceptionCodes.HIGH_PROD_APP_DIFFER_VALIDATE_FAILED));
        }

    }

    /**
     * 查询认缴信息与购买信息
     *
     * @param querySubscribeAmtParam 查询入参
     * @return 认缴金额信息
     */
    public SubscribeAmtInfoDto buildSubscribeAndBuyAmtInfo(QuerySubscribeAmtParam querySubscribeAmtParam) {
        // 1.产品信息
        HighProductInfoModel highProductBaseModel = highProductService.getHighProductInfo(querySubscribeAmtParam.getFundCode());
        // 2.当前生效的预约日历
        Date queryDate = querySubscribeAmtParam.getQueryDate() != null ? querySubscribeAmtParam.getQueryDate() : new Date();
        ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDateWithDeferPurchaseConfig(querySubscribeAmtParam.getHbOneNo(), querySubscribeAmtParam.getFundCode(), "0", highProductBaseModel.getShareClass(), querySubscribeAmtParam.getDisCode(), queryDate);
        // 3.确认持仓
        QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
        param.setTxAcctNo(querySubscribeAmtParam.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(querySubscribeAmtParam.getFundCode()));
        param.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        param.setIncludeDirect(false);
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(param);
        // 4.查询生效的好臻金额锁定配置
        List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList = new ArrayList<>();
        if (productAppointmentInfoBean != null) {
            logger.info("预约日历非空,查询好臻金额锁定配置");
            hzFundAmtLockCfgDtoList = hzFundAmtLockCfgService.queryValidHzFundAmtLockCfg(querySubscribeAmtParam.getHbOneNo(), querySubscribeAmtParam.getFundCode(), productAppointmentInfoBean.getAppointId());
        }
        // 5.在途订单
        QueryHighDealOrderParam dealOrderParam = new QueryHighDealOrderParam();
        dealOrderParam.setTxAcctNo(querySubscribeAmtParam.getTxAcctNo());
        dealOrderParam.setFundCodeList(Collections.singletonList(querySubscribeAmtParam.getFundCode()));
        dealOrderParam.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        if (productAppointmentInfoBean != null) {
            dealOrderParam.setAppointId(productAppointmentInfoBean.getAppointId());
        }
        List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList = highDealOrderDtlRepository.getOnWayAgentDealDtlList(dealOrderParam);
        // 6.查询预约单信息
        PreBookFullInfoQueryRequest preBookQueryRequest = new PreBookFullInfoQueryRequest();
        preBookQueryRequest.setHboneNo(querySubscribeAmtParam.getHbOneNo());
        preBookQueryRequest.setProdCode(querySubscribeAmtParam.getFundCode());
        List<String> tradeTypeList = new ArrayList<>();
        tradeTypeList.add(PreBookTradeTypeEnum.BUY.getCode());
        tradeTypeList.add(PreBookTradeTypeEnum.APPEND.getCode());
        preBookQueryRequest.setTradeTypeList(tradeTypeList);
        preBookQueryRequest.setPrebookStateList(Collections.singletonList(PreBookStateEnum.CONFIRM.getCode()));
        List<PreBookDetailVO> preBookDetailVOList = queryPreBookListOuterService.queryPreBookByPreRequest(preBookQueryRequest);
        PreBookDetailVO preOrderInfo = null;
        if (CollectionUtils.isNotEmpty(preBookDetailVOList)) {
            List<PreBookDetailVO> unConfirmPreBookList = preBookDetailVOList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getTradestate())).distinct().collect(Collectors.toList());
            preOrderInfo = CollectionUtils.isNotEmpty(unConfirmPreBookList) ? unConfirmPreBookList.get(0) : null;
        }
        // 7.如果分次call,需要添加认缴金额信息
        if (YesOrNoEnum.YES.getCode().equals(highProductBaseModel.getPeDivideCallFlag())) {
            // 7.1.好臻认缴记录
            SubscribeAmtDetailPo subscribeAmtDetailPo = subscribeAmtDetailRepository.getSubscribeAmtDetail(querySubscribeAmtParam.getTxAcctNo(), querySubscribeAmtParam.getFundCode());
            // 7.2.1.认缴金额信息添加
            SubscribeAmtInfoDto subscribeAmtInfoDto = buildSubscribeAmtInfo(onWayHighDealOrderDtlPoList, confirmBalanceBaseInfoList, subscribeAmtDetailPo, hzFundAmtLockCfgDtoList, preOrderInfo);
            // 7.2.2.认缴金额获取
            BigDecimal paidAmt = getPaidAmt(subscribeAmtInfoDto.getSubscribeAmt(), onWayHighDealOrderDtlPoList, confirmBalanceBaseInfoList, hzFundAmtLockCfgDtoList, productAppointmentInfoBean);
            subscribeAmtInfoDto.setPaidAmt(paidAmt);
            subscribeAmtInfoDto.setPeDivideCallFlag(highProductBaseModel.getPeDivideCallFlag());
            return subscribeAmtInfoDto;
        } else {
            // 7.3.购买金额添加
            SubscribeAmtInfoDto subscribeAmtInfoDto = buildBuyAmtInfo(onWayHighDealOrderDtlPoList, hzFundAmtLockCfgDtoList, preOrderInfo);
            subscribeAmtInfoDto.setPeDivideCallFlag(highProductBaseModel.getPeDivideCallFlag());
            return subscribeAmtInfoDto;
        }
    }

    /**
     * 购买金额
     */
    public SubscribeAmtInfoDto buildBuyAmtInfo(List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList, List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList, PreBookDetailVO preOrderInfo) {
        SubscribeAmtInfoDto subscribeAmtInfoDto = new SubscribeAmtInfoDto();
        subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.NO.getCode());
        subscribeAmtInfoDto.setPaidAmtCanUpdate(YesOrNoEnum.NO.getCode());


        List<HzFundAmtLockCfgDto> unUsedCfgList = null;
        List<HzFundAmtLockCfgDto> lockedCfgList = null;
        if (hzFundAmtLockCfgDtoList != null) {
            // 是否有未使用锁定配置
            unUsedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList());
            // 是否有金额锁定=是的配置
            lockedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getSubscribeAmtLock())).collect(Collectors.toList());
        }

        // 1.好臻名单客户正常下单或修改订单时，好臻名单有可用额度，即存在【已使用】=否的好臻名单配置,取好臻名单【总实缴金额】，不支持修改，无需限额校验
        if (CollectionUtils.isNotEmpty(unUsedCfgList)) {
            log.info("buildBuyAmtInfo-存在【已使用】=否的好臻名单配置,取好臻名单【总实缴金额】，不支持修改，无需限额校验,hzFundAmtLockCfgDtoList={}", JSON.toJSONString(hzFundAmtLockCfgDtoList));
            subscribeAmtInfoDto.setBuyAmt(unUsedCfgList.get(0).getTotalPaidAmt());
            subscribeAmtInfoDto.setBuyAmtCanUpdate(YesOrNoEnum.NO.getCode());
            subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.NO.getCode());
            return subscribeAmtInfoDto;
        }
        // 2.满足以下任一条件，取原订单的【总实缴金额】，支持修改，需限额校验
        if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList)) {
            // 2.1.未调整好臻名单额度下修改订单，即有在途，且好臻名单无可用额度且认缴金额未锁定。即不存在【已使用】=否的好臻名单配置，且存在【已使用】=是，且【认缴金额锁定】=否
            if (CollectionUtils.isNotEmpty(hzFundAmtLockCfgDtoList) && CollectionUtils.isEmpty(lockedCfgList)) {
                log.info("buildBuyAmtInfo-有在途，且好臻名单无可用额度且认缴金额未锁定,取原订单的【总实缴金额】，支持修改，需限额校验,hzFundAmtLockCfgDtoList={},onWayHighDealOrderDtlPoList={}", JSON.toJSONString(hzFundAmtLockCfgDtoList), JSON.toJSONString(onWayHighDealOrderDtlPoList));
                subscribeAmtInfoDto.setBuyAmt(onWayHighDealOrderDtlPoList.get(0).getSubsAmt());
                subscribeAmtInfoDto.setBuyAmtCanUpdate(YesOrNoEnum.YES.getCode());
                subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.YES.getCode());
                return subscribeAmtInfoDto;
            }
            // 2.2.非好臻名单客户修改订单，即有在途，且无好臻名单配置；
            if (CollectionUtils.isEmpty(hzFundAmtLockCfgDtoList)) {
                log.info("buildBuyAmtInfo-非好臻名单客户修改订单，即有在途，且无好臻名单配置,取原订单的【总实缴金额】，支持修改，需限额校验,hzFundAmtLockCfgDtoList={},onWayHighDealOrderDtlPoList={}", JSON.toJSONString(hzFundAmtLockCfgDtoList), JSON.toJSONString(onWayHighDealOrderDtlPoList));
                subscribeAmtInfoDto.setBuyAmt(onWayHighDealOrderDtlPoList.get(0).getSubsAmt());
                subscribeAmtInfoDto.setBuyAmtCanUpdate(YesOrNoEnum.YES.getCode());
                subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.YES.getCode());
                return subscribeAmtInfoDto;
            }
        }
        // 3.走预约单的逻辑,有有效预约单就取预约单上,否则就返回空
        if (preOrderInfo != null) {
            log.info("buildBuyAmtInfo-如果有预约单,那么就取预约单上的购买金额，支持修改，需限额校验,preOrderInfo={}", JSON.toJSONString(preOrderInfo));
            // 非分次call,预约单没有认缴金额,只有购买金额
            subscribeAmtInfoDto.setBuyAmt(preOrderInfo.getBuyamt());
            subscribeAmtInfoDto.setBuyAmtCanUpdate(YesOrNoEnum.YES.getCode());
            subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.YES.getCode());
            return subscribeAmtInfoDto;
        }
        log.info("buildBuyAmtInfo-不符合任何条件,购买金额为空，支持修改，需限额校验");
        return subscribeAmtInfoDto;
    }


    /**
     * 场景1：满足以下任一条件，取好臻名单【认缴金额】，不支持修改，无需限额校验
     * a）条件1：好臻名单客户正常下单，即无在途，且好臻名单有可用额度，即存在【已使用】=否的好臻名单配置；注：好臻名单客户前置条件为CRM预约单
     * b）条件2：调整好臻名单额度后修改订单，即有在途，且好臻名单有可用额度，即存在【已使用】=否的好臻名单配置；
     * c）条件3：未调整好臻名单额度下修改订单，即有在途，且好臻名单无可用额度且认缴金额锁定。即不存在【已使用】=否的好臻名单配置，且存在【已使用】=是，且【认缴金额锁定】=是。
     * 场景2：满足以下任一条件，取原订单的【认缴金额】，支持修改，需限额校验
     * a）条件1：未调整好臻名单额度下修改订单，即有在途，且好臻名单无可用额度且认缴金额未锁定。即不存在【已使用】=否的好臻名单配置，且存在【已使用】=是，且【认缴金额锁定】=否；
     * b）条件2：非好臻名单客户修改订单，即有在途，且无好臻名单配置；
     * 场景3：若非好臻名单客户正常下单，即无在途，且无好臻名单配置。则查询用户该产品是否存在有效的CRM实缴预约，即【预约状态】=已确认，【是否已使用】=否，【预约类型】=电子成单 的对应交易类型的产品预约单
     * a）若存在，则获订单关联的预约单的【认缴金额】，并填充至输入框内，支持修改，需限额校验；
     * b）若不存在，则默认置空，支持填写，需限额校验；
     */
    public SubscribeAmtInfoDto buildSubscribeAmtInfo(List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList,
                                                     List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList, SubscribeAmtDetailPo subscribeAmtDetailPo,
                                                     List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList, PreBookDetailVO preOrderInfo) {
        SubscribeAmtInfoDto subscribeAmtInfoDto = new SubscribeAmtInfoDto();
        subscribeAmtInfoDto.setBuyAmtCanUpdate(YesOrNoEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList)) {
            subscribeAmtInfoDto.setIsFirstPay(YesOrNoEnum.NO.getCode());
        } else {
            subscribeAmtInfoDto.setIsFirstPay(YesOrNoEnum.YES.getCode());
        }
        if (subscribeAmtDetailPo != null) {
            subscribeAmtInfoDto.setConfirmSubscribeAmt(subscribeAmtDetailPo.getSubscribeAmt());
            subscribeAmtInfoDto.setConfirmPaidAmt(subscribeAmtDetailPo.getPaidAmt());
        }
        // 1.非首次,认缴就取认缴表中的,不可以修改
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList) && subscribeAmtDetailPo != null) {
            log.info("buildSubscribeAmtInfo-非首次,认缴就取认缴表中的,不可以修改,confirmBalanceBaseInfoList={}", JSON.toJSONString(confirmBalanceBaseInfoList));
            subscribeAmtInfoDto.setSubscribeAmt(subscribeAmtDetailPo.getSubscribeAmt());
            subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.NO.getCode());
            subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.NO.getCode());
            return subscribeAmtInfoDto;
        }
        // 2.逻辑字段处理
        List<HzFundAmtLockCfgDto> unUsedCfgList = null;
        List<HzFundAmtLockCfgDto> lockedCfgList = null;
        if (hzFundAmtLockCfgDtoList != null) {
            // 是否有未使用锁定配置
            unUsedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList());
            // 是否有金额锁定=是的配置
            lockedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getSubscribeAmtLock())).collect(Collectors.toList());
        }

        // 3.有可使用的好臻金额锁定配置/好臻金额锁定已使用+金额锁定配置=锁定,就取配置上的认缴金额,不支持修改，无需限额校验
        if (CollectionUtils.isNotEmpty(unUsedCfgList)) {
            log.info("buildSubscribeAmtInfo-有可使用的好臻金额锁定配置,就取配置上的认缴金额,不支持修改，无需限额校验,unUsedCfgList={}", unUsedCfgList);
            subscribeAmtInfoDto.setSubscribeAmt(unUsedCfgList.get(0).getSubscribeAmt());
            subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.NO.getCode());
            subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.NO.getCode());
            subscribeAmtInfoDto.setPaidAmt(unUsedCfgList.get(0).getTotalPaidAmt());
            subscribeAmtInfoDto.setPaidAmtCanUpdate(YesOrNoEnum.NO.getCode());
            return subscribeAmtInfoDto;
        }
        // 4.有在途,有已使用的锁定金额锁定配置,就取配置上的,不然就取在途订单上的
        if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList)) {
            if (CollectionUtils.isNotEmpty(lockedCfgList)) {
                log.info("buildSubscribeAmtInfo-有在途,好臻金额锁定已使用+金额锁定配置=锁定,就取配置上的认缴金额,不支持修改，无需限额校验,onWayHighDealOrderDtlPoList={},lockedCfgList={}", JSON.toJSONString(onWayHighDealOrderDtlPoList), JSON.toJSONString(lockedCfgList));
                subscribeAmtInfoDto.setSubscribeAmt(lockedCfgList.get(0).getSubscribeAmt());
                subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.NO.getCode());
                subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.NO.getCode());
            } else {
                log.info("buildSubscribeAmtInfo-有在途，支持修改，需限额校验,onWayHighDealOrderDtlPoList={}", JSON.toJSONString(onWayHighDealOrderDtlPoList));
                HighDealOrderDtlPo highDealOrderDtlPo = onWayHighDealOrderDtlPoList.get(0);
                subscribeAmtInfoDto.setSubscribeAmt(highDealOrderDtlPo.getSubsAmt());
                subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.YES.getCode());
                subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.YES.getCode());
            }
            return subscribeAmtInfoDto;
        }
        // 5.如果有预约单,那么就取预约单上
        if (preOrderInfo != null) {
            log.info("buildSubscribeAmtInfo-如果有预约单,那么就取预约单上，支持修改，需限额校验,preOrderInfo={}", JSON.toJSONString(preOrderInfo));
            subscribeAmtInfoDto.setSubscribeAmt(preOrderInfo.getFcclTotalAmt());
            subscribeAmtInfoDto.setSubscribeAmtCanUpdate(YesOrNoEnum.YES.getCode());
            subscribeAmtInfoDto.setNeedCheckAmt(YesOrNoEnum.YES.getCode());
            return subscribeAmtInfoDto;
        }
        log.info("buildSubscribeAmtInfo-不符合任何条件,认缴金额为空，支持修改，需限额校验");
        return subscribeAmtInfoDto;
    }

    /**
     * 获取实缴金额
     */
    public BigDecimal getPaidAmt(BigDecimal subscribeAmt, List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList,
                                 List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList,
                                 List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList, ProductAppointmentInfoBean productAppointmentInfoBean) {
        if (subscribeAmt == null) {
            return null;
        }
        List<HzFundAmtLockCfgDto> unUsedCfgList = null;
        if (hzFundAmtLockCfgDtoList != null) {
            // 是否有未使用锁定配置
            unUsedCfgList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList());
        }
        // 1.若为首次实缴，即【是否首次实缴】=是，且满足以下任一条件，取输入框内的【认缴金额】*【本次实缴比
        if (CollectionUtils.isEmpty(confirmBalanceBaseInfoList) && productAppointmentInfoBean != null && productAppointmentInfoBean.getPayRatio() != null) {
            // 1.1.非好臻名单客户正常下单
            if (CollectionUtils.isEmpty(hzFundAmtLockCfgDtoList)) {
                logger.info("首次,非好臻名单客户正常下单,实缴取认缴*比例,subscribeAmt={},payRatio={}", subscribeAmt, productAppointmentInfoBean.getPayRatio());
                return subscribeAmt.multiply(productAppointmentInfoBean.getPayRatio());
            }
            // 1.2.未调整好臻名单额度下修改订单，即有在途，且好臻名单无可用额度。即不存在【已使用】=否的好臻名单配置
            if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList) && CollectionUtils.isEmpty(unUsedCfgList)) {
                logger.info("首次,有在途，且好臻名单无可用额度,实缴取认缴*比例,subscribeAmt={},payRatio={}", subscribeAmt, productAppointmentInfoBean.getPayRatio());
                return subscribeAmt.multiply(productAppointmentInfoBean.getPayRatio());
            }
        }
        // 2.若为非首次实缴，即【是否首次实缴】=否，且满足以下任一条件，取该客户该产品在中台认缴表内的【认缴金额】*【本次实缴比例】，不支持修改
        if (CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList) && productAppointmentInfoBean != null && productAppointmentInfoBean.getPayRatio() != null) {
            // 2.1.非好臻名单客户正常下单
            if (CollectionUtils.isEmpty(hzFundAmtLockCfgDtoList)) {
                logger.info("非首次,非好臻名单客户正常下单,实缴取认缴*比例,subscribeAmt={},payRatio={}", subscribeAmt, productAppointmentInfoBean.getPayRatio());
                return subscribeAmt.multiply(productAppointmentInfoBean.getPayRatio());
            }
            // 2.2.未调整好臻名单额度下修改订单，既有在途，且好臻名单无可用额度。即不存在【已使用】=否的好臻名单配置；
            // 2.2.未调整好臻名单额度下修改订单，即有在途，且好臻名单无可用额度。即不存在【已使用】=否的好臻名单配置
            if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList) && CollectionUtils.isEmpty(unUsedCfgList)) {
                logger.info("非首次,有在途，且好臻名单无可用额度,实缴取认缴*比例,subscribeAmt={},payRatio={}", subscribeAmt, productAppointmentInfoBean.getPayRatio());
                return subscribeAmt.multiply(productAppointmentInfoBean.getPayRatio());
            }
        }
        // 3.不区分是否首次实缴，若满足以下任一条件，取好臻名单配置的【总实缴金额】，不支持修改
        // 3.1.好臻名单客户正常下单，即无在途，且好臻名单有可用额度。即存在【已使用】=否的好臻名单配置
        if (CollectionUtils.isNotEmpty(unUsedCfgList)) {
            logger.info("好臻名单有可用额度,取好臻名单配置的【总实缴金额】,unUsedCfgList{}", JSON.toJSONString(unUsedCfgList));
            return unUsedCfgList.get(0).getTotalPaidAmt();
        }
        if (productAppointmentInfoBean != null && productAppointmentInfoBean.getPayRatio() != null) {
            logger.info("默认按照认缴乘以比例,subscribeAmt={},payRatio={}", JSON.toJSONString(subscribeAmt), productAppointmentInfoBean.getPayRatio());
            return subscribeAmt.multiply(productAppointmentInfoBean.getPayRatio());
        }
        logger.info("不符合任何场景,没有实缴金额,subscribeAmt{}", JSON.toJSONString(subscribeAmt));
        return null;
    }

    /**
     * 认缴实缴金额校验
     * 1.分次call产品
     * 1.1.有可用的好臻金额锁定及修改配置，实缴金额必须与好臻金额锁定及修改配置上的汇总实缴金额一致。
     * 1.2.没有可用的好臻金额锁定及修改配置，实缴金额必须与订单上的认缴金额*此产品此预约期的缴款比例一致。
     * 1.3.当前实缴+历史实缴<=认缴金额，否则不能下单成功。
     * 1.3.1.历史实缴=该客户该产品认申购确认成功的订单的确认金额+非交易过户转入订单的确认金额（订单关联的柜台订单上的“过户份额对应的认缴金额”）-非交易过户转出的确认金额（订单关联的柜台订单上的“过户份额对应的认缴金额”），此字段可以记录到客户认缴表里。
     * 2.非分次call产品
     * 2.1.实缴金额必须等于认缴金额。
     */
    private void validateSubscribeAndPaidAmt(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 好臻认缴记录
        SubscribeAmtDetailPo subscribeAmtDetailPo = subscribeAmtDetailRepository.getSubscribeAmtDetail(request.getTxAcctNo(), request.getFundCode());
        context.setSubscribeAmtDetailPo(subscribeAmtDetailPo);
        // 1.认缴金额校验
        QuerySubscribeAmtParam querySubscribeAmtParam = new QuerySubscribeAmtParam();
        querySubscribeAmtParam.setHbOneNo(request.getHbOneNo());
        querySubscribeAmtParam.setTxAcctNo(request.getTxAcctNo());
        querySubscribeAmtParam.setFundCode(request.getFundCode());
        querySubscribeAmtParam.setDisCode(request.getDisCode());
        querySubscribeAmtParam.setQueryDate(context.getAppDtm());
        SubscribeAmtInfoDto subscribeAmtInfoDto = buildSubscribeAndBuyAmtInfo(querySubscribeAmtParam);
        context.setSubscribeAmtInfoDto(subscribeAmtInfoDto);
        if (subscribeAmtInfoDto.getSubscribeAmt() != null && YesOrNoEnum.NO.getCode().equals(subscribeAmtInfoDto.getSubscribeAmtCanUpdate())) {
            if (request.getSubsAmt().compareTo(subscribeAmtInfoDto.getSubscribeAmt()) != 0) {
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "认缴金额不正确");
            }
        }
        // 2.实缴金额校验
        if (subscribeAmtInfoDto.getPaidAmt() != null && YesOrNoEnum.NO.getCode().equals(subscribeAmtInfoDto.getSubscribeAmtCanUpdate())) {
            if (context.getBuyBean().getNetAppAmt().compareTo(subscribeAmtInfoDto.getPaidAmt()) != 0) {
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "实缴金额校验不通过");
            }
        }
        // 3.下单金额校验(非分次call才会有值)
        // 存在【已使用】=否的好臻名单配置,取好臻名单【总实缴金额】，不支持修改
        if (subscribeAmtInfoDto.getBuyAmt() != null && YesOrNoEnum.NO.getCode().equals(subscribeAmtInfoDto.getBuyAmtCanUpdate())) {
            if (context.getBuyBean().getNetAppAmt().compareTo(subscribeAmtInfoDto.getBuyAmt()) != 0) {
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "存在未使用的好臻金额配置配置,取好臻名配置,总实缴金额,不支持修改");
            }
        }
        if (YesOrNoEnum.YES.getCode().equals(context.getHighProductInfoBean().getPeDivideCallFlag())) {
            checkPeDivideCall(request, context, subscribeAmtDetailPo);

        } else {
            // 5.非分次call产品,实缴金额必须等于认缴金额
            if (context.getBuyBean().getNetAppAmt().compareTo(request.getSubsAmt()) != 0) {
                logger.info("validatePaidAmt-非分次call产品,实缴金额必须不等于认缴金额,subsAmt={},netAppAmt={}", request.getSubsAmt(), context.getBuyBean().getNetAppAmt());
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "非分次call产品,实缴金额必须等于认缴金额");
            }
        }

    }

    /**
     * 分次call订单校验
     */
    private void checkPeDivideCall(BaseMergeSubsOrPurRequest request, OrderCreateContext context, SubscribeAmtDetailPo subscribeAmtDetailPo) {
        // 4.分次call产品
        List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList = context.getHzFundAmtLockCfgDtoList();
        List<HzFundAmtLockCfgDto> unUsedHzFundAmtLockCfgDtoList = hzFundAmtLockCfgDtoList.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getUsed())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unUsedHzFundAmtLockCfgDtoList)) {
            // 4.1.有可用的好臻金额锁定及修改配置,实缴金额必须与好臻金额锁定及修改配置上的汇总实缴金额一致
            BigDecimal totalPaidAmt = unUsedHzFundAmtLockCfgDtoList.get(0).getTotalPaidAmt();
            if (totalPaidAmt.compareTo(context.getBuyBean().getNetAppAmt()) != 0) {
                logger.info("validatePaidAmt-有可用的好臻金额锁定及修改配置，实缴金额必须与好臻金额锁定及修改配置上的汇总实缴金额不一致,totalPaidAmt={},netAppAmt={}", totalPaidAmt, context.getBuyBean().getNetAppAmt());
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "有可用的好臻金额锁定及修改配置，实缴金额必须与好臻金额锁定及修改配置上的汇总实缴金额不一致");
            }
        } else {
            ProductAppointmentInfoBean productAppointmentInfoBean = context.getProductAppointmentInfoBean();
            if (productAppointmentInfoBean == null) {
                logger.info("validatePaidAmt-分次call产品,没有预约日历,fundCode={}", request.getFundCode());
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "分次call产品,没有预约日历");
            }
            // 4.2.没有可用的好臻金额锁定及修改配置,实缴金额必须与订单上的认缴金额*此产品此预约期的缴款比例一致,因为前端有小数位截断,这里直接限定差额不能超过1
            BigDecimal payRatio = productAppointmentInfoBean.getPayRatio();
            if (payRatio.multiply(request.getSubsAmt()).subtract(context.getBuyBean().getNetAppAmt()).compareTo(BigDecimal.valueOf(-0.01)) <= 0 || payRatio.multiply(request.getSubsAmt()).subtract(context.getBuyBean().getNetAppAmt()).compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                logger.info("validatePaidAmt-没有可用好臻金额锁定配置,实缴金额不等于认缴乘缴款比例,fundCode={},payRatio={},subsAmt={},netAppAmt={},paidAmt={}", request.getFundCode(), payRatio, request.getSubsAmt(), context.getBuyBean().getNetAppAmt(), payRatio.multiply(request.getSubsAmt()));
                throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "实缴金额不等于认缴乘缴款比例");
            }
        }
        // 4.3.当前实缴+历史实缴<=认缴金额，否则不能下单成功
        BigDecimal paidAmt = BigDecimal.ZERO;
        if (subscribeAmtDetailPo != null) {
            paidAmt = subscribeAmtDetailPo.getPaidAmt();
        }
        BigDecimal totalPaidAmt = context.getBuyBean().getNetAppAmt().add(paidAmt);
        if (totalPaidAmt.compareTo(request.getSubsAmt()) > 0) {
            logger.info("validatePaidAmt-当前实缴+历史实缴>认缴金额,fundCode={},totalPaidAmt={},subsAmt={}", request.getFundCode(), totalPaidAmt, request.getSubsAmt());
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "当前实缴+历史实缴>认缴金额");
        }
    }


    /**
     * 人数/金额校验
     */
    private void validatePeopleAndAmt(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 缓存: 高端产品已使用额度信息
        HighQuotaBean highQuotaBean = highProductQuotaService.getQuotaInfo(request.getFundCode());
        // 认缴=锁定的好臻金额锁定配置
        List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList = context.getHzFundAmtLockCfgDtoList();
        List<HzFundAmtLockCfgDto> lockedHzFundAmtLockCfgDtoList = hzFundAmtLockCfgDtoList.stream().filter(x -> x.getSubscribeAmtLock().equals(YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        // 锁定人数
        int lockCount = 0;
        // 锁定金额
        BigDecimal lockAmount = BigDecimal.ZERO;
        HighProductLockInfoBean highProductLockInfoBean = queryHighProductOuterService.getHighProductLockInfo(request.getFundCode());
        if (highProductLockInfoBean != null) {
            lockCount = highProductLockInfoBean.getLockCount() != null ? highProductLockInfoBean.getLockCount() : lockCount;
            lockAmount = highProductLockInfoBean.getLockAmount() != null ? highProductLockInfoBean.getLockAmount() : lockAmount;
        }
        // 1.校验人数
        validatePeopleCount(context, lockCount, highQuotaBean, lockedHzFundAmtLockCfgDtoList);
        // 2.校验金额
        validateAmt(request, context, lockAmount, highQuotaBean, lockedHzFundAmtLockCfgDtoList);
    }

    /**
     * 校验金额
     * 有认缴金额锁定的好臻金额锁定及修改配置，
     * 客户无持仓，
     * 首次购买（新增订单），
     * 校验认缴金额是否满足锁定金额、总金额。
     * 认缴金额必须等于好臻金额锁定及修改配置上的认缴金额。
     * 非首次购买（修改订单：有在途，修改订单的场景），
     * 用锁定金额、总金额，扣减在途的认缴金额后再校验当前认缴金额。
     * 认缴金额必须等于好臻金额锁定及修改配置上的认缴金额。
     * 客户有持仓，认缴金额必须等于中台记录的该客户的认缴金额。
     * 没有认缴金额锁定的好臻金额锁定及修改配置，
     * 客户无持仓，
     * 首次购买（新增订单），
     * 校验认缴金额是否满足非锁定金额、总金额。
     * 如果有可用的好臻金额锁定及修改配置，认缴金额必须等于好臻金额锁定及修改配置上的认缴金额。
     * 非首次购买（修改订单：有在途，修改订单的场景），
     * 用非锁定金额、总金额，扣减在途认缴金额后再校验当前认缴金额。
     * 如果有可用的好臻金额锁定及修改配置，认缴金额必须等于好臻金额锁定及修改配置上的认缴金额。
     * 客户有持仓，认缴金额必须等于必须等于中台记录的该客户的认缴金额。
     */
    private void validateAmt(BaseMergeSubsOrPurRequest request, OrderCreateContext context,
                             BigDecimal lockAmount, HighQuotaBean highQuotaBean,
                             List<HzFundAmtLockCfgDto> lockedHzFundAmtLockCfgDtoList) {
        // 剩余认缴金额校验
        BigDecimal amountLimit = context.getHighProductInfoBean().getIpoAmountLimit();
        // 没有确认持仓
        if (CollectionUtils.isEmpty(context.getConfirmBalanceBaseInfoList())) {
            // 没有锁定的认缴金额配置
            BigDecimal onWaySubsAmt = BigDecimal.ZERO;
            // 有在途,在途下单就是修改场景,需要将之前订单的认缴加上,在计算剩余额度
            if (CollectionUtils.isNotEmpty(context.getOnWayHighDealOrderDtlPoList()) && YesOrNoEnum.YES.getCode().equals(context.getOriginalOrderPayed())) {
                onWaySubsAmt = context.getOnWayHighDealOrderDtlPoList().get(0).getSubsAmt();
            }
            if (CollectionUtils.isEmpty(lockedHzFundAmtLockCfgDtoList)) {
                BigDecimal leftUnLockAmount = amountLimit.subtract(lockAmount).subtract(highQuotaBean.getUnLockUsedAmount()).add(onWaySubsAmt).subtract(request.getSubsAmt());
                logger.info("validatePeopleAndAmt-剩余认缴金额校验,没有锁定的认缴金额配置,首单,leftLockAmount:{}", leftUnLockAmount);
                if (leftUnLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_UNLOCKAMOUNT_NOT_ENOUGH, MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_UNLOCKAMOUNT_NOT_ENOUGH));
                }
            } else {
                // 有锁定的认缴金额配置
                // 有在途,在途下单就是修改场景,需要将之前订单的认缴加上,在计算剩余额度
                BigDecimal leftLockAmount = lockAmount.subtract(highQuotaBean.getLockUsedAmount()).add(onWaySubsAmt).subtract(request.getSubsAmt());
                logger.info("validatePeopleAndAmt-剩余认缴金额校验,有锁定的认缴金额配置,首单,leftLockAmount:{}", leftLockAmount);
                if (leftLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_LOCKAMOUNT_NOT_ENOUGH, MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_LOCKAMOUNT_NOT_ENOUGH));
                }
            }
            BigDecimal leftTotalAmount = amountLimit.subtract(highQuotaBean.getLockUsedAmount()).subtract(highQuotaBean.getUnLockUsedAmount()).add(onWaySubsAmt).subtract(request.getSubsAmt());
            logger.info("validatePeopleAndAmt-剩余总人数,leftTotalAmount:{}", leftTotalAmount);
            if (leftTotalAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_TOTALAMOUNT_NOT_ENOUGH,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_TOTALAMOUNT_NOT_ENOUGH));
            }
        }

    }

    /**
     * 校验人数额度
     * 有在途+持仓,不校验人数额度;有锁定认缴配置,校验锁定人数,总人数;没有锁定认缴配置就校验非锁定,总人数
     */
    public void validatePeopleCount(OrderCreateContext context, int lockCount, HighQuotaBean highQuotaBean, List<HzFundAmtLockCfgDto> lockedHzFundAmtLockCfgDtoList) {
        if (CollectionUtils.isEmpty(context.getConfirmBalanceBaseInfoList()) && CollectionUtils.isEmpty(context.getOnWayHighDealOrderDtlPoList())) {
            // 1.校验总人数
            int peopleLimit = context.getHighProductInfoBean().getIpoPeopleLimit().intValue();
            if (peopleLimit <= 0) {
                throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL, MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_TOTAL_PARAM_IS_NULL));
            }
            // 2.客户配置的合伙人数(查不到给默认值1)
            Integer partnersNumber = queryHighProductOuterService.getHighPartnersNumber(context.getCustInfo().getCustInfo().getTxAcctNo());
            partnersNumber = null != partnersNumber ? partnersNumber : 1;
            // 3.校验剩余使用人数额度
            if (CollectionUtils.isNotEmpty(lockedHzFundAmtLockCfgDtoList)) {
                // 3.1.有锁定认缴配置,校验锁定人数
                int leftLockCount = lockCount - highQuotaBean.getLockUsedCount() - partnersNumber;
                if (leftLockCount < 0) {
                    throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_LOCKCOUNT_NOT_ENOUGH,
                            MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_LOCKCOUNT_NOT_ENOUGH));
                }

            } else {
                // 3.2.没有锁定认缴配置就校验非锁定
                int leftUnLockCount = peopleLimit - lockCount - highQuotaBean.getUnLockUsedCount() + (highQuotaBean.getLockExitCount() * -1) - partnersNumber;
                if (leftUnLockCount < 0) {
                    logger.info("validatePeopleAndAmt-未使用非锁定人数不足,leftUnLockCount={}", leftUnLockCount);
                    throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_UNLOCKCOUNT_NOT_ENOUGH,
                            MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_UNLOCKCOUNT_NOT_ENOUGH));
                }
            }
            // 4.校验剩余总额度人数数量
            int leftTotalCount = peopleLimit - highQuotaBean.getLockUsedCount() - highQuotaBean.getUnLockUsedCount() + (highQuotaBean.getLockExitCount() * -1);
            leftTotalCount = leftTotalCount - partnersNumber;
            logger.info("validatePeopleAndAmt-剩余总额度人数数量,leftTotalCount:{}", leftTotalCount);
            if (leftTotalCount < 0) {
                throw new ValidateException(ExceptionCodes.HIGH_PRODUCT_TOTALCOUNT_NOT_ENOUGH,
                        MessageSource.getMessageByCode(ExceptionCodes.HIGH_PRODUCT_TOTALCOUNT_NOT_ENOUGH));
            }
        }
    }

    /**
     * 持仓校验
     * 如果是非分次call的好臻股权产品，客户已有持仓，不允许下单
     */
    private void validateConfirmBalance(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
        param.setTxAcctNo(request.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(request.getFundCode()));
        param.setDisCodeList(Collections.singletonList(request.getDisCode()));
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(param);
        // 上下文中加入确认持仓信息
        context.setConfirmBalanceBaseInfoList(confirmBalanceBaseInfoList);
        HighProductInfoBean highProductInfoBean = context.getHighProductInfoBean();
        if (!YesOrNoEnum.YES.getCode().equals(highProductInfoBean.getPeDivideCallFlag()) && CollectionUtils.isNotEmpty(confirmBalanceBaseInfoList)) {
            logger.info("HzCreateOrderCheckLogicProcess-validateConfirmBalance,非分次call的好臻股权产品，客户已有持仓，不允许下单,confirmBalanceBaseInfoList={}", JSON.toJSONString(confirmBalanceBaseInfoList));
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "非分次call的好臻股权产品，客户已有持仓，不允许下单");
        }
    }

    /**
     * 好臻下单校验年龄限制
     */
    private void validAgeLimit(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        CustomerInfoCommand customerInfoCommand = null;
        if (context.getCustInfo() != null && context.getCustInfo().getCustInfo() != null) {
            customerInfoCommand = new CustomerInfoCommand();
            BeanUtils.copyProperties(context.getCustInfo().getCustInfo(), customerInfoCommand);
            customerInfoCommand.setFirstBuyFlag(context.getBuyBean().getFirstBuyFlag());
        }

        boolean validAge = hzFundBuyStatusLogicService.validAgeLimit(context.getHighProductInfoBean(), request.getTxChannel(), customerInfoCommand);
        if (!validAge) {
            logger.info("HzCreateOrderCheckLogicProcess-validAgeLimit,customerInfoCommand={},fundCode={}", JSON.toJSONString(customerInfoCommand), request.getFundCode());
            throw new ValidateException(ExceptionCodes.APPLY_FAILED_ORDER_CENTER_FAILED, "年龄范围校验不通过");
        }
    }

    /**
     * 校验在途订单
     * 该客户该产品该预约期内如果有申请成功的认申购订单，订单必须是未上报。否则不允许下单。
     *
     * @param request 下单入参
     * @param context 下单上下文
     */
    private void validateOnWayOrder(BaseMergeSubsOrPurRequest request, OrderCreateContext context) {
        // 1.是否有在途
        QueryHighDealOrderParam param = new QueryHighDealOrderParam();
        param.setTxAcctNo(request.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(request.getFundCode()));
        param.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        param.setAppointId(context.getAppointId());
        List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList = highDealOrderDtlRepository.getOnWayAgentDealDtlList(param);
        context.setOnWayHighDealOrderDtlPoList(onWayHighDealOrderDtlPoList);
        if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList)) {
            // 查询订单
            DealOrderPo dealOrderPo = dealOrderRepository.selectByDealNo(context.getOnWayHighDealOrderDtlPoList().get(0).getDealNo());
            context.setOriginalOrderPayed(PayStatusEnum.PAY_SUC.getCode().equals(dealOrderPo.getPayStatus()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        } else {
            context.setOriginalOrderPayed(YesOrNoEnum.NO.getCode());

        }


        // 2.查询在途,申请成功,必须是未上报的订单
        List<HighDealOrderDtlPo> notifyDealList = onWayHighDealOrderDtlPoList.stream().filter(x -> NotifySubmitFlagEnum.HAS_BEEN_NOTIFY.getCode().equals(x.getNotifySubmitFlag())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notifyDealList)) {
            logger.info("HzCreateOrderCheckLogicProcess-validateOnWayOrder,该客户该产品该预约期内如果有申请成功的认申购订单，订单必须是未上报,否则不允许下单,notUnNotifyDealList={}", JSON.toJSONString(notifyDealList));
            throw new ValidateException(ExceptionCodes.QUERY_CUST_INFO_FAILED, "有非未上报的,同预约日历的在途订单");
        }
    }


    @Override
    public boolean isThisDisCode(String disCode) {
        if (StringUtils.isBlank(disCode)) {
            return false;
        }
        return DisCodeEnum.HZ.getCode().equals(disCode);
    }
}
