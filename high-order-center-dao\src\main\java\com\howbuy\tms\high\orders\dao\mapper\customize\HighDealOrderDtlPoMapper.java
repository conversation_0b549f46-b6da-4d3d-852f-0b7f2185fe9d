package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.howbuy.tms.high.orders.dao.mapper.HighDealOrderDtlPoAutoMapper;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.vo.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * 去O
 */
public interface HighDealOrderDtlPoMapper extends HighDealOrderDtlPoAutoMapper {
    
    /**
     * countSumBuyAmt:(查询TA下, 用户总购买金额)
     * 
     * @param txAcctNo
     * @param taTradeDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年4月5日 下午4:52:43
     */
    BigDecimal countSumBuyAmt(@Param("txAcctNo") String txAcctNo, @Param("taTradeDt") String taTradeDt, @Param("fundCode") String fundCode);
    
    /**
     * countSumRedeemVol:(查询TA下, 用户总赎回份额)
     * 
     * @param txAcctNo
     * @param taTradeDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年4月10日 下午4:17:12
     */
    BigDecimal countSumRedeemVol(@Param("txAcctNo") String txAcctNo, @Param("taTradeDt") String taTradeDt, @Param("fundCode") String fundCode);

    /**
     * queryNotCompleteOrderDtlForModifyDiv:(查询修改分红方式未完成的订单明细)
     * 
     * @param txAcctNo
     * @param fundCode
     * @param fundShareClass
     * @return
     * <AUTHOR>
     * @date 2017年3月27日 下午7:04:45
     */
    Integer queryNotCompleteOrderDtlForModifyDiv(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("fundShareClass") String fundShareClass);

    /**
     * updateCancelOrder:更新撤单数据
     * 
     * @param record
     * @return
     * <AUTHOR>
     * @date 2016-10-10 下午11:36:45
     */
    int updateCancelOrder(Map<String, Object> record);
    
    /**
     * selectHighDealOrderDtlByDealNo:(根据订单号查询订单信息)
     * 
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2017年4月11日 下午3:31:00
     */
    QueryHighDealOrderDtlVo selectHighDealOrderDtlByDealNo(@Param("dealNo") String dealNo);

    /**
     * 根据订主单号查询合并上报订单列表
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.QueryHighDealOrderDtlVo>
     * @author: huaqiang.liu
     * @date: 2021/3/12 15:45
     * @since JDK 1.8
     */
    List<QueryHighDealOrderDtlVo> selectMergeSubmitOrderDtlByMainDealNo(@Param("mainDealNo") String mainDealNo);

    /**
     * 
     * selectTotalSalesAndPlaces:查询产品累计销售金额和累计购买人数
     * 
     * @param fundCode
     *            基金代码
     * @param disCode
     *            分销机构号（可空）
     * @param startDt
     *            开始TA工作日
     * @param endDt
     *            截止TA工作日
     * @param channelCode
     *            渠道代码（可空）
     * @param txAcctNo
     *            交易账号（可空）
     * @return
     * @return ProductTotalSalesVo
     * <AUTHOR>
     * @date 2017年4月6日 下午7:44:51
     */
    ProductTotalSalesVo selectTotalSalesAndPlaces(@Param("fundCode") String fundCode, @Param("disCode") String disCode, @Param("startDt") String startDt,
            @Param("endDt") String endDt, @Param("channelCode") String channelCode, @Param("txAcctNo") String txAcctNo);
    
    /**
     * selectUsedAppointmentDealNo:(查询成功使用的预约订单号)
     * 
     * @param appointmentDealNo
     * @return
     * <AUTHOR>
     * @date 2017年7月5日 下午3:53:21
     */
    int selectUsedAppointmentDealNo(@Param("appointmentDealNo") String appointmentDealNo);
    
    /**
     * selectLastWithPaySuccessLimitType:(查询用户某个产品最近一笔成功支付的订单的限额类型)
     * 
     * @param txAcctNo
     * @param disCode
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年7月6日 下午5:09:36
     */
    String selectLastWithPaySuccessLimitType(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("fundCode") String fundCode);

    /**
     * 
     * countIntransitTrade:查询在途交易
     * 
     * @param txAcctNo
     * @param cpAcctNo
     * @param disCode
     * @param fundCodes
     * @param protocolNo
     * @return
     * <AUTHOR>
     * @date 2018年5月9日 上午10:54:42
     */
    Integer countIntransitTrade(@Param("txAcctNo") String txAcctNo, @Param("cpAcctNo") String cpAcctNo, @Param("disCode") String disCode,
            @Param("list") List<String> fundCodes, @Param("protocolNo") String protocolNo);
    
    /**
     * 
     * countTransferOutIntransitTrade:查询份额迁移转入资金账号转出的在途交易
     * 
     * @param mBusiCode
     * @param txAcctNo
     * @param cpAcctNo
     * @param disCode
     * @param protocolNo
     * @return
     * <AUTHOR>
     * @date 2018年5月9日 上午10:54:42
     */
    Integer countTransferOutIntransitTrade(@Param("mBusiCode") String mBusiCode, @Param("txAcctNo") String txAcctNo, @Param("cpAcctNo") String cpAcctNo, 
    		@Param("disCode") String disCode, @Param("protocolNo") String protocolNo);

    /**
     * 获取股权产品(代销)回款金额
     *
     * @param disCodeList
     * @param txAcctNo
     * @param productCode
     * @return
     */
    BigDecimal countDxGqhkAmt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * 获取股权产品(crm)回款金额
     *
     * @param hbOneNo
     * @param productCode
     * @return
     */
    BigDecimal countCrmGqhkAmt(@Param("hbOneNo") String hbOneNo, @Param("productCode") String productCode);
    /**
     * 
     * selectDtlByDealNo:根据订单号查询明细
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2019年3月5日 下午5:06:27
     */
    HighDealOrderDtlPo selectDtlByDealNo(@Param("dealNo") String dealNo);

    List<HighDealOrderDtlPo> selectDtlCheck(@Param("fundCodes") List<String> fundCodes,
                                            @Param("submitTaDts") List<String> submitTaDts,
                                            @Param("mBusiCode") String mBusiCode);

    List<HighDealOrderDtlPo> selectDtlForCheck(@Param("fundCodes") List<String> fundCodes,
                                            @Param("submitTaDts") List<String> submitTaDts,
                                            @Param("mBusiCodeList") List<String> mBusiCodeList,
                                            @Param("fundTypeList") List<String> fundTypeList);
    /**
     * 
     * selectDtlForEsByDealNo:查询交易详情供ES使用
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2019年4月9日 上午10:41:07
     */
    HighDealOrderDtlForEsVo selectDtlForEsByDealNo(@Param("dealNo") String dealNo);

    /**
     * 根据主订单号查询合并上报单交易详情供ES使用
     * @param mainDealNo
     * @return com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlForEsVo
     * @author: huaqiang.liu
     * @date: 2021/3/12 14:30
     * @since JDK 1.8
     */
    HighDealOrderDtlForEsVo selectMargeOrderDtlForEsByMainDealNo(@Param("mainDealNo") String mainDealNo);
    /**
     * 
     * selectLatestAckDealDtl:查询产品最近确认订单
     * @param disCodeList
     * @param txAcctNo
     * @param fundCodes
     * @return
     * <AUTHOR>
     * @date 2019年6月4日 下午7:20:45
     */
    List<HighDealOrderDtlLatestAckVo> selectAckDealDtl(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("list") Set<String> fundCodes);

    List<HighDealOrderDtlPo> selectRepurchaseDeals(@Param("repurchaseProtocolNo") String repurchaseProtocolNo);

    List<HighDealOrderDtlPo> selectRepurchaseDealsByNos(@Param("txAcctNo") String txAcctNo,
                                                        @Param("repurchaseProtocolNoList") List<String> repurchaseProtocolNoList);

    /**
     * 查询赎回在途资产列表
     * @param disCodeList
     * @param txAcctNo
     * @return java.util.List<com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2020/10/29 19:10
     * @since JDK 1.8
     */
    List<HighDealOrderDtlPo> selectRedeemOnWayVolList(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo);

    /**
     * 统计指定日期之后（含）的分红数量
     * @param txAcctNo
     * @param fundCode
     * @param ackDt
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/3/23 19:25
     * @since JDK 1.8
     */
    int countDivOrderNumAfterDt(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("ackDt") String ackDt);

    List<HighDealOrderDtlPo> queryDivOrderNumAfterDt(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("ackDt") String ackDt);
    /**
     * 查询代销第一笔交易
     * @param txAcctNo
     * @param fundCode
     * @return
     */
    HighDealOrderDtlPo getFirstAckInfoForConsignment(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);
    /**
     * 查询代销最新一笔交易
     * @param txAcctNo
     * @param fundCode
     * @return
     */
    HighDealOrderDtlPo getLastAckInfoForConsignment(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);

    /**
     * 获取储蓄罐冻结支付成功的在途金额
     * @param productCodeList
     * @param txAcctNo
     * @return
     */
    BigDecimal getCxgDjZt(@Param("productCodeList")List<String> productCodeList, @Param("txAcctNo")String txAcctNo);


    /**
     * 查询持仓订单信息
     * @param txAcctNo 账号
     * @param fundCodeList 产品编码
     * @param disCodeList  分销机构
     * @return 订单信息
     */
    List<BalanceOrderVo> selectBalanceConsignmentOrderVo(@Param("txAcctNo") String txAcctNo,
                                                         @Param("fundCodeList") List<String> fundCodeList,
                                                         @Param("disCodeList") List<String> disCodeList);

    HighDealOrderDtlPo queryFirstTradeConfirm(@Param("txAcctNo")String txAcctNo,@Param("productCode") String productCode);

    /**
     * 查询在途订单信息,包括待付款
     * @param param 查询参数
     * @return 订单信息
     */
    List<HighDealOrderDtlPo> getOnWayAgentDealDtlList(@Param("param")QueryDealParamVo param);

    int updateByDealDtlNo(HighDealOrderDtlPo highDealOrderDtlPo);

    HighDealOrderDtlPo selectByDealDtlNo(@Param("dealDtlNo")String dealDtlNo);

    List<HighDealOrderDtlPo> selectBySubmitDtOrAckDt(@Param("txAcctNo")String txAcctNo,@Param("hbOneNo") String hbOneNo, @Param("fundCodeList")List<String> fundCodeList, @Param("startDt")String submitOrAckStartDt, @Param("endDt")String submitOrAckEndDt);

    /**
     * @description: 查询基金确认金额与确认份额
     * @param fundCodeList 基金代码列表
     * @param ackStartDt 确认开始日期
     * @param ackEndDt 确认结束日期
     * @param txAckFlagList 确认状态列表
     * @return List<FundAckVolAndAmtVo> 基金确认金额与确认份额列表
     * @author: hongdong.xie
     * @date: 2025/3/20 14:30
     * @since JDK 1.8
     */
    List<FundAckVolAndAmtVo> selectFundAckVolAndAmtInfo(@Param("fundCodeList") List<String> fundCodeList,
                                                        @Param("ackStartDt") String ackStartDt,
                                                        @Param("ackEndDt") String ackEndDt,
                                                        @Param("txAckFlagList") List<String> txAckFlagList,
                                                        @Param("mBusiCodeList") List<String> mBusiCodeList);
}
