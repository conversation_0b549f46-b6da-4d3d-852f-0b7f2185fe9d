<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.howbuy.tms</groupId>
    <artifactId>asset-client</artifactId>
    <version>3.16.21-RELEASE</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.8</lombok.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.2.15.RELEASE</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.15.RELEASE</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>